STAGE=local

DATABASE_NAME=holmatro_cp_db
DATABASE_PASSWORD=Holmatro_pw
DATABASE_USER=root
MYSQL_ARCH=mysql:8

TWEAKWISE_INSTANCE_KEY=
TWEAKWISE_TOKEN=

UVICORN_PORT=8000

SENTRY_URL_INCL_TOKEN=
SENTRY_ENV=

B2C_TENANT_ID=32fc4a30-cf52-45e5-b6bd-d5f5d43216e0

HOLMATRO_ASSETS_BUCKET_NAME=harborn-acc-holmatro-product-import-bucket

AWS_ACCESS_KEY_ID='testKeysID'
AWS_SECRET_ACCESS_KEY='testKeys'
AWS_REGION="eu-west-1"

PRIVATE_PYPI_USER=harborn
PRIVATE_PYPI_SECRET=

MY_HM_SUBSCRIPTION_TOKEN=
JWT_ENCODING_KEY=

SSM_HOLMATRO_PORTAL_API_KEY_NAME=

AZURE_API_URL="localhost:8000"
AZURE_API_KEY="test"

RABBITMQ_DEFAULT_USER=guest
RABBITMQ_DEFAULT_PASS=guest
RABBITMQ_BROKER_URL="amqp://guest:guest@rabbitmq:5672/"

SYNC_FORCE_PROPOSITION_ID=

REQUEST_TIMEOUT_SECONDS="30"

OPENSEARCH_HOST="opensearch"
OPENSEARCH_PORT=9200
OPENSEARCH_USERNAME=""
OPENSEARCH_PASSWORD=""
OPENSEARCH_USE_SSL="false"
OPENSEARCH_INDEX_NAME="holmatro-customer-portal"

SERVICE_NAME="holmatro-customer-portal-dev"
ENABLE_OTEL=false

IS_HOLMATRO_CATALOG=true
