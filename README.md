
# Holmatro Customer Portal

This project, holmatro_customer_portal, is an interface for interacting with the Holmatro Customer Portal's services and databases.

## With docker

### Build and Use

Run `cp .env.dist .env` and fill the required variables.

Build and Start Services
To build and start the services defined in the docker-compose.yml file, navigate to the project root directory in your terminal and run the following command: `docker-compose up`. To force a rebuild use the `--build` argument.

Once the services are up run the migrations with `docker compose exec app poetry run alembic upgrade head`.

### phpMyAdmin
phpMyAdmin is available at http://localhost:8080 to facilitate querying and exploring the local database. 

Upon first login an error **authentication method unknown** may arrise, this can be solved by following these steps to update the MYSQL server:

- Login to MYSQL: `docker-compose exec mysql-service bash` & `mysql -u root -p`
- run `ALTER USER 'root'@'%' IDENTIFIED WITH mysql_native_password
BY 'password';` (replace 'password' with `DATABASE_PASSWORD` from your env)


## Without docker

### Python
Install Python 3.12 (supported AWS version) using [pyenv](https://github.com/pyenv/pyenv).
```
pyenv install --skip-existing $(pyenv install --list | grep '^\s*3\.12' | grep -v '\-dev$' | sort --version-sort | tail -1)
```

### Poetry
Python dependency management is done using [Poetry](https://python-poetry.org/). See [installation instructions](https://python-poetry.org/docs/) if poetry has not been installed on your system. To create a virtual environment use `poetry env use 3.12` or whichever version is installed on your system.

Install all dependencies using `poetry install`. Add dependencies using `poetry add <package>` with the optional `--dev` flag for development requirements. Project information and requirements are stored in `pyproject.toml`, dependencies are managed using the `poetry.lock` file in the root project directory.

### Code formatting
[Black](https://black.readthedocs.io/en/stable/) and [isort](https://pycqa.github.io/isort/) are used as code formatter tools.

To run all fixes at once run `poetry run poe fix`. To install the pre-commit hook in your `.git/` directory run `poetry run pre-commit install` once.

### Run tests
Run all unittests using `poetry run poe test`. Please not that the tests expect a mysql endpoint available at `127.0.0.1`, see [section below](###Migrations) for an example command to run a docker mysql container. 

All tests will be run with environment variables set in [.env.test](./.env.test). These can be overridden by variables defined in a `.env.local` file. 

### Migrations
[Alembic](https://alembic.sqlalchemy.org/en/latest/index.htmlhttps://alembic.sqlalchemy.org/en/latest/index.html) is used to perform database migrations. Alembic expects the database to exist. 
To run a docker container with the database, run:

Run all migrations with `poetry run alembic upgrade head`. 

Migration files may be created automatically using `poetry run alembic revision --autogenerate`.

### Mysql
An existing database may be imported by placing it in `/docker-resources/mysql/` before starting the mysql-service for the first time. During startup the mysql-service container will load all `.sql` (or `.zip`) files in that folder.

In case of re-importing: stop the service and remove the docker mysql volume first.

## FastAPI 
### Accessing API Documentation
This project uses FastAPI, a modern web framework for building APIs with Python 3.12 based on standard Python type hints. One of the best features of FastAPI is the automatic interactive API documentation.

To access the interactive API documentation:

Ensure that the Docker services are up and running by executing docker-compose up in the terminal from the project root directory.
Once the services are running, open your web browser.
Navigate to:

`http://0.0.0.0:8000/docs`

## Authorization Guide
### Web-Based Authorization
To authorize via a web browser:

1. Navigate to the following [Authorization](https://myhmb2c.b2clogin.com/32fc4a30-cf52-45e5-b6bd-d5f5d43216e0/oauth2/v2.0/authorize?p=B2C_1_myhmb2c_HmPortal_sin&client_id=632fd4f4-86f8-4a53-aacc-4dee0de705c3&nonce=defaultNonce&redirect_uri=https%3A%2F%2Fholmatro-portal.acceptatie.harborn.dev%2F&scope=openid&response_type=id_token&prompt=login) URL.
2. After the authentication process, you'll be redirected to a return URL.
3. Fetch the id_token from the return URL's parameters. This token will be essential for subsequent requests.

### ROPC

The Resource Owner Password Credentials (ROPC) flow lets a client use the user's email and password to obtain JWT tokens directly from the identity provider. However, due to its sensitive nature, this method isn't recommended for all scenarios.

### Endpoint
```text
POST https://myhmb2c.b2clogin.com/32fc4a30-cf52-45e5-b6bd-d5f5d43216e0/oauth2/v2.0/token?p=B2C_1_myhmb2c_HmPortal_sin
```
### Body

```text
tenant_id
client_id=YOUR_CLIENT_ID
&scope=user.read%20openid%20profile%20offline_access
&username=USER_EMAIL
&password=USER_PASSWORD
&grant_type=password
&client_secret=secret
```

## Configurator
The configurator (state machine) is build dynamically by the [`ConfiguratorFactory`](./holmatro_customer_portal/services/configurator/workflow.py) for a given state. The factory creates a Configurator with all states and all relevant transitions. 

### States
States can be defined by extending the [BaseState](./holmatro_customer_portal/services/configurator/states/base.py) class. The `on_enter` method may be overridden to add logic that should be executed when entering the state, e.g. to update the state definition, and the `on_exit` method may be overridden to add logic that should be executed when exiting the state. 
To add a state you should:

* add its name to the [ConfiguratorState](./holmatro_customer_portal/services/configurator/states/base.py) enum
* add a state definition (json) to ./holmatro_customer_portal/services/configurator/states/definitions/
* create a state class which extends the BaseState 
* add the class file path to ./holmatro_customer_portal/services/configurator/states/__init__.py

The `on_enter` and the `on_exit` methods will be bound to the `Configurator` class by the `ConfiguratorFactory`, the first argument `self` will therefore be an instance of the `Configurator` which has a db session that can be used.

### Transitions
Transitions between states are defined by extending the [BaseTransition](./holmatro_customer_portal/services/configurator/transitions/base.py) class. The `condition` method may be overridden to add logic that determines if this transition may be executed. There are 3 transition types: `next`, `back` and `skip`. Each transition type may exist starting from the same state more than once, the conditions should determine which of these (heuristically evaluated) transistions must be executed.

The `condition` method will be bound to the `Configurator` class by the `ConfiguratorFactory`, the first argument `self` will therefore be an instance of the `Configurator` which has a db session that can be used.

### Possible improvements
* Reconfigure the `get_product` queries within the `/products` and `configurator` sections to enhance ease of maintenance.
* Consider integrating Enum class values into the `state_definition` files. This implementation ensures alignment between the searched value and its stored counterpart, thereby enhancing consistency and reliability in operations.
* In the workflow, when loading state definition, we could already validate it. Instead of return the dict, we should return a State object.

## Product synchronisation
Product information from SyncForce is synchronised to this portal via the Holmatro Data Hub. The Data Hub publishes product ids (SyncForce UMIDs) of updated products to a RabbitMQ messaging queue, the server of which runs alongside the portal backend. [Celery](https://docs.celeryq.dev/en/stable/) is used to [process](./holmatro_customer_portal/celery_config.py) the messages. 

To (re)sync all products, the queue can be populated with all products in the Dealer Portal proposition (= the SyncForce proposition that defines the products and category structure for this application) using the resync endpoint, [see here the documentation for the production environment](https://myportal.holmatro.com/docs#/imports/resync_products_data_imports_resync_products_post). 

It is also possible to publish a single / few product id(s) to the queue. The payload should be json encoded, see the implementation in [the Data Hub module](https://github.com/Holmatro-IT/holmatro-products-sync/blob/main/src/services/products_import/util/rabbit_mq_publisher.py) for details. 

To publish product ids using Postman, use a Pre-request script to create the appropriate body. For example this script will trigger synchronisation of a single product with id `2716ae53-9aef-4d4f-a873-8fbee197bc58`:

```javascript
let body ={
    mode: 'raw',
    raw: JSON.stringify({
        "properties": {
            "content_type": "application/json",
            "content_encoding": "utf-8"
        },
        "routing_key": "celery",
        "payload": JSON.stringify({
            "id": "from-postman",
            "task": "holmatro_customer_portal.services.import_product.import_products_from_storage",
            "args": [[
                "2716ae53-9aef-4d4f-a873-8fbee197bc58",
            ]],
            "retries": 0
        }),
        "payload_encoding": "string"
    }),
    options: {
        raw: {
            language: 'json'
        }
    }
}

pm.request.body.update(body)
```
The `args` argument may contain an array of product ids.

![alt text](postman-queue-example.png)

## Scripts

### Collect translation keys
A script to collect all translation keys from the configurator state definitions can be run with `poetry run python holmatro_customer_portal/scripts/collect_translation_keys.py`. The output will be written to file.

### OpenSearch and OpenSearch Dashboards
Used for indexing and querying products. Dashboard can be found at http://localhost:5601.

### OpenSearch reindexing
To reindex the OpenSearch index, you can use the following command:
- In your terminal, call the function using Typer: `typer holmatro_customer_portal/cli.py run reindex`
- Check help for more options: `typer holmatro_customer_portal/cli.py run reindex --help`

### (Re-)import filters and filter templates
This can be called as often as needed, but needs to be run at least once after the initial database setup. 
It will import all filters and filter templates from the Holmatro Data Hub into the local database.
- In your terminal, call the function using Typer: `typer holmatro_customer_portal/cli.py run reimport-filters`