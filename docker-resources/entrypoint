#!/bin/sh
set -e

echo "Running python entrypoint script..."

## Running migrations
poetry run alembic upgrade head

# Get the current namespace if running in Kubernetes
if [ -r /var/run/secrets/kubernetes.io/serviceaccount/namespace ]; then
  export POD_NAMESPACE=$(cat /var/run/secrets/kubernetes.io/serviceaccount/namespace)
else
  export POD_NAMESPACE="N/A"
fi

if [ "$ENABLE_OTEL" = "true" ]; then
  echo "Starting python server with OpenTelemetry..."
  poetry run pip install opentelemetry-distro opentelemetry-exporter-otlp

  poetry run opentelemetry-bootstrap -a requirements > /tmp/otel-reqs.txt
  poetry run pip install --requirement /tmp/otel-reqs.txt
  rm /tmp/otel-reqs.txt

  poetry run opentelemetry-instrument \
      --traces_exporter otlp \
      --propagators baggage,tracecontext \
      --exporter_otlp_protocol http/protobuf \
      --exporter_otlp_endpoint https://ingest.otel.harborn.com \
      --traces_sampler parentbased_always_on \
      --service_name "${SERVICE_NAME:-unknown-service}" \
      --resource_attributes deployment.environment="${STAGE:-unknown-environment},service.namespace=${POD_NAMESPACE}" \
      python holmatro_customer_portal/main.py
else
  echo "Starting python server..."
  poetry run python holmatro_customer_portal/main.py
fi
