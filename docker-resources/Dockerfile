# Use the specified Python version.
ARG PYTHON_VERSION=3.12

# Use the Python slim image based on Debian Slim.
FROM python:${PYTHON_VERSION}-slim-bookworm

# Set the working directory in the container.
WORKDIR /app

# Copy the pyproject.toml file into the working directory.
COPY pyproject.toml .

# Install poetry (Python package manager) and configure it not to create a virtual environment.
RUN pip install poetry \
    && poetry config virtualenvs.create false

RUN apt-get update \
    && apt-get install -y wkhtmltopdf \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*
    
# Arguments for private PyPi credentials.
ARG PRIVATE_PYPI_USER
ARG PRIVATE_PYPI_SECRET

# Copy the rest of the code into the working directory.
COPY . .

# Configure poetry with the credentials for the private PyPi repository.
RUN poetry config http-basic.harborn $PRIVATE_PYPI_USER $PRIVATE_PYPI_SECRET

# Install the project dependencies using poetry.
RUN poetry install

# Set the PYTHONPATH environment variable.
ENV PYTHONPATH=/app

COPY docker-resources/entrypoint /usr/local/bin/entrypoint
ENTRYPOINT ["entrypoint"]
