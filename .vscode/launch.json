{"version": "0.2.0", "configurations": [{"name": "Python: Holmatro Customer Portal", "type": "python", "request": "launch", "program": "${workspaceFolder}/holmatro_customer_portal/main.py", "console": "integratedTerminal", "justMyCode": false, "python": "${command:python.interpreterPath}", "env": {"STAGE": "local", "DATABASE_NAME": "holmatro_cp_db", "DATABASE_PASSWORD": "Holmatro_pw", "DATABASE_USER": "root", "MYSQL_ARCH": "mysql:8", "UVICORN_PORT": "8000", "REQUEST_TIMEOUT_SECONDS": "30", "AZURE_API_URL": "localhost:8000", "AZURE_API_KEY": "test", "RABBITMQ_DEFAULT_USER": "guest", "RABBITMQ_DEFAULT_PASS": "guest", "RABBITMQ_BROKER_URL": "amqp://guest:guest@rabbitmq:5672/", "AWS_ACCESS_KEY_ID": "testKeysID", "AWS_SECRET_ACCESS_KEY": "testKeys", "AWS_REGION": "eu-west-1", "HOLMATRO_ASSETS_BUCKET_NAME": "harborn-acc-holmatro-product-import-bucket", "B2C_TENANT_ID": "32fc4a30-cf52-45e5-b6bd-d5f5d43216e0", "JWT_ENCODING_KEY": "debug_key_for_local_development"}}, {"name": "Python: <PERSON><PERSON><PERSON>", "type": "python", "request": "attach", "connect": {"host": "localhost", "port": 5678}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "."}], "justMyCode": false}, {"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "justMyCode": false}]}