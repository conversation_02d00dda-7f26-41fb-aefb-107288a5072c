pipeline {
    agent any
    environment {
        DOCKER_BUILD_ARG_PRIVATE_PYPI_USER     = "harborn"
        DOCKER_BUILD_ARG_PRIVATE_PYPI_SECRET   = credentials("harborn_pypi_secret")
    }
    options {
        buildDiscarder(logRotator(numToKeepStr: '30'))
        disableConcurrentBuilds()
    }
    stages {
        stage('Build & Push') {
            steps {
                script {
                    dockerfile.build_and_push()
                }
            }
        }
    }
}
