#!/usr/bin/env python3
"""
Debug script to compare autocomplete behavior between environments.
This script helps identify differences in OpenSearch configuration and data.
"""

import json
import os
from typing import Dict, Any
from opensearchpy import OpenSearch
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.open_search.opensearch_client import OpenSearchClient


def get_opensearch_client():
    """Create OpenSearch client using environment variables."""
    return OpenSearch(
        hosts=[{"host": Env.OPENSEARCH_HOST.get(), "port": Env.OPENSEARCH_PORT.get()}],
        http_auth=(
            (Env.OPENSEARCH_USERNAME.get(), Env.OPENSEARCH_PASSWORD.get())
            if Env.OPENSEARCH_USERNAME.get() and Env.OPENSEARCH_PASSWORD.get()
            else None
        ),
        use_ssl=Env.OPENSEARCH_USE_SSL.get(),
        ssl_show_warnings=True,
    )


def check_environment_variables():
    """Check all OpenSearch-related environment variables."""
    print("=== Environment Variables ===")
    env_vars = [
        "OPENSEARCH_HOST",
        "OPENSEARCH_PORT", 
        "OPENSEARCH_USERNAME",
        "OPENSEARCH_PASSWORD",
        "OPENSEARCH_USE_SSL",
        "OPENSEARCH_INDEX_NAME",
        "OPENSEARCH_FUZZY_SEARCH_ENABLED",
        "IS_HOLMATRO_CATALOG"
    ]
    
    for var in env_vars:
        try:
            value = getattr(Env, var).get()
            # Hide sensitive values
            if "PASSWORD" in var or "USERNAME" in var:
                display_value = "***" if value else "Not set"
            else:
                display_value = value
            print(f"{var}: {display_value}")
        except Exception as e:
            print(f"{var}: ERROR - {e}")
    print()


def check_index_exists_and_mapping(client, index_name):
    """Check if index exists and get its mapping."""
    print(f"=== Index Information: {index_name} ===")
    
    try:
        if not client.indices.exists(index=index_name):
            print(f"❌ Index '{index_name}' does not exist!")
            return False
            
        print(f"✅ Index '{index_name}' exists")
        
        # Get index mapping
        mapping = client.indices.get_mapping(index=index_name)
        print(f"Index mapping structure:")
        
        properties = mapping[index_name]["mappings"]["properties"]
        
        # Check completion suggester fields specifically
        completion_fields = ["product_name_suggest", "article_number_suggest"]
        for field in completion_fields:
            if field in properties:
                field_config = properties[field]
                print(f"  {field}: {json.dumps(field_config, indent=4)}")
            else:
                print(f"  ❌ {field}: MISSING!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking index: {e}")
        return False


def get_index_stats(client, index_name):
    """Get basic statistics about the index."""
    print(f"=== Index Statistics: {index_name} ===")
    
    try:
        stats = client.indices.stats(index=index_name)
        total_docs = stats["indices"][index_name]["total"]["docs"]["count"]
        index_size = stats["indices"][index_name]["total"]["store"]["size_in_bytes"]
        
        print(f"Total documents: {total_docs}")
        print(f"Index size: {index_size / (1024*1024):.2f} MB")
        
        return total_docs
        
    except Exception as e:
        print(f"❌ Error getting index stats: {e}")
        return 0


def test_autocomplete_queries(client, index_name, test_queries):
    """Test specific autocomplete queries."""
    print(f"=== Testing Autocomplete Queries ===")
    
    for query in test_queries:
        print(f"\nTesting query: '{query}'")
        
        # Test the exact same query structure as the application
        body = {
            "_source": ["id", "product_id", "product_names"],
            "suggest": {
                "product_name_suggest": {
                    "prefix": query,
                    "completion": {
                        "field": "product_name_suggest",
                        "size": 10,
                        "skip_duplicates": True,
                    },
                },
                "article_number_suggest": {
                    "prefix": query,
                    "completion": {
                        "field": "article_number_suggest", 
                        "size": 10,
                        "skip_duplicates": True,
                    },
                },
            },
        }
        
        try:
            response = client.search(index=index_name, body=body)
            
            # Process product name suggestions
            product_suggestions = []
            if "suggest" in response and "product_name_suggest" in response["suggest"]:
                for suggestion_group in response["suggest"]["product_name_suggest"]:
                    for option in suggestion_group.get("options", []):
                        product_suggestions.append({
                            "text": option["text"],
                            "score": option["_score"]
                        })
            
            # Process article number suggestions  
            article_suggestions = []
            if "suggest" in response and "article_number_suggest" in response["suggest"]:
                for suggestion_group in response["suggest"]["article_number_suggest"]:
                    for option in suggestion_group.get("options", []):
                        article_suggestions.append({
                            "text": option["text"],
                            "score": option["_score"]
                        })
            
            print(f"  Product name suggestions ({len(product_suggestions)}):")
            for suggestion in product_suggestions[:5]:  # Show top 5
                print(f"    - {suggestion['text']} (score: {suggestion['score']})")
                
            print(f"  Article number suggestions ({len(article_suggestions)}):")
            for suggestion in article_suggestions[:5]:  # Show top 5
                print(f"    - {suggestion['text']} (score: {suggestion['score']})")
                
        except Exception as e:
            print(f"  ❌ Error testing query '{query}': {e}")


def sample_indexed_data(client, index_name, sample_size=5):
    """Sample some indexed data to verify content."""
    print(f"=== Sample Indexed Data ===")
    
    try:
        # Get a sample of documents
        response = client.search(
            index=index_name,
            body={
                "size": sample_size,
                "_source": ["article_number", "product_names", "product_name_suggest", "article_number_suggest"],
                "query": {"match_all": {}}
            }
        )
        
        for i, hit in enumerate(response["hits"]["hits"], 1):
            source = hit["_source"]
            print(f"\nSample {i}:")
            print(f"  Article Number: {source.get('article_number', 'N/A')}")
            print(f"  Product Names: {source.get('product_names', [])}")
            print(f"  Product Name Suggest: {source.get('product_name_suggest', [])}")
            print(f"  Article Number Suggest: {source.get('article_number_suggest', [])}")
            
    except Exception as e:
        print(f"❌ Error sampling data: {e}")


def main():
    """Main debug function."""
    print("🔍 OpenSearch Autocomplete Debug Tool")
    print("=" * 50)
    
    # Check environment variables
    check_environment_variables()
    
    try:
        # Get index name
        index_name = Env.OPENSEARCH_INDEX_NAME.get()
        print(f"Using index: {index_name}")
        
        # Create client
        client = get_opensearch_client()
        
        # Check index exists and mapping
        if not check_index_exists_and_mapping(client, index_name):
            return
        
        # Get index statistics
        doc_count = get_index_stats(client, index_name)
        
        if doc_count == 0:
            print("⚠️  Index is empty! No documents to test.")
            return
        
        # Sample some data
        sample_indexed_data(client, index_name)
        
        # Test problematic queries
        test_queries = ["Cutt", "Mobi", "cut", "mob"]
        test_autocomplete_queries(client, index_name, test_queries)
        
        print("\n" + "=" * 50)
        print("✅ Debug complete!")
        print("\nNext steps:")
        print("1. Compare this output between localhost and test environment")
        print("2. Check if document counts match")
        print("3. Verify completion suggester data is populated correctly")
        print("4. Compare fuzzy search settings")
        
    except Exception as e:
        print(f"❌ Fatal error: {e}")


if __name__ == "__main__":
    main()
