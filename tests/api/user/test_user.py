import uuid
from unittest.mock import patch

from holmatro_customer_portal.database.enums import Currency
from holmatro_customer_portal.database.models import Category, User
from tests.fixtures.database_models_fixture import mocked_language, preferred_mocked_language
from tests.fixtures.my_hm_fixture import my_hm_mock_responses
from tests.fixtures.test_client import TestClientFactory, mock_user_claims


@patch("holmatro_customer_portal.services.get_user.fetch_user_categories")
@patch("holmatro_customer_portal.services.get_user.DatabaseOperations.upsert")
def test_get_profile_details(mock_db_ops, mock_fetch_user_categories, my_hm_mock_responses):
    categories = [Category(category_id="7411"), Category(category_id="2838")]
    mock_fetch_user_categories.return_value = categories

    mock_user = User(
        id=uuid.uuid4(),
        name="<PERSON>",
        email="<EMAIL>",
        last_name="<PERSON><PERSON>",
        main_language_id=mocked_language.id,
        main_language=mocked_language,
        currency=Currency.USD,
        favorite_products=[],
        preferred_language_id=preferred_mocked_language.id,
        preferred_language=preferred_mocked_language,
        relation_name="Holmatro",
        configurations=[],
        categories=categories,
    )

    mock_db_ops.side_effect = [
        mocked_language,
        mock_user,
    ]
    # Mock response of db query for categories names
    test_client = TestClientFactory.get_client()

    response = test_client.get("/data/user/profile")

    assert response.status_code == 200
    assert response.json()["name"] == mock_user_claims.name
    assert response.json()["email"] == mock_user_claims.emails[0]
    assert response.json()["last_name"] == mock_user_claims.family_name
    assert response.json()["categories"] == [7411, 2838]
    assert response.json()["id"] == str(mock_user.id)
