from holmatro_customer_portal.database.enums import SystemType
from tests.fixtures.database_models_fixture import mocked_language, mocked_user, preferred_mocked_language
from tests.fixtures.test_client import TestClientFactory, mock_database

test_client = TestClientFactory.get_client()


def test_update_preferred_language():
    test_language_code = "en"

    response = test_client.post(f"/data/user/preferred_language/{test_language_code}")
    assert response.status_code == 200
    data = response.json()
    assert data["details"] == "Preferred language updated successfully"
    assert data["preferred_language"] == test_language_code


def test_not_valid_preferred_language():
    test_language_code = "hkh"

    response = test_client.post(f"/data/user/preferred_language/{test_language_code}")
    assert response.status_code == 422


def test_not_allowed_preferred_language():
    mock_database.add(preferred_mocked_language)

    mocked_user.preferred_language = mocked_language
    mocked_user.system_type = SystemType.IMPERIAL

    response = test_client.post(f"/data/user/preferred_language/{preferred_mocked_language.language_code}")
    assert response.status_code == 409
