from holmatro_customer_portal.database.enums import SystemType
from tests.fixtures.database_models_fixture import mocked_user, preferred_mocked_language
from tests.fixtures.test_client import TestClientFactory

test_client = TestClientFactory.get_client()


def test_update_preferred_system_type():
    type_ = SystemType.METRIC

    response = test_client.post(f"/data/user/system_type/{type_.value}")
    assert response.status_code == 200

    data = response.json()
    assert data["details"] == "Preferred system type updated successfully"
    assert data["preferred_system_type"] == type_.value


def test_not_allowed_system_type():
    """Imperial is only allowed in combination with the English language."""
    mocked_user.preferred_language = preferred_mocked_language
    mocked_user.system_type = SystemType.METRIC

    response = test_client.post(f"/data/user/system_type/{SystemType.IMPERIAL.value}")
    assert response.status_code == 409
