import uuid
from http import HTTPStatus
from unittest.mock import MagicMock, patch

from fastapi import HTTPException

from holmatro_customer_portal.schemas.response_schema import ProductDetailsRes
from tests.fixtures.database_models_fixture import mocked_user
from tests.fixtures.my_hm_client import MyHolmatroClientFactoryMock, mock_my_hm_client
from tests.fixtures.responses_fixtures import mock_product_details
from tests.fixtures.test_client import TestClientFactory, mock_database

test_client = TestClientFactory.get_client()


@patch("holmatro_customer_portal.api.products.MyHolmatroClientFactory", MyHolmatroClientFactoryMock)
@patch("holmatro_customer_portal.api.products.get_product_details_handle")
def test_happy_path(mock_get_product_details_handle: MagicMock):
    mock_get_product_details_handle.return_value = ProductDetailsRes(**mock_product_details)

    res = test_client.get("/data/products/product_details/4d5cecd0-3798-474c-b435-c24b78d827e2")

    assert set(res.json()) == set(mock_product_details)
    assert res.status_code == HTTPStatus.OK
    mock_get_product_details_handle.assert_called_once_with(
        uuid.UUID("4d5cecd0-3798-474c-b435-c24b78d827e2"),
        mocked_user,
        mock_database,
        mock_my_hm_client,
    )


@patch("holmatro_customer_portal.api.products.MyHolmatroClientFactory", MyHolmatroClientFactoryMock)
@patch("holmatro_customer_portal.api.products.get_product_details_handle")
def test_failure(mock_get_product_details_handle: MagicMock):
    mock_get_product_details_handle.side_effect = HTTPException(status_code=404, detail="Product not found")

    res = test_client.get("/data/products/product_details/4d5cecd0-3798-474c-b435-c24b78d827e2")

    assert res.status_code == HTTPStatus.NOT_FOUND
    mock_get_product_details_handle.assert_called_once_with(
        uuid.UUID("4d5cecd0-3798-474c-b435-c24b78d827e2"),
        mocked_user,
        mock_database,
        mock_my_hm_client,
    )
