import uuid
from http import HTTPStatus
from unittest.mock import MagicMock, patch

import pytest

from holmatro_customer_portal.schemas.response_schema import AutocompleteRes, ProductAutocomplete, SearchParamsReq
from tests.fixtures.database_models_fixture import mocked_user
from tests.fixtures.test_client import TestClientFactory, mock_catalog_repository, mock_database

product_id = "a071a444-fb4c-4722-b334-c341522b6ec4"
product_id2 = uuid.uuid4()


@pytest.mark.parametrize(
    "mock_response, expected_result",
    [
        (
            AutocompleteRes(products=[], suggestions=["chain", "cylinder", "cylinders"]),
            {"products": [], "suggestions": ["chain", "cylinder", "cylinders"]},
        ),
        (AutocompleteRes(products=[], suggestions=[]), {"products": [], "suggestions": []}),
        (
            AutocompleteRes(
                products=[
                    ProductAutocomplete(product_id=uuid.UUID(product_id), name="product 1", category_id=1),
                    ProductAutocomplete(product_id=uuid.UUID(product_id), name="product 2", category_id=1),
                ],
                suggestions=["chain", "cylinder", "cylinders"],
            ),
            {
                "products": [
                    {"product_id": product_id, "name": "product 1", "category_id": 1},
                    {"product_id": product_id, "name": "product 2", "category_id": 1},
                ],
                "suggestions": ["chain", "cylinder", "cylinders"],
            },
        ),
    ],
)
@patch("holmatro_customer_portal.api.products.get_autocomplete_handle")
def test_get_autocomplete(
    mock_get_autocomplete_handle: MagicMock,
    mock_response: AutocompleteRes,
    expected_result: dict,
    catalog_repo_mock: MagicMock,
):
    params = SearchParamsReq(search_query="c")
    mock_get_autocomplete_handle.return_value = mock_response

    test_client = TestClientFactory.get_client(catalog_repository=catalog_repo_mock)
    res = test_client.get("/data/products/autocomplete/c")

    assert res.status_code == HTTPStatus.OK
    assert res.json() == expected_result
    mock_get_autocomplete_handle.assert_called_once_with(catalog_repo_mock, params, mocked_user, mock_database)
