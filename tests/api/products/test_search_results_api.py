from http import HTTPStatus
from unittest.mock import MagicMock, patch

import pytest

from holmatro_customer_portal.schemas.response_schema import ProductsRes, SearchFiltersReq, SearchParamsReq
from holmatro_customer_portal.utils.enums import SyncforceCategory
from tests.fixtures.database_models_fixture import mocked_user
from tests.fixtures.my_hm_client import MyHolmatroClientFactoryMock, mock_my_hm_client
from tests.fixtures.test_client import TestClientFactory, mock_catalog_repository, mock_database
from tests.fixtures.tweakwise_fixtures import mock_tweakwise_response_properties

test_client = TestClientFactory.get_client()


@pytest.mark.parametrize(
    "url, filters, params",
    [
        pytest.param("/data/products/search/chain", {}, SearchParamsReq(search_query="chain"), id="Test search"),
        pytest.param(
            # Test for non-existent parameters
            "/data/products/search/chain?some_param=3&another_param=1-1000",
            {},
            SearchParamsReq(search_query="chain"),
            id="Test random param",
        ),
        pytest.param(
            "/data/products/search/cylinder?pg_no=3&prod_cat_path=20007411&no_prod_pg=24",
            {"material": "aluminium"},
            SearchParamsReq(search_query="cylinder", page_number=3, products_per_page=24),
            id="Test page number and products per page",
        ),
        pytest.param(
            "/data/products/search/cylinder?prod_category_id=7411",
            {},
            SearchParamsReq(
                search_query="cylinder",
                category_id=SyncforceCategory.INDUSTRIAL_LIFTING,
            ),
            id="Test validate category id",
        ),
    ],
)
@patch("holmatro_customer_portal.api.products.MyHolmatroClientFactory", MyHolmatroClientFactoryMock)
@patch("holmatro_customer_portal.api.products.get_products_by_search_handle")
def test_happy_path(
    mock_get_products_by_search_handle: MagicMock,
    url: str,
    filters: dict,
    params: SearchParamsReq,
):
    mock_get_products_by_search_handle.return_value = ProductsRes(
        **{"products": [], "properties": mock_tweakwise_response_properties}
    )

    response = test_client.post(url, json={"filters": filters})

    assert response.status_code == HTTPStatus.OK
    mock_get_products_by_search_handle.assert_called_once_with(
        mock_catalog_repository,
        params,
        SearchFiltersReq(**{"filters": filters}),
        mocked_user,
        mock_database,
        mock_my_hm_client,
    )


@pytest.mark.parametrize(
    "url, filters, status_code",
    [
        pytest.param(
            "/data/products/search/chain",
            "some_filter",
            HTTPStatus.UNPROCESSABLE_ENTITY,
            id="Test raise error for body as string",
        ),
        pytest.param(
            "/data/products/search/cylinder?pg_no=a",
            None,
            HTTPStatus.UNPROCESSABLE_ENTITY,
            id="Test raise error for page_number as string",
        ),
        pytest.param(
            "/data/products/search/cylinder?prod_category_id=a",
            None,
            HTTPStatus.UNPROCESSABLE_ENTITY,
            id="Test raise error for category id as string",
        ),
        pytest.param(
            "/data/products/search/cylinder?prod_category_id=2",
            None,
            HTTPStatus.FORBIDDEN,
            id="Test raise error for invalid category id",
        ),
    ],
)
@patch("holmatro_customer_portal.api.products.get_products_by_search_handle")
def test_invalid_input(
    mock_get_products_by_search_handle: MagicMock, url: str, filters: str | None, status_code: HTTPStatus
):
    response = test_client.post(url, json=filters)

    assert response.status_code == status_code
    mock_get_products_by_search_handle.assert_not_called()
