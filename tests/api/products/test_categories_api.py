from http import HTTPStatus
from unittest.mock import MagicMock

import pytest
from fastapi import HTTPEx<PERSON>

from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import Category
from tests.fixtures.test_client import TestClientFactory


@pytest.mark.parametrize(
    "call, category_id",
    [
        (True, 50005000),  # Test category id in request
        (False, 20007411),  # Assert called with category linked to the user
    ],
)
def test_happy_path(call: bool, category_id: int):
    mock_response = {
        "title": "Category 1",
        "category_id": 1,
        "category_path": "1",
        "children": [],
        "image_url": None,
    }

    mock_catalog_repository = MagicMock()
    mock_catalog_repository.get_category_tree.return_value = Category(**mock_response)
    test_client = TestClientFactory.get_client(catalog_repository=mock_catalog_repository)

    if call:
        result = test_client.get(f"/data/products/categories?cat_id={category_id}")
    else:
        result = test_client.get("/data/products/categories")

    assert result.json() == [mock_response]
    assert result.status_code == HTTPStatus.OK
    mock_catalog_repository.get_category_tree.assert_called_once_with(category_id)


def test_failure():
    mock_catalog_repository = MagicMock()
    mock_catalog_repository.get_category_tree.side_effect = HTTPException(status_code=HTTPStatus.NOT_FOUND)
    test_client = TestClientFactory.get_client(catalog_repository=mock_catalog_repository)

    result = test_client.get("/data/products/categories?cat_id=10005000")

    assert result.status_code == HTTPStatus.NOT_FOUND
    mock_catalog_repository.get_category_tree.assert_called_once_with(10005000)


def test_invalid_input():
    mock_catalog_repository = MagicMock()
    test_client = TestClientFactory.get_client(catalog_repository=mock_catalog_repository)
    result = test_client.get("/data/products/categories?cat_id=500020011")

    assert result.status_code == HTTPStatus.BAD_REQUEST
    assert result.json()["detail"] == "No valid category ID provided"
    mock_catalog_repository.get_category_tree.assert_not_called()
