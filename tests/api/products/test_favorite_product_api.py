import copy
import uuid
from http import HTTPStatus
from unittest.mock import MagicMock, patch

from holmatro_customer_portal.schemas.response_schema import ProductPreviewRes
from tests.fixtures.database_models_fixture import mocked_user
from tests.fixtures.my_hm_client import MyHolmatroClientFactoryMock, mock_my_hm_client
from tests.fixtures.responses_fixtures import mock_product_preview
from tests.fixtures.test_client import TestClientFactory, mock_database

test_client = TestClientFactory.get_client()
product_id = uuid.uuid4()


@patch("holmatro_customer_portal.api.products.MyHolmatroClientFactory", MyHolmatroClientFactoryMock)
@patch("holmatro_customer_portal.api.products.get_favorite_products_handle")
def test_get_favorite_products(mock_get_favorites: MagicMock):
    product_preview = copy.deepcopy(mock_product_preview)
    product_preview["product_id"] = str(product_id)

    mock_get_favorites.return_value = [ProductPreviewRes(**product_preview)]

    res = test_client.get("/data/products/favorite_products")

    assert res.status_code == HTTPStatus.OK
    assert res.json() == [product_preview]
    mock_get_favorites.assert_called_once_with(mocked_user, mock_database, mock_my_hm_client)


@patch("holmatro_customer_portal.api.products.add_favorite_product_handle")
def test_add_favorite_product(mock_add_favorite: MagicMock):
    res = test_client.post(f"/data/products/favorite_product/{product_id}")

    assert res.status_code == HTTPStatus.NO_CONTENT
    mock_add_favorite.assert_called_once_with(product_id, mocked_user, mock_database)


@patch("holmatro_customer_portal.api.products.remove_favorite_product_handle")
def test_remove_favorite_product(mock_remove_favorite: MagicMock):
    res = test_client.delete(f"/data/products/favorite_product/{product_id}")

    assert res.status_code == HTTPStatus.NO_CONTENT
    mock_remove_favorite.assert_called_once_with(product_id, mocked_user, mock_database)
