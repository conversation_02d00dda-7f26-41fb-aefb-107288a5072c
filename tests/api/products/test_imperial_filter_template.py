from http import HTTPStatus
from unittest.mock import MagicMock, patch

import pytest

from holmatro_customer_portal.schemas.response_schema import ParamsReq, ProductsWithFiltersRes
from holmatro_customer_portal.services.configurator.configurator_enums import ImperialFilterTemplateIDs
from tests.api.products.test_get_products_api import mock_response
from tests.fixtures.database_models_fixture import mocked_us_user
from tests.fixtures.my_hm_client import MyHolmatroClientFactoryMock, mock_my_hm_client
from tests.fixtures.test_client import TestClientFactory, mock_catalog_repository, mock_database


@pytest.mark.parametrize(
    "category_id, filter_template_id",
    [
        pytest.param(7411, ImperialFilterTemplateIDs.INDUSTRIAL_LIFTING, id="lifting"),
        pytest.param(2838, ImperialFilterTemplateIDs.INDUSTRIAL_CUTTING, id="cutting"),
    ],
)
@patch("holmatro_customer_portal.api.products.MyHolmatroClientFactory", MyHolmatroClientFactoryMock)
@patch("holmatro_customer_portal.api.products.get_products_with_filters_handle")
def test_imperial_filter(
    mock_get_products_handle: MagicMock,
    category_id: int,
    filter_template_id: int,
):
    # It overrides the dependencies to accept an user with "country" : US
    us_client = TestClientFactory.get_client(user=mocked_us_user)

    mock_get_products_handle.return_value = ProductsWithFiltersRes(**mock_response)

    result = us_client.post(f"/data/products?prod_category_id={category_id}")

    assert result.status_code == HTTPStatus.OK
    mock_get_products_handle.assert_called_once_with(
        mock_catalog_repository,
        ParamsReq(
            search_query=None,
            category_path=f"1000{category_id}",
            page_number=1,
            products_per_page=12,
            filter_template_id=filter_template_id,
            category_id=category_id,
        ),
        None,
        mocked_us_user,
        mock_database,
        mock_my_hm_client,
    )

    # It overrides the dependencies back for the next tests
    TestClientFactory.get_client()
