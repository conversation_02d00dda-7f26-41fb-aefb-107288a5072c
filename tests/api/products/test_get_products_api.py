import json
from http import HTTPStatus
from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest

from holmatro_customer_portal.schemas.response_schema import ParamsReq, ProductsWithFiltersRes, SearchFiltersReq
from holmatro_customer_portal.utils.enums import SyncforceCategory
from tests.fixtures.database_models_fixture import mocked_user
from tests.fixtures.my_hm_client import MyHolmatroClientFactoryMock, mock_my_hm_client
from tests.fixtures.responses_fixtures import mock_product_preview
from tests.fixtures.test_client import TestClientFactory, mock_catalog_repository, mock_database
from tests.fixtures.tweakwise_fixtures import mock_tweakwise_response_properties

test_client = TestClientFactory.get_client()
product_id = uuid4()
mock_response = {
    "products": [{**mock_product_preview.copy(), "product_id": str(product_id)}],
    "properties": mock_tweakwise_response_properties,
    "filters": [],
}


@pytest.mark.parametrize(
    "url, params, filters",
    [
        pytest.param(
            "/data/products?prod_category_id=7411",
            ParamsReq(
                category_path="20007411",
                category_id=SyncforceCategory.INDUSTRIAL_LIFTING,
            ),
            None,
            id="Test category path",
        ),
        pytest.param(
            "/data/products?prod_cat_path=20007411",
            ParamsReq(
                category_path="20007411",
                category_id=SyncforceCategory.INDUSTRIAL_LIFTING,
            ),
            SearchFiltersReq(filters={"material": "aluminium"}),
            id="Test category path with filters",
        ),
        pytest.param(
            "/data/products?prod_category_id=7411&prod_category_id=7411",
            ParamsReq(
                category_path="20007411",
                category_id=SyncforceCategory.INDUSTRIAL_LIFTING,
            ),
            None,
            id="Test category path and category id",
        ),
        pytest.param(
            "/data/products?prod_category_id=7411",
            ParamsReq(
                category_path="20007411",
                category_id=SyncforceCategory.INDUSTRIAL_LIFTING,
            ),
            None,
            id="Test category id without path",
        ),
    ],
)
@patch("holmatro_customer_portal.api.products.MyHolmatroClientFactory", MyHolmatroClientFactoryMock)
@patch("holmatro_customer_portal.api.products.get_products_with_filters_handle")
def test_happy_path(
    mock_get_products_handle: MagicMock,
    url: str,
    params: ParamsReq,
    filters: SearchFiltersReq | None,
):
    mock_get_products_handle.return_value = ProductsWithFiltersRes(**mock_response)

    if not filters:
        result = test_client.post(url)
    else:
        result = test_client.post(url, json={"filters": filters.filters})

    assert result.json() == json.loads(ProductsWithFiltersRes(**mock_response).model_dump_json())
    assert result.status_code == HTTPStatus.OK
    mock_get_products_handle.assert_called_once_with(
        mock_catalog_repository, params, filters, mocked_user, mock_database, mock_my_hm_client
    )


@pytest.mark.parametrize(
    "url, status_code",
    [
        pytest.param("/data/products?prod_cat_path=1", HTTPStatus.FORBIDDEN, id="Test invalid path 1"),
        pytest.param("/data/products?prod_cat_path=11007411", HTTPStatus.FORBIDDEN, id="Test invalid path (language)"),
        pytest.param("/data/products", HTTPStatus.BAD_REQUEST, id="Test unknown path or id"),
        pytest.param("/data/products?prod_category_id=1", HTTPStatus.FORBIDDEN, id="Test invalid id"),
    ],
)
@patch("holmatro_customer_portal.api.products.MyHolmatroClientFactory", MyHolmatroClientFactoryMock)
@patch("holmatro_customer_portal.api.products.get_products_with_filters_handle")
def test_failure(
    mock_get_products_handle: MagicMock,
    url: str,
    status_code: HTTPStatus,
):
    mock_get_products_handle.return_value = ProductsWithFiltersRes(**mock_response)

    result = test_client.post(url)

    assert result.status_code == status_code
    mock_get_products_handle.assert_not_called()
