from http import HTTPStatus
from unittest.mock import ANY, MagicMock, patch

import pytest
from fastapi import HTT<PERSON>Exception

from holmatro_customer_portal.api.dependencies import _get_category_id_from_path, _validate_and_get_category_path
from holmatro_customer_portal.schemas.response_schema import ParamsReq, ProductsWithFiltersRes
from holmatro_customer_portal.utils.enums import SyncforceCategory
from tests.api.products.test_get_products_api import mock_response
from tests.fixtures.database_models_fixture import mocked_user
from tests.fixtures.my_hm_client import MyHolmatroClientFactoryMock
from tests.fixtures.test_client import TestClientFactory

test_client = TestClientFactory.get_client()


@patch("holmatro_customer_portal.api.products.get_category_tree_handle")
def test_invalid_category_id(mock_get_category_tree_handle: MagicMock):
    result = test_client.get("/data/products/categories/?cat_id=5000")

    assert result.status_code == HTTPStatus.BAD_REQUEST
    mock_get_category_tree_handle.assert_not_called()


@pytest.mark.parametrize(
    "category_path, url_call",
    [
        pytest.param("50007411", "?prod_cat_path=50007411", id="test category path"),
        pytest.param(
            "50008604-50007411",
            "?prod_cat_path=50008604-50007411",
            id="test category path with first chunk of Industrial",
        ),
        pytest.param("20007411", "?prod_category_id=7411", id="test user category id"),
        pytest.param(
            "50007411-12345678",
            "?prod_cat_path=50007411-12345678&prod_category_id=7411",
            id="test user category id and category path sent together",
        ),
        pytest.param(
            "20007411",
            "?prod_cat_path=54007411-12345678&prod_category_id=7411",
            id="test wrong category_path, correct user category id",
        ),
    ],
)
@patch("holmatro_customer_portal.api.products.get_products_with_filters_handle")
@patch("holmatro_customer_portal.api.products.MyHolmatroClientFactory", MyHolmatroClientFactoryMock)
def test_validate_category_path_or_category_id(
    mock_get_products_handle: MagicMock,
    category_path: str | None,
    url_call: str,
):
    mock_get_products_handle.return_value = ProductsWithFiltersRes(**mock_response)
    params = ParamsReq(
        search_query=None,
        category_path=category_path,
        page_number=1,
        products_per_page=12,
        category_id=SyncforceCategory.INDUSTRIAL_LIFTING,
    )
    result = test_client.post(f"/data/products{url_call}")

    assert result.status_code == HTTPStatus.OK
    mock_get_products_handle.assert_called_once_with(ANY, params, ANY, ANY, ANY, ANY)


@pytest.mark.parametrize(
    "url_call, result_details, status_code",
    [
        pytest.param(
            "?",
            "Category path or user category is required",
            HTTPStatus.BAD_REQUEST,
            id="not user_category_id neither category_path",
        ),
        pytest.param(
            "?prod_cat_path=52007411",
            "User not authorized for the requested category path",
            HTTPStatus.FORBIDDEN,
            id="test category path not in Industrial",
        ),
        pytest.param(
            "?prod_category_id=3622",
            "User is not linked to this category",
            HTTPStatus.FORBIDDEN,
            id="test user category id",
        ),
        pytest.param(
            "?prod_cat_path=54007411-12345678&prod_category_id=33",
            "User is not linked to this category",
            HTTPStatus.FORBIDDEN,
            id="test not valid category path neither category id",
        ),
    ],
)
@patch("holmatro_customer_portal.api.products.get_products_with_filters_handle")
def test_invalid_category_path_or_category_id(
    mock_get_products_handle: MagicMock,
    url_call: str,
    result_details: str,
    status_code: HTTPStatus,
):
    mock_get_products_handle.return_value = ProductsWithFiltersRes(**mock_response)
    result = test_client.post(f"/data/products{url_call}")

    assert result.status_code == status_code
    assert result.json()["detail"] == result_details
    mock_get_products_handle.assert_not_called()


@pytest.mark.parametrize(
    "path, id",
    [
        ("20007411", 7411),
        ("20007411", None),
        (None, 7411),
    ],
)
def test_validate_and_get_category_path(path: str, id: int):
    result = _validate_and_get_category_path(mocked_user, path, id)

    assert result == "20007411"


@pytest.mark.parametrize(
    "path, id, status_code, detail",
    [
        pytest.param(
            None,
            None,
            HTTPStatus.BAD_REQUEST,
            "Category path or user category is required",
            id="Tests no path or id provided",
        ),
        pytest.param(
            "100012",
            None,
            HTTPStatus.FORBIDDEN,
            "User not authorized for the requested category path",
            id="Tests invalid path, unknown id",
        ),
        pytest.param(
            None, 11, HTTPStatus.FORBIDDEN, "User is not linked to this category", id="Tests invalid path, invalid id"
        ),
    ],
)
def test_validate_and_get_category_path_raises(path: str, id: int, status_code: HTTPStatus, detail: str):
    with pytest.raises(HTTPException) as e:
        _validate_and_get_category_path(mocked_user, path, id)

    assert e.value.status_code == status_code
    assert e.value.detail == detail


@pytest.mark.parametrize("path", ["10007411", "10008604-10007411", "10008604-10007411-100022"])
def test_get_category_id_from_path(path: str):
    result = _get_category_id_from_path(path)

    assert result == 7411


@pytest.mark.parametrize("path", ["10008604"])
def test_get_category_id_from_path_raises(path: str):
    with pytest.raises(RuntimeError) as e:
        _get_category_id_from_path(path)

    assert e.value.args[0] == f"Category path {path} doesn't have enough information to retrieve a category id"
