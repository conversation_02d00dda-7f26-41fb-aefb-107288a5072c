from tests.fixtures.test_client import TestClientFactory


def test_get_claims_endpoint():
    client = TestClientFactory.get_client()

    response = client.get("data/auth/claims")

    assert response.status_code == 200
    decoded_response = response.json()

    assert decoded_response["name"] == "<PERSON>"
    assert decoded_response["family_name"] == "<PERSON><PERSON>"
    assert decoded_response["emails"] == ["<PERSON><PERSON>Jan<PERSON>@example.com"]
