import re

import pytest
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Language, User
from holmatro_customer_portal.utils.auth.auth import get_jwt_token, get_user, get_user_claims
from tests.fixtures.test_client import TestClientFactory


class TestGenerateToken:

    @pytest.fixture(autouse=True)
    def run_setup_and_teardown(self):
        self.db = UnifiedAlchemyMagicMock()
        self.client = TestClientFactory.get_client(db=self.db)
        self.client.app.dependency_overrides[get_user_claims] = None
        self.client.app.dependency_overrides[get_jwt_token] = None
        self.client.app.dependency_overrides[get_user] = None

        yield  # This is where the test is run

        # It overrides the dependencies back for the next tests
        TestClientFactory.get_client()

    def test_generate_token(self):
        # It overrides the dependencies to mock a call without a user
        self.db.add(Language(language_code="en"))

        response = self.client.get("data/auth/generate-token")

        assert response.status_code == 200
        assert isinstance(response.json()["token"], str)

        users = self.db.query(User).all()
        anonymous_user = users[0]
        assert re.match(r"[0-9a-z-]{36}@example.com", anonymous_user.email)
        assert anonymous_user.preferred_language.language_code == "en"
