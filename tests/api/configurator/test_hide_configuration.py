from http import HTTPStatus
from unittest.mock import patch
from uuid import uuid4

from holmatro_customer_portal.database.models import Configuration
from tests.fixtures.configurator import ConfiguratorFactory
from tests.fixtures.test_client import TestClientFactory, mock_database, mock_db_user

test_client = TestClientFactory.get_client()
configuration_id = uuid4()


@patch("holmatro_customer_portal.api.configurator.ConfiguratorFactory", return_value=ConfiguratorFactory())
def test_hide_configuration(factory: ConfiguratorFactory):
    mock_database.query(Configuration).delete()
    mock_database.add(Configuration(id=configuration_id, user=mock_db_user(), reference="hidden_test"))

    result = test_client.delete(f"/data/configurator/configuration/{configuration_id}")
    assert result.status_code == HTTPStatus.NO_CONTENT

    updated_configuration = mock_database.query(Configuration).all()
    assert updated_configuration[0].hidden is True

    mock_database.query(Configuration).delete()
