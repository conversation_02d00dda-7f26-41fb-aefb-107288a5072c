from http import HTTPStatus
from unittest.mock import patch
from uuid import uuid4

import pytest

from holmatro_customer_portal.database.models import Category, Configuration
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from tests.fixtures.my_hm_client import MyHolmatroClientFactoryMock
from tests.fixtures.test_client import TestClientFactory, mock_database, mocked_user

test_client = TestClientFactory.get_client()
configuration_id = uuid4()


@pytest.mark.parametrize(
    "state, status_code",
    [
        (ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST.value, HTTPStatus.BAD_REQUEST),
        (ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL.value, HTTPStatus.OK),
        (ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN.value, HTTPStatus.OK),
    ],
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_overview.Overview.on_enter")
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.add_couplers_to_configuration"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.set_hose_state_content"
)
@patch("holmatro_customer_portal.api.configurator.ConfiguratorMapper.store_configuration_product", return_value=[])
@patch("holmatro_customer_portal.api.configurator.MyHolmatroClientFactory", MyHolmatroClientFactoryMock)
def test_validate_amount_product_in_request(_, __, ___, ____, state: str, status_code: HTTPStatus):
    mock_database.query(Configuration).delete()
    mock_database.add(
        Configuration(id=configuration_id, user=mocked_user, reference="test", category=Category(category_id=1))
    )

    request = {
        "configuration_id": str(configuration_id),
        "state_id": state,
        "configuration_data": [],
        "products": [{"product_id": str(uuid4()), "quantity": 1}, {"product_id": str(uuid4()), "quantity": 3}],
    }
    with patch(
        "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_cylinder.get_configuration_cylinder_acting_type",
        return_value=None,
    ):
        result = test_client.post("/data/configurator/next", json=request)

    assert result.status_code == status_code

    mock_database.query(Configuration).delete()
