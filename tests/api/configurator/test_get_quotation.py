from http import HTTPStatus
from unittest.mock import patch
from uuid import uuid4

from holmatro_customer_portal.database.models import Configuration, User
from tests.fixtures.test_client import TestClientFactory, mock_database

test_client = TestClientFactory.get_client()
configuration_id = uuid4()


@patch("holmatro_customer_portal.api.configurator.get_quotation")
def test_happy_path(get_quotation_mock):
    get_quotation_mock.return_value = b"pdf"

    mock_database.add(Configuration(id=configuration_id, user=User(anonymous=True)))

    result = test_client.get(f"/data/configurator/overview/{configuration_id}")
    assert result.status_code == HTTPStatus.OK
    assert result.content == b"pdf"
    assert result.headers["Content-Type"] == "application/pdf"


def test_not_found():
    """Test it verifies if the user of the associated configuration is anonymous"""

    mock_database.add(Configuration(id=configuration_id, user=User()))

    result = test_client.get(f"/data/configurator/overview/{uuid4()}")
    assert result.status_code == HTTPStatus.NOT_FOUND


def test_verification_anonymous():
    """Test it verifies if the user of the associated configuration is anonymous"""

    mock_database.add(Configuration(id=configuration_id, user=User()))

    result = test_client.get(f"/data/configurator/overview/{configuration_id}")
    assert result.status_code == HTTPStatus.FORBIDDEN
