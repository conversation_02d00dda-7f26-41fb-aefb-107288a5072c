import uuid
from http import HTTPStatus
from unittest.mock import patch
from uuid import uuid4

import pytest

from holmatro_customer_portal.database.models import Category, Configuration, User
from holmatro_customer_portal.schemas.configurator import ConfigurationStateRes
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from tests.fixtures.configurator import ConfiguratorFactory
from tests.fixtures.my_hm_client import MyHolmatroClientFactoryMock
from tests.fixtures.test_client import TestClientFactory, mock_database, mock_db_user

test_client = TestClientFactory.get_client()
configuration_id = uuid4()


@pytest.mark.parametrize(
    "state, last_transition, expected_state",
    [
        # (
        #     ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS.value,
        #     "next",
        #     ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST.value,
        # ),
        # (
        #     ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST.value,
        #     "next",
        #     ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING.value,
        # ),
        # (
        #     ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING.value,
        #     "skip",
        #     ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST.value,
        # ),
        # (
        #     ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS.value,
        #     "back",
        #     ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING.value,
        # ),
        (
            None,
            "next",
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS.value,
        ),
        (
            ConfiguratorState.INDUSTRIAL_OVERVIEW.value,
            "back",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL.value,
        ),
    ],
)
@patch("holmatro_customer_portal.api.configurator.ConfiguratorFactory", return_value=ConfiguratorFactory())
@patch("holmatro_customer_portal.api.configurator.MyHolmatroClientFactory", MyHolmatroClientFactoryMock)
def test_get_configuration(factory: ConfiguratorFactory, state: str, last_transition: str, expected_state: str):
    mock_database.add(
        Configuration(
            id=configuration_id,
            user=mock_db_user(),
            reference="test",
            last_updated_state=state,
            last_transition=last_transition,
            category=Category(category_id="7411"),
        )
    )

    result = test_client.get(f"/data/configurator/configuration/{configuration_id}")
    assert result.status_code == HTTPStatus.OK

    response = ConfigurationStateRes(**result.json())
    assert response.configuration_id == str(configuration_id)
    assert response.state_definition.id == expected_state

    mock_database.query(Configuration).delete()


@pytest.mark.parametrize(
    "configuration",
    [
        None,
        Configuration(id=uuid.uuid4(), user=mock_db_user(), reference="test"),
        Configuration(id=configuration_id, user=User(id=uuid.uuid4()), reference="test"),
    ],
)
@patch("holmatro_customer_portal.api.configurator.ConfiguratorFactory", return_value=ConfiguratorFactory())
@patch("holmatro_customer_portal.api.configurator.MyHolmatroClientFactory", MyHolmatroClientFactoryMock)
def test_get_configuration_exception(factory: ConfiguratorFactory, configuration: Configuration):
    if configuration:
        mock_database.add(configuration)

    result = test_client.get(f"/data/configurator/configuration/{configuration_id}")
    assert result.status_code == HTTPStatus.NOT_FOUND

    mock_database.query(Configuration).delete()
