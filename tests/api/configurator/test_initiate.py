from http import HTTPStatus
from uuid import uuid4

import pytest

from holmatro_customer_portal.database.models import Category, Configuration
from holmatro_customer_portal.schemas.configurator import ConfigurationStateRes
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from tests.fixtures.database_models_fixture import mocked_category
from tests.fixtures.test_client import TestClientFactory, mock_database

test_client = TestClientFactory.get_client()
configuration_id = uuid4()


def test_initiate():
    mock_database.add(Configuration(id=configuration_id, reference="test"))

    result = test_client.post("/data/configurator/initiate", json={"reference": "test"})
    assert result.status_code == 200

    response = ConfigurationStateRes(**result.json())
    assert response.state_definition.id == ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS.value
    assert response.configuration_data == []

    mock_database.query(Configuration).delete()


@pytest.mark.parametrize(
    "category, status_code",
    [
        (None, HTTPStatus.OK),
        (int(mocked_category.category_id), HTTPStatus.OK),
        (000, HTTPStatus.BAD_REQUEST),
    ],
)
def test_initiate_for_category(category, status_code):
    mock_database.add(mocked_category)

    result = test_client.post("/data/configurator/initiate", json={"reference": "test", "category": category})
    assert result.status_code == status_code.value
    mock_database.query(Configuration).delete()
    mock_database.query(Category).delete()
