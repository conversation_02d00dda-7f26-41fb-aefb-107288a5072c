from datetime import datetime as dt
from uuid import uuid4

from holmatro_customer_portal.database.models import Category, Configuration, ConfigurationProduct
from holmatro_customer_portal.schemas.configurator import ConfigurationRes
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from tests.fixtures.test_client import TestClientFactory, mock_database


def test_get_configurations():
    test_client = TestClientFactory.get_client()
    mock_database.add_all(
        [
            Configuration(id=uuid4(), category=Category(category_id=1), reference="test", started_at=dt.now()),
            Configuration(
                id=uuid4(),
                category=Category(category_id=1),
                reference="test",
                last_updated_state=ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS.value,
                started_at=dt.now(),
                products=[
                    ConfigurationProduct(state_id=ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST.value)
                ],
            ),
            Configuration(
                id=uuid4(),
                category=Category(category_id=1),
                reference="tags test",
                started_at=dt.now(),
                last_updated_state=ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS.value,
                products=[
                    ConfigurationProduct(state_id=ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST.value),
                    ConfigurationProduct(
                        state_id=ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS.value
                    ),
                    ConfigurationProduct(state_id="industrial.lifting.something.flowpanel.settings"),
                ],
            ),
            Configuration(
                id=uuid4(),
                category=Category(category_id=1),
                reference="tags test",
                started_at=dt.now(),
                last_updated_state=ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS.value,
                products=[
                    ConfigurationProduct(state_id="industrial.lifting.something.flowpanel.settings"),
                ],
            ),
        ]
    )

    result = test_client.get("/data/configurator/configurations")

    assert result.status_code == 200

    [config1, config2, config3, config4] = [ConfigurationRes(**r) for r in result.json()]
    assert config1.reference == "test"
    assert config1.progress == 0
    assert config2.progress == 50
    assert len(config3.tags) == 2
    assert "system_components" in config3.tags and "cylinder" in config3.tags
    assert config2.tags == ["cylinder"]
    assert len(config4.tags) == 0

    mock_database.query(Configuration).delete()
