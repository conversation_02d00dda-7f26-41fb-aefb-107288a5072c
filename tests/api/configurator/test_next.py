from unittest.mock import patch
from uuid import uuid4

import pytest

from holmatro_customer_portal.database.models import Category, Configuration
from holmatro_customer_portal.schemas.configurator import ConfigurationStateRes
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from tests.fixtures.configurator import ConfiguratorFactory
from tests.fixtures.my_hm_client import MyHolmatroClientFactoryMock
from tests.fixtures.test_client import TestClientFactory, mock_database, mock_db_user

test_client = TestClientFactory.get_client()
configuration_id = uuid4()


@pytest.mark.parametrize(
    "state, expected_state",
    [
        (
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS.value,
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST.value,
        ),
        (
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST.value,
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING.value,
        ),
        (
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING.value,
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS.value,
        ),
        (
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS.value,
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST.value,
        ),
    ],
)
@patch("holmatro_customer_portal.api.configurator.ConfiguratorFactory", return_value=ConfiguratorFactory())
@patch("holmatro_customer_portal.api.configurator.MyHolmatroClientFactory", MyHolmatroClientFactoryMock)
def test_next(factory: ConfiguratorFactory, state: str, expected_state: str):
    mock_database.add(
        Configuration(id=configuration_id, user=mock_db_user(), reference="test", category=Category(category_id=1))
    )

    request = {"configuration_id": str(configuration_id), "state_id": state, "configuration_data": []}
    with patch(
        "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_cylinder.get_configuration_cylinder_acting_type",
        return_value=None,
    ):
        result = test_client.post("/data/configurator/next", json=request)
    assert result.status_code == 200

    response = ConfigurationStateRes(**result.json())
    assert response.configuration_id == str(configuration_id)
    assert response.category_id == 1
    assert response.state_definition.id == expected_state

    mock_database.query(Configuration).delete()


@patch("holmatro_customer_portal.api.configurator.MyHolmatroClientFactory", MyHolmatroClientFactoryMock)
def test_pump_acting_condition():
    mock_database.add(
        Configuration(id=configuration_id, user=mock_db_user(), reference="test", category=Category(category_id=1))
    )

    request = {
        "configuration_id": str(configuration_id),
        "state_id": ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST.value,
        "configuration_data": [],
    }
    with (
        patch(
            "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_cylinder.get_configuration_cylinder_acting_type",
            return_value=None,
        ),
        patch(
            "holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.PumpSettingsActing.on_enter"
        ),
    ):
        result = test_client.post("/data/configurator/next", json=request)
    assert result.status_code == 200

    response = ConfigurationStateRes(**result.json())
    assert response.configuration_id == str(configuration_id)
    assert response.state_definition.id == ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING.value

    with (
        patch(
            "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_cylinder.get_configuration_cylinder_acting_type",
            return_value="single",
        ),
        patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.PumpSettings.on_enter"),
    ):
        result = test_client.post("/data/configurator/next", json=request)
    assert result.status_code == 200

    response = ConfigurationStateRes(**result.json())
    assert response.configuration_id == str(configuration_id)
    assert response.state_definition.id == ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS.value

    mock_database.query(Configuration).delete()
