from unittest.mock import MagicMock

from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.domain.repositories.catalog_repository import CatalogRepository
from holmatro_customer_portal.services.configurator.states import ConfiguratorState
from holmatro_customer_portal.services.configurator.workflow import Configurator, ConfiguratorFactory
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.my_hm.my_hm_interface import MyHolmatroClientInterface


def create_test_configurator(
    session: Session,
    state: ConfiguratorState,
    configuration: Configuration,
    my_hm_client: MyHolmatroClientInterface,
    catalog_repository: CatalogRepository = None,
) -> Configurator:
    """
    Helper function to create a Configurator instance for tests.
    It provides a default MagicMock for CatalogRepository if not specified.
    """
    if catalog_repository is None:
        catalog_repository = MagicMock(spec=CatalogRepository)

    factory = ConfiguratorFactory()
    return factory.get_configurator_for_state(
        session=session,
        state=state,
        configuration=configuration,
        my_hm_client=my_hm_client,
        catalog_repository=catalog_repository,
    )
