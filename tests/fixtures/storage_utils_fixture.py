import json

from holmatro_customer_portal.utils.env import Env
from tests.fixtures.asset_details_fixture import asset_details_mock
from tests.fixtures.categories_fixture import categories_mock
from tests.fixtures.product_details_fixture import product_details_mock


def setup_storage_assets(client):
    # Create bucket
    client.create_bucket(Bucket=Env.HOLMATRO_ASSETS_BUCKET_NAME.get())

    # Put product-details-1.json
    client.put_object(
        Bucket=Env.HOLMATRO_ASSETS_BUCKET_NAME.get(),
        Key="product-details-1.json",
        Body=json.dumps(product_details_mock),
    )
    client.put_object(
        Bucket=Env.HOLMATRO_ASSETS_BUCKET_NAME.get(),
        Key="categories-1.json",
        Body=json.dumps(categories_mock),
    )

    # Put asset-details.json
    base_path = "assets/"
    uuid = "d2c00eb9-99d4-4e0c-9f4a-8a8f29890729"
    s3_path = f"{base_path}{uuid}/"
    client.put_object(
        Bucket=Env.HOLMATRO_ASSETS_BUCKET_NAME.get(),
        Key=f"{s3_path}asset-details.json",
        Body=json.dumps(asset_details_mock),
    )
    client.put_object(
        Bucket=Env.HOLMATRO_ASSETS_BUCKET_NAME.get(),
        Key=f"{s3_path}E0001136.jpg",
        Body=b"",
    )
