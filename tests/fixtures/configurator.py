import types

from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.domain.repositories.catalog_repository import CatalogRepository
from holmatro_customer_portal.services.configurator.states.base import BaseState, ConfiguratorState
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition
from holmatro_customer_portal.services.configurator.workflow import Configurator
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class ConfiguratorFactory:
    """Creates a configurator for testing."""

    def get_configurator_for_state(
        self,
        session: Session,
        state: ConfiguratorState,
        configuration: Configuration,
        my_hm_client: MyHolmatroPortalApiClient,
        catalog_repository: CatalogRepository,
    ) -> Configurator:
        _logger.debug(f"Creating configurator for state {state}")

        states = [s().definition for s in BaseState.__subclasses__()]
        for s in states:
            """Do not execute the state custom on_enter method during tests."""
            s["on_enter"] = ["load_state_definition"]
            s["on_exit"] = []
        configurator = Configurator(session, states, state, configuration, my_hm_client, catalog_repository)

        for s in BaseState.__subclasses__():
            enter_method_name = s().on_enter_method_name

        for t in BaseTransition.__subclasses__():
            if t.source == state:
                method_name = t().condition_method_name
                setattr(configurator, method_name, types.MethodType(t.condition, configurator))
                configurator.machine.add_transition(**t().definition)

        return configurator
