import re
from datetime import datetime

import pytest
import requests_mock


@pytest.fixture
def my_hm_mock_responses():
    with requests_mock.Mocker() as m:
        # Mock the /general/user endpoint
        m.get(
            "http://example.com/general/user",
            json={
                "User": {
                    "emailAddress": "<EMAIL>",
                    "contactId": "12345",
                    "relationId": "67890",
                    "relationName": "Holmatro",
                    "CurrencyID": "USD",
                    "createdTsUtc": datetime.now().isoformat(),
                    "updatedTsUtc": datetime.now().isoformat(),
                    "LanguageId": "EN",
                    "CountryId": "DE",
                },
                "Attributes": [
                    {"SFProposition": "HIE", "SFCategory": "HolmatroIndustrial", "SFSubCategory": "Lifting:"}
                ],
            },
        )

        product_sales_prices_matcher = re.compile("http://example.com/products/[^/]+/salesprices")

        def product_sales_prices_json_callback(request, _) -> dict:
            article_number = request.path.split("/")[-2]
            return {
                "ArticleNo": article_number,
                "GrossPrice": "200.00",
                "NettPrice": "100.00",
                "Discount%'": "20.000",
                "Currency": "USD",
                "CreatedTsUtc": datetime.now().isoformat(),
                "UpdatedTsUtc": datetime.now().isoformat(),
            }

        # Mock the /products/{article_no}/salesprices endpoint
        m.get(product_sales_prices_matcher, json=product_sales_prices_json_callback)

        def products_prices_json_callback(request, _) -> dict:
            article_numbers = request.query.split("&")
            return {
                "SalesInfo": [
                    {
                        "ArticleNo": article_number.split("=")[-1],
                        "GrossPrice": "200",
                        "NettPrice": "100",
                        "Discount%'": "20.000",
                        "Currency": "USD",
                        "Unit": "piece",
                        "Stock": 100.0,
                        "RestockDays": 7,
                        "CreatedTsUtc": datetime.now().isoformat(),
                        "UpdatedTsUtc": datetime.now().isoformat(),
                    }
                    for article_number in article_numbers
                ]
            }

        # Mock the /products/salesinfo endpoint
        m.get(
            re.compile("http://example.com/products/salesinfo[^/]"),
            json=products_prices_json_callback,
        )

        availability_matcher = re.compile("http://example.com/products/[^/]+/availability")

        def availability_json_callback(request, _) -> dict:
            article_number = request.path.split("/")[-2]
            return {
                "ArticleNo": article_number,
                "Stock": 100.0,
                "RestockDays": 7,
                "createdTsUtc": datetime.now().isoformat(),
                "updatedTsUtc": datetime.now().isoformat(),
            }

        # Mock the /products/{article_no}/availability endpoint
        m.get(availability_matcher, json=availability_json_callback)

        yield m
