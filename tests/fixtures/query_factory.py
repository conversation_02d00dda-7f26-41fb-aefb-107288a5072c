from dataclasses import dataclass
from uuid import UUID, uuid4

from holmatro_customer_portal.database.enums import TextType
from holmatro_customer_portal.database.models import Brand, Product, ProductTextTranslation


@dataclass
class MockProductPreview:
    product_id: UUID
    product_article_number: str
    product_name: str
    asset_url: str
    asset_file_name: str
    category_id: int
    product_classification_id: int


@dataclass
class MockConfiguratorProduct(MockProductPreview):
    quantity: int


@dataclass
class MockProductDetails:
    Product: Product
    ProductTextTranslation: ProductTextTranslation
    product_name: str
    category_id: int


@dataclass
class MockRelatedProduct:
    related_product_id: UUID
    relationship_name: str
    relationship_id: int


class MockQueryFactory:
    """Mock results from database queries"""

    def __init__(self, product_id: UUID = uuid4(), product_name: str = "Mock Product", category_id: int = 7411):
        self.product_id = product_id
        self.product_name = product_name
        self.category_id = category_id

    def get_mock_product_preview(
        self,
        product_article_number: str = "1",
        asset_url: str = "https://example.com/thumbnail.jpg",
        asset_file_name: str = "Asset File Name",
        product_classification_id: int = 2100,
    ):
        return MockProductPreview(
            product_id=self.product_id,
            product_article_number=product_article_number,
            product_name=self.product_name,
            asset_url=asset_url,
            asset_file_name=asset_file_name,
            category_id=self.category_id,
            product_classification_id=product_classification_id,
        )

    def get_mock_configurator_product(
        self,
        product_article_number: str = "1",
        asset_url: str = "https://example.com/thumbnail.jpg",
        asset_file_name: str = "Asset File Name",
        quantity: int = 1,
        product_classification_id: int = 2100,
    ):
        return MockConfiguratorProduct(
            product_id=self.product_id,
            product_article_number=product_article_number,
            product_name=self.product_name,
            asset_url=asset_url,
            asset_file_name=asset_file_name,
            category_id=self.category_id,
            quantity=quantity,
            product_classification_id=product_classification_id,
        )

    def get_mock_product_details(
        self,
        product_article_number: str = "1",
        product_description_type: TextType = TextType.features,
        product_description_value: str = "product features",
    ):
        brand = Brand(brand_name="Holmatro")
        product = Product(id=self.product_id, article_number=product_article_number, brand=brand)
        product_text_translation = ProductTextTranslation(
            type=product_description_type, value=product_description_value
        )

        return MockProductDetails(
            Product=product,
            ProductTextTranslation=product_text_translation,
            product_name=self.product_name,
            category_id=self.category_id,
        )

    def get_related_product(self, relationship_name: str = "Alternative products", relationship_id: int = 1062):
        return MockRelatedProduct(
            related_product_id=self.product_id,
            relationship_name=relationship_name,
            relationship_id=relationship_id,
        )
