import pytest
import requests_mock

from holmatro_customer_portal.utils.env import Env
from tests.fixtures.asset_details_fixture import asset_details_mock
from tests.fixtures.categories_fixture import categories_mock
from tests.fixtures.product_details_fixture import product_details_mock


@pytest.fixture
def http_requests_mock():
    api_url = Env.AZURE_API_URL.get()
    with requests_mock.Mocker() as m:

        def get_asset_details_or_product_details(request, context):
            if "asset-details" in request.url:
                return asset_details_mock
            elif "categories" in request.url:
                return categories_mock
            elif "product-details" in request.url:
                return product_details_mock
            return {}

        m.get(
            f"https://{api_url}/files",
            json=get_asset_details_or_product_details,
            headers={"content-type": "application/json"},
        )

        m.get(
            f"https://{api_url}/files/list_objects",
            json=[
                {"Key": "assets/d2c00eb9-99d4-4e0c-9f4a-8a8f29890729/asset-details.json", "Size": 100},
                {"Key": "assets/d2c00eb9-99d4-4e0c-9f4a-8a8f29890729/E0001136.jpg", "Size": 100},
            ],
            headers={"content-type": "application/json"},
        )

        yield m
