mock_tweakwise_response_properties = {
    "nrofitems": 32656,
    "pagesize": 12,
    "nrofpages": 2722,
    "currentpage": 1,
    "selectedcategory": 0,
}


mock_tweakwise_product_attributes = {
    "ArticleNumber": "10002000-1000-1074",
    "Name": "Gas/Petrol Duo Pump SR 40 PC 2",
    "Price": 0.0,
    "Brand": "Holmatro",
    "Stock": 0,
    "GroupCode": None,
    "Image": None,
    "Url": "/en/rescue/gas/petrol-duo-pump-sr-40-pc-2-0",
    "Categories": [],
    "Attributes": [],
}

mock_tweakwise_facet_settings = {
    "facetid": 1,
    "isvisible": True,
    "attributename": "Categorie",
    "urlkey": "categorie",
    "title": "Categorie",
    "iscollapsible": False,
    "iscollapsed": False,
    "nrofshownattributes": 25,
    "expandtext": "Show more",
    "collapsetext": "Show less",
    "ismultiselect": False,
    "multiselectlogic": None,
    "selectiontype": "link",
    "nrofcolumns": 1,
    "isnrofresultsvisible": True,
    "isinfovisible": False,
    "infotext": None,
    "containsclickpoints": False,
    "containsbuckets": False,
    "source": "CATEGORY",
    "prefix": None,
    "postfix": None,
    "cssclass": None,
}

mock_tweakwise_facet_attributes = {
    "title": "Holmatro EN",
    "isselected": True,
    "nrofresults": 0,
    "attributeid": 1000,
    "url": "?tn_cid=1-1000&tn_fk_material=reinforced%20with%202%20aramide%20inlays&tn_fk_stroke-mm=240-520",
}

mock_tweakwise_lifting_tree = {
    "title": "Lifting",
    "categoryid": 10007411,
    "categorypath": "10007411",
    "children": [
        {
            "title": "Pumps",
            "categoryid": 10001017,
            "categorypath": "10008604-10007411-10001017",
            "children": [
                {
                    "title": "Hand and Foot pumps",
                    "categoryid": 10003118,
                    "categorypath": "10008604-10007411-10001017-10003118",
                    "children": [],
                }
            ],
        },
    ],
}

mock_tweakwise_category_tree = {
    "title": "Industrial Equipment",
    "categoryid": 10008604,
    "categorypath": "10008604",
    "children": [
        mock_tweakwise_lifting_tree,
        {
            "title": "Cutting",
            "categoryid": 10002838,
            "categorypath": "10008604-10002838",
            "children": [
                {
                    "title": "Cutters",
                    "categoryid": 10006921,
                    "categorypath": "10008604-10002838-10006921",
                    "children": [],
                }
            ],
        },
        {"title": "#AC Accessories", "categoryid": 10006633, "categorypath": "10008604-10006633", "children": []},
    ],
}
