from holmatro_customer_portal.schemas.my_hm_api_schema import <PERSON><PERSON>H<PERSON>rice, MYHMProductInfo, MYHMStock
from holmatro_customer_portal.utils.my_hm.my_hm_interface import MyHolmatroClientInterface


class MyHolmatroMockClient(MyHolmatroClientInterface):
    def get_user_info(self):
        return "mock_user_info"

    def get_sales_info(self, article_numbers, currency):
        return [MYHMProductInfo(article_no=no) for no in article_numbers]

    def get_product_sales_prices(self, article_no, currency):
        return MYHMPrice(article_no=article_no)

    def get_stock(self, article_no):
        return MYHMStock(article_no=article_no)


mock_my_hm_client = MyHolmatroMockClient()


class MyHolmatroClientFactoryMock:
    @staticmethod
    def create_client(jwt_token: str) -> str:
        return mock_my_hm_client
