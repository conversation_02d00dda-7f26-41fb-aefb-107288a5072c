from copy import deepcopy
from datetime import date, timedelta
from decimal import Decimal
from uuid import UUID, uuid4

from fixtures.responses_fixtures import mock_asset_group_res, mock_grouped_attribute_res, mock_image_res

from holmatro_customer_portal.schemas.configurator import ConfiguratorProductRes
from holmatro_customer_portal.schemas.response_schema import (
    AssetGroupRes,
    AttributeRes,
    GroupedAttributeRes,
    ImageRes,
    ProductDescription,
    ProductDetailsRes,
    ProductPreviewRes,
    Stock,
)

mock_product_description = {
    "long_description": [],
    "features": [],
    "supplied_with": [],
    "available_on_request": [],
    "accessories": [],
    "additional_information": [],
    "other": [],
}


class MockProductMapper:
    def __init__(self, product_id: UUID = uuid4(), product_name: str = "Mock Product", category_id: int = 7411):
        self.product_id = product_id
        self.product_name = product_name
        self.category_id = category_id

    def get_mock_product_preview(
        self,
        article_number: str = "1",
        thumbnail_link: str = "https://example.com/thumbnail.jpg",
        price_net: Decimal = Decimal(100.00),
        price_gross: Decimal = Decimal(200.00),
        favorite: bool = False,
        stock_status: Stock = Stock.IN_STOCK,
        stock_quantity: float = 100.0,
        available_from: date = date.today() + timedelta(days=7),
        product_attributes: list[AttributeRes] = [],
    ) -> ProductPreviewRes:
        return ProductPreviewRes(
            product_id=self.product_id,
            name=self.product_name,
            category_id=self.category_id,
            article_number=article_number,
            thumbnail_link=thumbnail_link,
            price_net=price_net,
            price_gross=price_gross,
            favorite=favorite,
            stock_status=stock_status,
            stock_quantity=stock_quantity,
            available_from=available_from,
            product_attributes=product_attributes,
        )

    def get_mock_configurator_product(
        self,
        article_number: str = "1",
        thumbnail_link: str = "https://example.com/thumbnail.jpg",
        price_net: Decimal = Decimal(100.00),
        price_gross: Decimal = Decimal(200.00),
        quantity: int = 1,
        favorite: bool = False,
        stock_status: Stock = Stock.IN_STOCK,
        stock_quantity: float = 100.0,
        available_from: date = date.today() + timedelta(days=7),
        product_attributes: list[AttributeRes] = [],
        total_price_net: Decimal = Decimal(100.00),
        total_price_gross: Decimal = Decimal(200.00),
    ) -> ConfiguratorProductRes:
        return ConfiguratorProductRes(
            product_id=self.product_id,
            name=self.product_name,
            category_id=self.category_id,
            article_number=article_number,
            thumbnail_link=thumbnail_link,
            price_net=price_net,
            price_gross=price_gross,
            quantity=quantity,
            favorite=favorite,
            stock_status=stock_status,
            stock_quantity=stock_quantity,
            available_from=available_from,
            product_attributes=product_attributes,
            total_price_net=total_price_net,
            total_price_gross=total_price_gross,
        )

    def get_mock_product_details(
        self,
        article_number: str = "1",
        thumbnail_link: str = "https://example.com/thumbnail.jpg",
        price_net: Decimal = Decimal(100.00),
        price_gross: Decimal = Decimal(200.00),
        favorite: bool = False,
        stock_status: Stock = Stock.IN_STOCK,
        stock_quantity: float = 100.0,
        available_from: date = date.today() + timedelta(days=7),
        product_attributes: list[AttributeRes] = [],
        product_description: dict | None = None,
    ) -> ProductDetailsRes:
        product_description_mock = deepcopy(mock_product_description)
        if product_description:
            for key, value in product_description.items():
                product_description_mock["key"] = value

        return ProductDetailsRes(
            product_id=self.product_id,
            name=self.product_name,
            category_id=self.category_id,
            article_number=article_number,
            thumbnail_link=thumbnail_link,
            price_net=price_net,
            price_gross=price_gross,
            favorite=favorite,
            stock_status=stock_status,
            stock_quantity=stock_quantity,
            available_from=available_from,
            product_attributes=product_attributes,
            description=ProductDescription.model_validate(product_description_mock),
            brand_name="Holmatro",
            alternative_products=[],
            combined_with=[],
            product_images=[ImageRes.model_validate(mock_image_res)],
            attributes=[GroupedAttributeRes.model_validate(mock_grouped_attribute_res)],
            assets=[AssetGroupRes.model_validate(mock_asset_group_res)],
        )
