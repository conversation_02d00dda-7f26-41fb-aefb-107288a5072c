import uuid

from holmatro_customer_portal.database.enums import AssetType, Currency, SystemType
from holmatro_customer_portal.database.models import (
    Asset,
    Attribute,
    AttributeTranslation,
    Category,
    CategoryTranslation,
    Configuration,
    ConfigurationProduct,
    Language,
    Product,
    ProductNameTranslation,
    User,
)
from holmatro_customer_portal.utils.enums import SyncforceCategory

mocked_language = Language(id=uuid.uuid4(), language_code="en")
preferred_mocked_language = Language(id=uuid.uuid4(), language_code="nl")


# Mocking Attribute
mocked_attribute = Attribute(
    id=uuid.uuid4(),
    type="mocked_type",
    is_localized=True,
    is_read_only=False,
    attribute_group_id=1,
    system_type=SystemType.METRIC.value,
    product_id=uuid.uuid4(),
    content="mocked_content",
    unit_type="mocked_unit_type",
    multi_lingual_values=True,
)

# Mocking AttributeTranslation
mocked_translation = AttributeTranslation(
    id=uuid.uuid4(),
    name="mocked_name",
    language_id=mocked_language.id,
    attribute_id=mocked_attribute.id,
    attribute_group_name="mocked_group_name",
    attribute_value="mocked_value",
    attribute_unit="mocked_unit",
)
mocked_attribute_no_content = Attribute(
    id=uuid.uuid4(),
    type="mocked_type",
    is_localized=True,
    attribute_group_id=1,
    is_read_only=False,
    system_type="mocked_system_type",
    product_id=uuid.uuid4(),
    content=None,
    unit_type="mocked_unit_type",
    multi_lingual_values=True,
)

# Mocking AttributeTranslation
mocked_translation_no_content = AttributeTranslation(
    id=uuid.uuid4(),
    name="mocked_name",
    language_id=mocked_language.id,
    attribute_id=mocked_attribute.id,
    attribute_group_name="mocked_group_name",
    attribute_value=None,
    attribute_unit="mocked_unit",
)
mocked_product = Product(
    id=uuid.UUID("171b1491-3ab3-46a8-9347-da4d4651b37a"),
    article_number="1",
    product_names=[ProductNameTranslation(value="product", language_id=mocked_language.id, language=mocked_language)],
    assets=[Asset(id=uuid.UUID("e7378f16-4ec3-497a-8833-8f4045d9660e"), asset_type=AssetType.PRODUCT_MAIN_IMAGE)],
)

mocked_us_user = User(
    id=uuid.uuid4(),
    name="User",
    email="<EMAIL>",
    last_name="Mock",
    main_language_id=mocked_language.id,
    main_language=mocked_language,
    currency=Currency.USD,
    country="US",
    favorite_products=[],
    categories=[Category(category_id="7411"), Category(category_id="2838")],
)

mocked_category = Category(
    id=uuid.uuid4(),
    umid=str(uuid.uuid4()),
    category_id=str(SyncforceCategory.INDUSTRIAL_LIFTING.value),
    translations=[CategoryTranslation(name="cylinder", language=mocked_language)],
)

mocked_configuration = Configuration(
    id=uuid.uuid4(),
    category=mocked_category,
    reference="Configuration",
    quotation_number="1245",
    products=[
        ConfigurationProduct(
            quantity=1, product_id=uuid.UUID("171b1491-3ab3-46a8-9347-da4d4651b37a"), product=mocked_product
        )
    ],
)

# Mocking User
mocked_user = User(
    id=uuid.uuid4(),
    name="User",
    email="<EMAIL>",
    last_name="Mock",
    main_language_id=mocked_language.id,
    main_language=mocked_language,
    currency=Currency.USD,
    relation_name="Holmatro",
    favorite_products=[],
    preferred_language_id=preferred_mocked_language.id,
    preferred_language=preferred_mocked_language,
    configurations=[mocked_configuration],
    categories=[mocked_category],
)
