from unittest.mock import MagicMock

from fastapi.testclient import <PERSON><PERSON>lient
from mock_alchemy.mocking import UnifiedAlchemyMagicMock
from requests_mock import Mocker

from holmatro_customer_portal.database.models import User
from holmatro_customer_portal.domain.repositories.catalog_repository import CatalogRepository
from holmatro_customer_portal.utils.env import Env
from tests.fixtures.database_models_fixture import mocked_user

with <PERSON><PERSON>(real_http=True) as req_mocker:
    url = f"{Env.B2C_BASE_URL.get()}/{Env.B2C_TENANT_ID.get()}/discovery/v2.0/keys?p=b2c_1_myhmb2c_hmportal_sin"
    req_mocker.get(url, json={"keys": [{"kid": "123456"}]})
    from holmatro_customer_portal.main import app
    from holmatro_customer_portal.utils.auth.auth import (
        get_jwt_token,
        get_user,
        get_user_claims,
        get_user_with_favorites,
    )
    from holmatro_customer_portal.utils.database import get_db
    from holmatro_customer_portal.api.dependencies import get_catalog_repository

from holmatro_customer_portal.schemas.auth import UserClaims

mock_database = UnifiedAlchemyMagicMock()
mock_user_claims = UserClaims(**{"name": "Dirk", "family_name": "Janssen", "emails": ["<EMAIL>"]})
mock_jwt_token = "jwt_token"
mock_catalog_repository = MagicMock()


def mock_db_user():
    return mocked_user


class TestClientFactory:
    @staticmethod
    def get_client(
        user: User | None = mocked_user,
        user_claims: UserClaims | None = mock_user_claims,
        jwt_token: str | None = mock_jwt_token,
        db: UnifiedAlchemyMagicMock | None = mock_database,
        catalog_repository: CatalogRepository | None = mock_catalog_repository,
    ) -> TestClient:
        app.dependency_overrides[get_user_claims] = lambda: user_claims
        app.dependency_overrides[get_db] = lambda: db
        app.dependency_overrides[get_jwt_token] = lambda: jwt_token
        app.dependency_overrides[get_user] = lambda: user
        app.dependency_overrides[get_user_with_favorites] = lambda: user
        app.dependency_overrides[get_catalog_repository] = lambda: catalog_repository
        return TestClient(app)
