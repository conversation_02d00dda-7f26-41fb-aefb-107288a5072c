from typing import Generator

import pytest
from sqlalchemy import Engine, create_engine
from sqlalchemy.orm import Session, sessionmaker

from holmatro_customer_portal.database.base import meta


def in_memory_db() -> Engine:
    """
    Creates and returns an in-memory SQLite engine and sets up the schema.
    This is now a regular helper function, not a fixture.
    """
    engine = create_engine("sqlite:///:memory:")
    meta.create_all(engine)
    return engine


def session_factory(db_engine: Engine) -> sessionmaker[Session]:
    return sessionmaker(bind=db_engine, autocommit=False, autoflush=True)


@pytest.fixture
def session() -> Generator[Session, None, None]:
    """
    A pytest fixture that provides a single, isolated database session for a test.
    It now handles the entire lifecycle, from engine creation to disposal.
    """
    engine = in_memory_db()

    factory = session_factory(engine)
    db_session = factory()

    try:
        yield db_session
    finally:
        db_session.close()
        engine.dispose()
