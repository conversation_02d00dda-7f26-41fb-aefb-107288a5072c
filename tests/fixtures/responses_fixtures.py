from datetime import datetime as dt
from datetime import timedelta, timezone
from uuid import uuid4

from holmatro_customer_portal.database.enums import AssetType, SystemType

# Generate a UUID to be used consistently across fixtures
product_id = uuid4()
asset_id = uuid4()
date = dt.now().date().isoformat()

# Mock data for AttributeRes
mock_attribute_res = {
    "content": "Some content",
    "system_type": SystemType.METRIC.value,  # Use the value of the Enum
    "name": "Attribute Name",
    "unit_type": "ml",
}

# Mock data for GroupedAttributeRes
mock_grouped_attribute_res = {
    "grouped_attributes": [mock_attribute_res],
    "group": "Attribute Group Name",
    "group_id": 1,
}

# Mock data for ImageRes
mock_image_res = {
    "url": "https://example.com/image.jpg",
    "type": AssetType.PRODUCT_MAIN_IMAGE.value,  # Use the value of the Enum
}

# Mock data for AssetRes
mock_asset_res = {
    "id": asset_id,
    "file_type": "image/jpeg",
    "url": "https://example.com/image.jpg",
    "size": "1.0",
    "type": AssetType.PRODUCT_MAIN_IMAGE.value,  # Use the value of the Enum
}

# Mock data for AssetGroupRes
mock_asset_group_res = {
    "asset_files": [mock_asset_res],
    "file_name": "Sample File Name",
    "size": "1.0",
    "asset_type": "pdf",
}

# Mock Product Preview
mock_product_preview = {
    "name": "product",
    "product_id": product_id,
    "article_number": "1",
    "thumbnail_link": "https://example.com/thumbnail.jpg",
    "price_net": "100.00",
    "price_gross": "200.00",
    "favorite": True,
    "stock_status": "in_stock",
    "stock_quantity": 10.0,
    "available_from": date,
    "product_attributes": [],
    "category_id": 7411,
}

# Mock Product Descriptions
mock_product_descriptions = {
    "long_description": [],
    "features": [],
    "supplied_with": [],
    "available_on_request": [],
    "accessories": [],
    "additional_information": [],
    "other": [],
}

# Mock Product Details - using the same product_id as mock_product_preview
# FIX IT: remove duplicate keys
mock_product_details = {
    "name": "product",
    "product_id": product_id,  # Use the same UUID as in mock_product_preview
    "article_number": "1",
    "thumbnail_link": None,
    "price_net": "100.00",
    "price_gross": "200.00",
    "stock_status": "in_stock",
    "stock_quantity": 100.0,
    "available_from": date,
    "description": mock_product_descriptions,
    "favorite": True,
    "brand_name": "Holmatro",
    "attributes": {},
    "assets": None,
    "alternative_products": [],
    "combined_with": [],
    "product_images": [mock_image_res],
    "attributes": [mock_grouped_attribute_res],
    "assets": [mock_asset_group_res],
    "product_attributes": [],
    "category_id": 7411,
}

mock_overview_product = {
    "product_id": product_id,
    "name": "product 1",
    "article_number": "1",
    "thumbnail_link": "asset_url",
    "price_net": "100.00",
    "price_gross": "200.00",
    "quantity": 3,
    "favorite": False,
    "stock_status": "in_stock",
    "stock_quantity": 100.0,
    "available_from": (dt.now(timezone.utc).date() + timedelta(days=7)).isoformat(),
    "product_attributes": [],
    "total_price_net": "300.00",
    "total_price_gross": "600.00",
    "category_id": 7411,
}
