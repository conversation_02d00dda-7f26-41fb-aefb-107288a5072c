import pytest
from sqlalchemy.orm import Session

from holmatro_customer_portal.database.models import Category, CategoryAssociation
from holmatro_customer_portal.mappers.product_model_mapper import ProductAttributesMapper
from holmatro_customer_portal.schemas.syncforce_product_schema import Category as SFCategory
from holmatro_customer_portal.utils.database import DatabaseOperations
from tests.factories import SFCategoryFactory


@pytest.fixture
def product_attr_mapper(session: Session):
    """Create a ProductAttributesMapper instance for testing"""
    db_ops = DatabaseOperations(session)
    # Provide empty categories data for testing
    categories_data = {}
    return ProductAttributesMapper(db_ops, categories_data)


def test_map_categories_returns_category_associations(session: Session):
    """Test that map_categories returns CategoryAssociation objects instead of Category objects"""

    # Test data for categories
    test_categories = {
        "test-umid": [
            [
                {
                    "category_umid": "bbf36fac-96a5-4d7d-a94e-10db4fc3350f",
                    "sort_index": 0,
                    "syncforce_index": 10,
                    "product_index": 5,
                    "translations": [],
                    "assets": None,
                },
                {
                    "category_umid": "c002b18e-c522-4215-9bf9-bde3a7864e8a",
                    "sort_index": 1,
                    "syncforce_index": 20,
                    "product_index": 3,
                    "translations": [],
                    "assets": None,
                },
            ]
        ]
    }

    # Create mapper with test categories data
    db_ops = DatabaseOperations(session)
    product_attr_mapper = ProductAttributesMapper(db_ops, test_categories)

    # Call the method
    result = product_attr_mapper.map_categories("test-umid")

    # Verify the result
    assert isinstance(result, list)
    assert len(result) == 2

    # Check that all items are CategoryAssociation objects
    for item in result:
        assert isinstance(item, CategoryAssociation)
        assert hasattr(item, "category")
        assert hasattr(item, "product_sort_order")
        assert isinstance(item.category, Category)


def test_category_association_product_sort_order_mapping(session: Session):
    """Test that product_index from SF categories maps correctly to product_sort_order"""

    test_categories = {
        "test-umid": [
            [
                {
                    "category_umid": "bbf36fac-96a5-4d7d-a94e-10db4fc3350f",
                    "sort_index": 0,
                    "syncforce_index": 10,
                    "product_sort_index": 42,
                    "translations": [],
                    "assets": None,
                },
                {
                    "category_umid": "c002b18e-c522-4215-9bf9-bde3a7864e8a",
                    "sort_index": 1,
                    "syncforce_index": 20,
                    "product_sort_index": 15,
                    "translations": [],
                    "assets": None,
                },
            ]
        ]
    }

    # Create mapper with test categories data
    db_ops = DatabaseOperations(session)
    product_attr_mapper = ProductAttributesMapper(db_ops, test_categories)

    result = product_attr_mapper.map_categories("test-umid")

    # Check that product_sort_order values are correctly mapped
    assert result[0].product_sort_order == 42
    assert result[1].product_sort_order == 15


def test_category_association_without_product_index(session: Session):
    """Test that CategoryAssociation handles missing product_index gracefully"""

    test_categories = {
        "test-umid": [
            [
                {
                    "category_umid": "bbf36fac-96a5-4d7d-a94e-10db4fc3350f",
                    "sort_index": 0,
                    "syncforce_index": 10,
                    # No product_sort_index field
                    "translations": [],
                    "assets": None,
                }
            ]
        ]
    }

    # Create mapper with test categories data
    db_ops = DatabaseOperations(session)
    product_attr_mapper = ProductAttributesMapper(db_ops, test_categories)

    result = product_attr_mapper.map_categories("test-umid")

    # Should handle missing product_sort_index gracefully
    assert len(result) == 1
    assert result[0].product_sort_order is None


def test_category_association_with_multiple_trees(session: Session):
    """Test that CategoryAssociation works with multiple category trees"""

    test_categories = {
        "test-umid": [
            # First tree
            [
                {
                    "category_umid": "bbf36fac-96a5-4d7d-a94e-10db4fc3350f",
                    "sort_index": 0,
                    "syncforce_index": 10,
                    "product_sort_index": 1,
                    "translations": [],
                    "assets": None,
                }
            ],
            # Second tree
            [
                {
                    "category_umid": "c002b18e-c522-4215-9bf9-bde3a7864e8a",
                    "sort_index": 0,
                    "syncforce_index": 20,
                    "product_sort_index": 2,
                    "translations": [],
                    "assets": None,
                }
            ],
        ]
    }

    # Create mapper with test categories data
    db_ops = DatabaseOperations(session)
    product_attr_mapper = ProductAttributesMapper(db_ops, test_categories)

    result = product_attr_mapper.map_categories("test-umid")

    # Should handle multiple trees correctly
    assert len(result) == 2
    assert result[0].product_sort_order == 1
    assert result[1].product_sort_order == 2


def test_sf_category_schema_has_product_sort_index():
    """Test that the SFCategory schema includes the product_sort_index field"""

    # Test that we can create an SFCategory with product_sort_index
    sf_category = SFCategoryFactory.build(
        category_umid="bbf36fac-96a5-4d7d-a94e-10db4fc3350f",
        sort_index=0,
        syncforce_index=10,
        product_sort_index=42,
        translations=[],
        assets=None,
    )

    assert hasattr(sf_category, "product_sort_index")
    assert sf_category.product_sort_index == 42

    # Test that product_sort_index can be None
    sf_category_no_index = SFCategoryFactory.build(
        category_umid="c002b18e-c522-4215-9bf9-bde3a7864e8a",
        sort_index=0,
        syncforce_index=10,
        product_sort_index=None,
        translations=[],
        assets=None,
    )

    assert hasattr(sf_category_no_index, "product_sort_index")
    assert sf_category_no_index.product_sort_index is None
