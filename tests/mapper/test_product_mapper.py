import copy
import json
import uuid
from unittest.mock import call, patch

from mock_alchemy.mocking import UnifiedAlchemyMagicMock
from pydantic import ValidationError
from sqlalchemy.orm import Session

from holmatro_customer_portal.database.models import Brand, Language
from holmatro_customer_portal.database.models import Product
from holmatro_customer_portal.database.models import Product as DBProduct
from holmatro_customer_portal.mappers.product_model_mapper import ProductMapper
from holmatro_customer_portal.schemas.syncforce_product_schema import Product as SFProduct
from holmatro_customer_portal.services.import_product import import_product_to_db
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.transforms import convert_dict_keys_to_snake
from tests.factories import ProductFactory, SFCategoryFactory
from tests.fixtures.categories_fixture import categories_mock
from tests.fixtures.http_requests_fixture import http_requests_mock
from tests.fixtures.product_details_fixture import product_details_mock as product_details_mock_original
from tests.fixtures.storage_utils_fixture import setup_storage_assets

mock_session = UnifiedAlchemyMagicMock(
    data=[([call.query(Language).filter_by(language_code="en").one_or_none()], Language(language_code="en"))]
)
mock_uuid = uuid.uuid4()
product_details_mock = copy.deepcopy(product_details_mock_original)


def fetch_json_data(filepath: str) -> dict:
    with open(filepath, "r") as file:
        data = json.load(file)
    return data


def create_pydantic_product_instance(data: dict) -> SFProduct:
    return SFProduct(**data["product"])


def test_transform_json_to_product_model():
    data = convert_dict_keys_to_snake(product_details_mock)["product"]
    sf_product = SFProduct(**data)

    assert isinstance(sf_product, SFProduct)
    assert sf_product.umid == data["umid"]
    assert sf_product.master_code == data["master_code"]
    assert sf_product.brand.model_dump() == data["brand"]


def custom_upsert_side_effect(model, **kwargs):
    if model == Language:
        return Language(name="en")
    elif isinstance(model, Brand):
        return Brand(brand_name="Holmatro")


def test_update_dirty_flag(s3_client, http_requests_mock, session: Session):
    setup_storage_assets(s3_client)

    data = convert_dict_keys_to_snake(product_details_mock)["product"]
    product_umid = uuid.UUID(data["umid"])

    product_id = uuid.uuid4()
    product = ProductFactory.build(id=product_id, dirty=False, umid=product_umid)
    session.add(product)
    session.commit()

    assert product.dirty is False
    assert product.umid == product_umid

    import_product_to_db(session, product_details_mock, categories_mock)
    updated_product_instance = session.query(Product).filter_by(umid=product_umid).one_or_none()

    assert updated_product_instance.dirty is True
    assert updated_product_instance.id == product_id
    assert len(session.query(Product).all()) == 1


def test_map_pydantic_to_sqlalchemy(s3_client, http_requests_mock, session: Session):
    setup_storage_assets(s3_client)

    data = convert_dict_keys_to_snake(product_details_mock)["product"]
    sf_product = SFProduct(**data)
    related_product_id = sf_product.relations.relation[0].products[0].umid

    mock_session = UnifiedAlchemyMagicMock(
        data=[([call.query(DBProduct.id), call.filter_by(umid=uuid.UUID(related_product_id))], [(("1"))])]
    )

    import_product_to_db(session, product_details_mock, categories_mock)

    product_mapper = ProductMapper(mock_session, categories_mock)

    db_product = product_mapper.map_domain_to_db_model(sf_product, sf_product.id)

    assert isinstance(db_product, DBProduct)
    assert sf_product.article_number == db_product.article_number
    assert sf_product.master_code == db_product.master_code
    assert uuid.UUID(sf_product.umid) == db_product.umid
    assert sf_product.part_nr == db_product.part_nr
    assert sf_product.gtin == db_product.gtin
    assert len(sf_product.texts) == len(db_product.product_texts)
    assert len(sf_product.languages) == len(db_product.languages)

    assert sf_product.assets.asset[0].role_id == db_product.assets[0].role_id
    assert uuid.UUID(sf_product.assets.asset[0].udai) == db_product.assets[0].udai

    assert sf_product.product_attributes[6].value.content == db_product.attributes[6].content
    assert sf_product.product_attributes[6].translations[0].name == db_product.attributes[6].translations[0].name

    assert "1" == db_product.related_products[0].related_product_id

    assert (
        db_product.categories[0].category.translations[0].name
        == categories_mock[sf_product.umid][0][0]["translations"][0]["label"]
    )
    assert (
        db_product.categories[1].category.translations[0].name
        == categories_mock[sf_product.umid][0][1]["translations"][0]["label"]
    )
    assert (
        db_product.categories[2].category.translations[0].name
        == categories_mock[sf_product.umid][0][2]["translations"][0]["label"]
    )

    assert (
        db_product.categories[0].category.translations[0].name
        == categories_mock[sf_product.umid][0][0]["translations"][0]["label"]
    )
    assert (
        db_product.categories[1].category.translations[1].name
        == categories_mock[sf_product.umid][0][1]["translations"][1]["label"]
    )
    assert (
        db_product.categories[2].category.translations[2].name
        == categories_mock[sf_product.umid][0][2]["translations"][2]["label"]
    )

    assert db_product.dirty is True
    assert db_product.deleted_at is None


@patch(
    "holmatro_customer_portal.services.import_product.session_creator",
    return_value=UnifiedAlchemyMagicMock(
        data=[([call.query(Language).filter_by(language_code="en").one_or_none()], Language(language_code="en"))]
    ),
)
def test_import_product_to_db(mock_db, s3_client, http_requests_mock, session: Session):
    base_path = "assets/"
    uuid = "d2c00eb9-99d4-4e0c-9f4a-8a8f29890729"
    s3_path = f"{base_path}{uuid}/"
    setup_storage_assets(s3_client)
    import_product_to_db(session, product_details_mock, categories_mock)


@patch("holmatro_customer_portal.services.import_product.sentry_sdk")
def test_wrong_or_missing_key_product_details(mock_sentry, s3_client, session: Session):
    # Create bucket
    s3_client.create_bucket(Bucket=Env.HOLMATRO_ASSETS_BUCKET_NAME.get())

    # Test invalid Id - should return None and capture ValidationError in Sentry
    product_details_copy = copy.deepcopy(product_details_mock)
    product_details_copy["Product"]["Id"] = "Not an integer"
    result = import_product_to_db(session, product_details_copy, categories_mock)
    assert result is None
    mock_sentry.capture_exception.assert_called_once()
    captured_exception = mock_sentry.capture_exception.call_args[0][0]
    assert isinstance(captured_exception, ValidationError)

    # Reset mock for second test
    mock_sentry.reset_mock()

    # Test missing UMID - should return None and capture ValidationError in Sentry
    product_details_copy = copy.deepcopy(product_details_mock)
    del product_details_copy["Product"]["UMID"]
    result = import_product_to_db(session, product_details_copy, categories_mock)
    assert result is None
    mock_sentry.capture_exception.assert_called_once()
    captured_exception = mock_sentry.capture_exception.call_args[0][0]
    assert isinstance(captured_exception, ValidationError)


def test_convert_dict_keys_to_snake():
    camel_case_data = {"articleNumber": "12345", "masterCode": "67890", "someOtherKey": {"nestedKey": "value"}}

    snake_case_data = convert_dict_keys_to_snake(camel_case_data)

    # Then the returned dictionary should have snake_case keys
    assert snake_case_data == {
        "article_number": "12345",
        "master_code": "67890",
        "some_other_key": {"nested_key": "value"},
    }


def test_process_categories_and_products_parent_child_relationships(session: Session):
    """Test that process_categories_and_products correctly sets up parent-child relationships"""
    # Create test categories with sort_index hierarchy (using proper UUIDs)
    categories = [
        SFCategoryFactory.build(
            category_umid="bbf36fac-96a5-4d7d-a94e-10db4fc3350f",
            sort_index=0,  # Root category
            translations=[],
            assets=None,
        ),
        SFCategoryFactory.build(
            category_umid="c002b18e-c522-4215-9bf9-bde3a7864e8a",
            sort_index=1,  # Child of root (parent at index 0)
            translations=[],
            assets=None,
        ),
        SFCategoryFactory.build(
            category_umid="9fb795e9-305f-4f4e-974e-95dd996f1fbb",
            sort_index=2,  # Child of child (parent at index 1)
            translations=[],
            assets=None,
        ),
    ]

    # Create mapper and process categories
    mapper = ProductMapper(session, categories_mock)

    # Call the method under test - need to access the ProductAttributesMapper
    result_categories = mapper.product_attr_mapper.process_categories_and_products([categories])

    # Commit the transaction to persist categories
    session.commit()

    # Verify we got 3 categories back
    assert len(result_categories) == 3

    # Use the returned categories directly (they are the database objects)
    # Categories are returned in the order they were processed (sorted by sort_index)
    root_category = result_categories[0]  # sort_index 0
    child_category = result_categories[1]  # sort_index 1
    grandchild_category = result_categories[2]  # sort_index 2

    # Verify parent-child relationships
    assert root_category.parent_id is None  # Root has no parent
    assert child_category.parent_id == root_category.id  # Child's parent is root
    assert grandchild_category.parent_id == child_category.id  # Grandchild's parent is child

    # Verify category IDs match what was generated
    assert root_category.category_id == categories[0].category_id
    assert child_category.category_id == categories[1].category_id
    assert grandchild_category.category_id == categories[2].category_id


def test_process_categories_sorting_by_sort_index(session: Session):
    """Test that categories are processed in correct sort_index order"""
    # Create categories in RANDOM order (not sorted by sort_index)
    categories = [
        SFCategoryFactory.build(
            category_umid="aaf36fac-96a5-4d7d-a94e-10db4fc3350f",
            sort_index=2,  # Should be processed last
            translations=[],
            assets=None,
        ),
        SFCategoryFactory.build(
            category_umid="bbf36fac-96a5-4d7d-a94e-10db4fc3350f",
            sort_index=0,  # Should be processed first
            translations=[],
            assets=None,
        ),
        SFCategoryFactory.build(
            category_umid="ccf36fac-96a5-4d7d-a94e-10db4fc3350f",
            sort_index=1,  # Should be processed second
            translations=[],
            assets=None,
        ),
    ]

    # Create mapper and process categories
    mapper = ProductMapper(session, categories_mock)

    # Call the method under test - need to access the ProductAttributesMapper
    result_categories = mapper.product_attr_mapper.process_categories_and_products([categories])

    # Commit the transaction to persist categories
    session.commit()

    # Use the returned categories directly (they are sorted by sort_index)
    category_0 = result_categories[0]  # sort_index 0
    category_1 = result_categories[1]  # sort_index 1
    category_2 = result_categories[2]  # sort_index 2

    # Verify the hierarchy was built correctly despite random input order
    assert category_0.parent_id is None  # sort_index 0: no parent
    assert category_1.parent_id == category_0.id  # sort_index 1: parent is sort_index 0
    assert category_2.parent_id == category_1.id  # sort_index 2: parent is sort_index 1


def test_process_categories_with_gap_in_sort_index(session: Session):
    """Test behavior when there's a gap in sort_index sequence"""
    from holmatro_customer_portal.mappers.product_model_mapper import ProductMapper

    # Create categories with gap in sort_index (missing index 1)
    categories = [
        SFCategoryFactory.build(
            category_umid="ddf36fac-96a5-4d7d-a94e-10db4fc3350f", sort_index=0, translations=[], assets=None  # Root
        ),
        SFCategoryFactory.build(
            category_umid="eef36fac-96a5-4d7d-a94e-10db4fc3350f",
            sort_index=2,  # Gap: no sort_index 1, so this should have no parent
            translations=[],
            assets=None,
        ),
    ]

    # Create mapper and process categories
    mapper = ProductMapper(session, categories_mock)

    # Call the method under test - need to access the ProductAttributesMapper
    result_categories = mapper.product_attr_mapper.process_categories_and_products([categories])

    # Commit the transaction to persist categories
    session.commit()

    # Use the returned categories directly (they are sorted by sort_index)
    category_0 = result_categories[0]  # sort_index 0
    category_2 = result_categories[1]  # sort_index 2 (but second in returned list)

    # Verify relationships
    assert category_0.parent_id is None  # sort_index 0: no parent
    assert category_2.parent_id is None  # sort_index 2: no parent at index 1, so no parent
