import copy
from unittest.mock import patch
from uuid import uuid4

from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Configuration, ConfigurationData, ConfigurationProduct, Product
from holmatro_customer_portal.mappers import ConfiguratorMapper
from holmatro_customer_portal.schemas.configurator import (
    ConfigurationStateReq,
    ConfiguratorProduct,
    ConfiguratorProductRes,
    ConfiguratorStateProducts,
)
from holmatro_customer_portal.services.configurator.states.base import (
    ConfiguratorCylinderStageDataIDs,
    ConfiguratorState,
)
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient
from tests.fixtures.database_models_fixture import mocked_user
from tests.fixtures.responses_fixtures import mock_product_preview

configuration_id = uuid4()
mock_session = UnifiedAlchemyMagicMock()

configuration_response = {
    "configuration_id": str(configuration_id),
    "state_id": ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS.value,
    "configuration_data": [
        {"attribute_id": ConfiguratorCylinderStageDataIDs.CYLINDER_SETTINGS_CALCULATION_WEIGHT.value, "value": 3},
    ],
}


def test_store_configuration_data():
    configuration = Configuration(id=configuration_id)
    config_state = ConfigurationStateReq(**configuration_response)

    [mapped] = ConfiguratorMapper(mock_session).store_configuration_data(configuration, config_state)
    assert isinstance(mapped, ConfigurationData)
    assert mapped.configuration == configuration
    assert mapped.state_id == ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS.value
    assert mapped.attribute_id == ConfiguratorCylinderStageDataIDs.CYLINDER_SETTINGS_CALCULATION_WEIGHT.value
    assert mapped.value == 3


def test_store_configuration_no_product():
    configuration = Configuration(id=configuration_id)
    config_state = ConfigurationStateReq(**configuration_response)

    assert (
        ConfiguratorMapper(mock_session).store_configuration_product(configuration, configuration_state=config_state)
        == []
    )


def test_store_configuration_product_from_request():
    product_id = uuid4()
    mock_session.add(Product(id=product_id))

    configuration = Configuration(id=configuration_id)
    config_state = ConfigurationStateReq(**configuration_response)
    config_state.products = [ConfiguratorProduct(product_id=product_id, quantity=1)]

    result = ConfiguratorMapper(mock_session).store_configuration_product(
        configuration, configuration_state=config_state
    )
    assert isinstance(result, list)
    assert isinstance(result[0], ConfigurationProduct)

    mock_session.query(Product).delete()


def test_store_background_configuration_product_from_request():
    mock_session.add(Product(article_number="111.111"))

    configuration = Configuration(id=configuration_id)
    configuration_state_product = ConfiguratorStateProducts(
        state_id=ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS.value,
        products=[ConfiguratorProduct(article_number="111.111", quantity=1, product_id=None)],
    )

    result = ConfiguratorMapper(mock_session).store_configuration_product(configuration, configuration_state_product)
    assert isinstance(result, list)
    assert isinstance(result[0], ConfigurationProduct)

    mock_session.query(Product).delete()


def test_collect_configuration_data():
    configuration = Configuration(id=configuration_id)
    configuration.data = [
        ConfigurationData(
            configuration=configuration,
            state_id=ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS.value,
            attribute_id=ConfiguratorCylinderStageDataIDs.CYLINDER_SETTINGS_CALCULATION_WEIGHT.value,
            value=3,
        ),
        ConfigurationData(
            configuration=configuration,
            state_id="pump.settings",
            attribute_id="pump.settings.color",
            value="red",
        ),
    ]

    [collected] = ConfiguratorMapper(mock_session).collect_configuration_data(
        configuration, ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS
    )
    assert collected.attribute_id == ConfiguratorCylinderStageDataIDs.CYLINDER_SETTINGS_CALCULATION_WEIGHT.value
    assert collected.value == 3


@patch("holmatro_customer_portal.mappers.configurator_mapper.ProductPreviewMapper.configurator_product_preview_mapper")
def test_collect_configuration_product(mock_get_product_preview):
    mock_my_hm_client = MyHolmatroPortalApiClient("jwt_token")
    configuration = Configuration(id=configuration_id, user=mocked_user)
    product1_id = uuid4()
    product2_id = uuid4()
    mock_session.add_all(
        [
            Product(id=product1_id),
            Product(id=product2_id),
            ConfigurationProduct(
                configuration_id=configuration_id,
                product_id=product1_id,
                state_id=ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN,
                quantity=1,
            ),
            ConfigurationProduct(
                configuration_id=configuration_id,
                product_id=product2_id,
                state_id=ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN,
                quantity=3,
            ),
        ]
    )

    mock_get_product_preview.return_value = [
        ConfiguratorProductRes(
            **copy.deepcopy(mock_product_preview), **{"quantity": 1, "total_price_net": 0.0, "total_price_gross": 0.0}
        ),
        ConfiguratorProductRes(
            **copy.deepcopy(mock_product_preview), **{"quantity": 3, "total_price_net": 0.0, "total_price_gross": 0.0}
        ),
    ]

    collected = ConfiguratorMapper(mock_session).collect_configuration_product(
        configuration, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN, mock_my_hm_client
    )

    assert isinstance(collected, list)
    assert len(collected) == 2
    assert isinstance(collected[0], ConfiguratorProductRes)
    assert collected[0].quantity == 1
    assert collected[1].quantity == 3

    mock_session.query(Product).delete()
    mock_session.query(ConfigurationProduct).delete()
