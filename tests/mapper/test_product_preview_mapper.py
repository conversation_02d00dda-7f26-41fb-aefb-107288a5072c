import copy
import uuid
from collections import namedtuple
from datetime import datetime
from unittest.mock import MagicMock, call, patch

import pytest
from _decimal import Decimal
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Attribute, AttributeTranslation
from holmatro_customer_portal.mappers.product_preview_mapper import ProductPreviewMapper
from holmatro_customer_portal.schemas.my_hm_api_schema import MYHMProductInfo
from holmatro_customer_portal.schemas.response_schema import Stock
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient
from tests.fixtures.database_models_fixture import mocked_attribute, mocked_translation, mocked_user
from tests.fixtures.my_hm_fixture import my_hm_mock_responses
from tests.fixtures.product_mapper_factory import MockProductMapper
from tests.fixtures.query_factory import MockQueryFactory

product_id = uuid.uuid4()
user_id = uuid.uuid4()


@pytest.mark.parametrize(
    "stock_response, expected_result",
    [
        (
            {
                "ArticleNo": "1",
                "GrossPrice": "200.00",
                "NettPrice": "100.00",
                "Discount%'": "20.000",
                "Currency": "USD",
                "Unit": "piece",
                "Stock": 100.0,
                "RestockDays": 7,
                "CreatedTsUtc": datetime.now().isoformat(),
                "UpdatedTsUtc": datetime.now().isoformat(),
            },
            {
                "product_id": product_id,
                "name": "product 1",
                "article_number": "1",
                "thumbnail_link": "thumbnail_link",
                "price_net": Decimal("100.0"),
                "price_gross": Decimal("200.0"),
                "favorite": True,
                "stock_status": Stock.IN_STOCK,
                "stock_quantity": 10.0,
                "available_from": datetime.now().date().isoformat(),
                "product_attributes": [],
                "category_id": 7411,
            },
        ),
        (
            {
                "ArticleNo": "1",
                "GrossPrice": "200.00",
                "NettPrice": "100.00",
                "Discount%'": "20.000",
                "Currency": "USD",
                "Unit": "piece",
                "Stock": 0.0,
                "RestockDays": 7,
                "CreatedTsUtc": datetime.now().isoformat(),
                "UpdatedTsUtc": datetime.now().isoformat(),
            },
            MockProductMapper(product_id=product_id)
            .get_mock_product_preview(favorite=True, stock_status=Stock.NO_STOCK)
            .model_dump(),
        ),
    ],
)
@patch("holmatro_customer_portal.services.get_categories.query_category_ids", return_value=[("1", 1058)])
@patch("holmatro_customer_portal.mappers.product_preview_mapper.ProductPreviewMapper.is_product_favorite")
def test_product_preview_mapper(
    mock_is_favorite: MagicMock,
    _: MagicMock,
    stock_response: dict | None,
    expected_result: dict,
    my_hm_mock_responses,
):
    mock_session = UnifiedAlchemyMagicMock(
        data=[
            (
                [
                    call.query(Attribute, AttributeTranslation),
                ],
                [(mocked_attribute, mocked_translation)],
            ),
        ]
    )
    mock_user = copy.deepcopy(mocked_user)
    mock_user.id = user_id
    mock_is_favorite.return_value = True

    query_result = [MockQueryFactory(product_id=product_id).get_mock_product_preview()]
    my_hm_mock_responses.get(
        "http://example.com/products/salesinfo",
        json={"SalesInfo": [stock_response]},
    )
    mock_my_hm_client = MyHolmatroPortalApiClient("jwt_token")

    res = ProductPreviewMapper(mock_session, mock_my_hm_client).product_preview_mapper(mock_user, query_result)

    assert len(res[0].model_dump()) == len(expected_result)
    assert res[0].product_id == product_id
    assert res[0].article_number == "1"
    assert res[0].name == "Mock Product"
    assert res[0].thumbnail_link == "https://example.com/thumbnail.jpg"
    assert res[0].price_net == 100.00
    assert res[0].price_gross == 200.00
    assert res[0].favorite is True
    assert res[0].stock_status == expected_result["stock_status"]
    assert len(res[0].product_attributes) == 1
    assert type(res) is list


def test_configurator_product_preview_mapper(my_hm_mock_responses):
    mock_session = UnifiedAlchemyMagicMock()
    mock_my_hm_client = MyHolmatroPortalApiClient("jwt_token")
    ProductPreview = namedtuple(
        "ProductPreview",
        "product_id, quantity, product_article_number, product_name, asset_url, asset_file_name, category_id",
    )
    query_result = [ProductPreview(uuid.uuid4(), 3, "1", "product 1", "asset_url", "asset_file_name", 7411)]

    res = ProductPreviewMapper(mock_session, mock_my_hm_client).configurator_product_preview_mapper(
        mocked_user, query_result  # type: ignore
    )

    assert len(res) == 1
    assert res[0].quantity == 3
    assert res[0].total_price_net == Decimal("300.00")
    assert res[0].total_price_gross == Decimal("600.00")
    assert res[0].stock_status == Stock.IN_STOCK


@pytest.mark.parametrize(
    "query_result",
    [
        pytest.param(MockQueryFactory().get_mock_product_preview(), id="Product preview"),
        pytest.param(MockQueryFactory().get_mock_configurator_product(), id="Configurator product"),
        pytest.param(MockQueryFactory().get_mock_product_details(), id="Product details"),
    ],
)
def test_get_sales_prices_method(query_result, my_hm_mock_responses):
    mock_session = UnifiedAlchemyMagicMock()
    mock_my_hm_client = MyHolmatroPortalApiClient("jwt_token")

    res = ProductPreviewMapper(mock_session, mock_my_hm_client)._get_sales_info(mocked_user, [query_result])

    assert isinstance(res, dict)
    assert isinstance(res["1"], MYHMProductInfo)
