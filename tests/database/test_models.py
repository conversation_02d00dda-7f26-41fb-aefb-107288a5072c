import pytest
from sqlalchemy.orm import Session

from holmatro_customer_portal.database.models import (
    Category,
    CategoryAssociation,
    CategoryTranslation,
    Language,
    Product,
    ProductNameTranslation,
)
from tests.factories import CategoryFactory, LanguageFactory, ProductFactory
from tests.fixtures.database import session


class TestCategoryAssociationOrdering:
    """Test CategoryAssociation ordering by product_sort_order."""

    @pytest.fixture(autouse=True)
    def _pass_fixtures(self, session):
        self.session = session

    def test_product_categories_ordered_by_sort_order(self):
        """Test that Product.categories returns CategoryAssociation objects ordered by product_sort_order."""
        # Create a product
        product = ProductFactory.build(product_id=1, article_number="TEST001")
        self.session.add(product)
        self.session.commit()

        # Create categories
        category1 = CategoryFactory.build(category_id="CAT001")
        category2 = CategoryFactory.build(category_id="CAT002")
        category3 = CategoryFactory.build(category_id="CAT003")
        self.session.add_all([category1, category2, category3])
        self.session.commit()

        # Create CategoryAssociations with different sort orders (intentionally out of order)
        assoc1 = CategoryAssociation(
            category_id=category1.id, product_id=product.id, product_sort_order=3  # Should be third
        )
        assoc2 = CategoryAssociation(
            category_id=category2.id, product_id=product.id, product_sort_order=1  # Should be first
        )
        assoc3 = CategoryAssociation(
            category_id=category3.id, product_id=product.id, product_sort_order=2  # Should be second
        )

        self.session.add_all([assoc1, assoc2, assoc3])
        self.session.commit()

        # Refresh to load relationships
        self.session.refresh(product)

        # Test that categories are returned in sort order
        categories = product.categories
        assert len(categories) == 3

        # Should be ordered by product_sort_order: 1, 2, 3
        assert categories[0].product_sort_order == 1
        assert categories[0].category_id == category2.id

        assert categories[1].product_sort_order == 2
        assert categories[1].category_id == category3.id

        assert categories[2].product_sort_order == 3
        assert categories[2].category_id == category1.id

    def test_product_categories_with_null_sort_order(self):
        """Test that CategoryAssociations with NULL sort_order appear last."""
        # Create a product
        product = ProductFactory.build(product_id=2, article_number="TEST002")
        self.session.add(product)
        self.session.commit()

        # Create categories
        category1 = CategoryFactory.build(category_id="CAT004")
        category2 = CategoryFactory.build(category_id="CAT005")
        category3 = CategoryFactory.build(category_id="CAT006")
        self.session.add_all([category1, category2, category3])
        self.session.commit()

        # Create CategoryAssociations with mixed sort orders including NULL
        assoc1 = CategoryAssociation(
            category_id=category1.id, product_id=product.id, product_sort_order=None  # Should be last
        )
        assoc2 = CategoryAssociation(
            category_id=category2.id, product_id=product.id, product_sort_order=1  # Should be first
        )
        assoc3 = CategoryAssociation(
            category_id=category3.id,
            product_id=product.id,
            product_sort_order=None,  # Should be last (along with assoc1)
        )

        self.session.add_all([assoc1, assoc2, assoc3])
        self.session.commit()

        # Refresh to load relationships
        self.session.refresh(product)

        # Test ordering
        categories = product.categories
        assert len(categories) == 3

        # First should be the one with sort_order=1
        assert categories[0].product_sort_order == 1
        assert categories[0].category_id == category2.id

        # The remaining two should have NULL sort_order (order among NULLs is not guaranteed)
        null_sort_orders = [cat.product_sort_order for cat in categories[1:]]
        assert all(sort_order is None for sort_order in null_sort_orders)

    def test_manual_query_with_ordering(self):
        """Test manual queries with explicit ordering work correctly."""
        # Create a category
        category = CategoryFactory.build(category_id="CAT007")
        self.session.add(category)
        self.session.commit()

        # Create products
        product1 = ProductFactory.build(product_id=3, article_number="TEST003")
        product2 = ProductFactory.build(product_id=4, article_number="TEST004")
        product3 = ProductFactory.build(product_id=5, article_number="TEST005")
        self.session.add_all([product1, product2, product3])
        self.session.commit()

        # Create CategoryAssociations with different sort orders
        assoc1 = CategoryAssociation(category_id=category.id, product_id=product1.id, product_sort_order=2)
        assoc2 = CategoryAssociation(category_id=category.id, product_id=product2.id, product_sort_order=1)
        assoc3 = CategoryAssociation(category_id=category.id, product_id=product3.id, product_sort_order=None)

        self.session.add_all([assoc1, assoc2, assoc3])
        self.session.commit()

        # Test manual query with explicit ordering
        ordered_associations = (
            self.session.query(CategoryAssociation)
            .filter(CategoryAssociation.category_id == category.id)
            .order_by(CategoryAssociation.product_sort_order.asc().nulls_last())
            .all()
        )

        assert len(ordered_associations) == 3

        # Should be ordered: sort_order 1, 2, then NULL
        assert ordered_associations[0].product_sort_order == 1
        assert ordered_associations[0].product_id == product2.id

        assert ordered_associations[1].product_sort_order == 2
        assert ordered_associations[1].product_id == product1.id

        assert ordered_associations[2].product_sort_order is None
        assert ordered_associations[2].product_id == product3.id

    def test_duplicate_sort_orders_handled_gracefully(self):
        """Test that duplicate sort orders don't break the ordering."""
        # Create a product
        product = ProductFactory.build(product_id=6, article_number="TEST006")
        self.session.add(product)
        self.session.commit()

        # Create categories
        category1 = CategoryFactory.build(category_id="CAT008")
        category2 = CategoryFactory.build(category_id="CAT009")
        category3 = CategoryFactory.build(category_id="CAT010")
        self.session.add_all([category1, category2, category3])
        self.session.commit()

        # Create CategoryAssociations with duplicate sort orders
        assoc1 = CategoryAssociation(category_id=category1.id, product_id=product.id, product_sort_order=1)
        assoc2 = CategoryAssociation(
            category_id=category2.id, product_id=product.id, product_sort_order=1  # Same as assoc1
        )
        assoc3 = CategoryAssociation(category_id=category3.id, product_id=product.id, product_sort_order=2)

        self.session.add_all([assoc1, assoc2, assoc3])
        self.session.commit()

        # Refresh to load relationships
        self.session.refresh(product)

        # Test that ordering still works (duplicates will be in arbitrary order among themselves)
        categories = product.categories
        assert len(categories) == 3

        # First two should have sort_order=1
        assert categories[0].product_sort_order == 1
        assert categories[1].product_sort_order == 1

        # Last should have sort_order=2
        assert categories[2].product_sort_order == 2
        assert categories[2].category_id == category3.id

    def test_empty_categories_list(self):
        """Test that a product with no categories returns an empty list."""
        # Create a product with no categories
        product = ProductFactory.build(product_id=7, article_number="TEST007")
        self.session.add(product)
        self.session.commit()

        # Refresh to load relationships
        self.session.refresh(product)

        # Test empty categories
        categories = product.categories
        assert len(categories) == 0
        assert isinstance(categories, list)
