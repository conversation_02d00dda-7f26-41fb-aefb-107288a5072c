import datetime
from decimal import Decimal
from unittest.mock import Mock, patch

import pytest
from pydantic import ValidationError

from holmatro_customer_portal.database.enums import Currency
from holmatro_customer_portal.database.models import ConfigurationProduct, Product
from holmatro_customer_portal.schemas.my_hm_api_schema import MY<PERSON><PERSON><PERSON>, MYHMStock
from holmatro_customer_portal.schemas.quotation import QuoteItem
from holmatro_customer_portal.schemas.response_schema import Stock
from holmatro_customer_portal.services.configurator.get_quotation import (
    get_quotation,
    get_quotation_items,
    group_by_quantity,
)
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient
from tests.fixtures.database_models_fixture import mocked_configuration, mocked_user
from tests.fixtures.my_hm_fixture import my_hm_mock_responses


@patch("holmatro_customer_portal.services.configurator.get_quotation._load_pdf_template")
@patch("holmatro_customer_portal.services.configurator.get_quotation.pdfkit.from_string")
def test_get_quotation(_, mock_load_pdf_template, my_hm_mock_responses):
    mock_template = Mock()
    mock_load_pdf_template.return_value = mock_template

    my_holmatro_client = MyHolmatroPortalApiClient("124")
    get_quotation(mocked_configuration, my_holmatro_client, mocked_user)

    assert mock_template.render.call_args.kwargs["quote"]["title"] == "Quotation"
    assert mock_template.render.call_args.kwargs["quote"]["reference_value"] == mocked_user.configurations[0].reference
    assert mock_template.render.call_args.kwargs["quote"]["details_company"] == mocked_user.relation_name

    assert mock_template.render.call_args.kwargs["quote_items"][0]["quantity"] == str(
        mocked_user.configurations[0].products[0].quantity
    )
    assert mock_template.render.call_args.kwargs["quote_items"][0]["net_price"] == "100.00"


@patch("holmatro_customer_portal.services.configurator.get_quotation.pdfkit.from_string")
def test_get_quotation_missing_values(mock_pdf_kit, my_hm_mock_responses):
    my_holmatro_client = MyHolmatroPortalApiClient("124")
    mocked_user.email = None

    with pytest.raises(ValidationError):
        get_quotation(mocked_configuration, my_holmatro_client, mocked_user)


def test_group_by_quantity():
    products = [
        ConfigurationProduct(product_id="1", quantity=1),
        ConfigurationProduct(product_id="1", quantity=3),
        ConfigurationProduct(product_id="2", quantity=1),
    ]
    res = group_by_quantity(products)

    assert len(res) == 2
    assert res[0].quantity == 4


@patch("holmatro_customer_portal.services.configurator.get_quotation.name_translation", return_value="")
def test_get_quotation_items(_):
    utc_now = datetime.datetime.utcnow()

    configuration_products = [
        ConfigurationProduct(quantity=3, product=Product(article_number="101.102.103", product_names=[])),
        ConfigurationProduct(quantity=1, product=Product(article_number="101.102.104", product_names=[])),
    ]
    product_prices = {
        "101.102.103": MYHMPrice(
            article_no="101.102.103",
            gross_price=Decimal(10),
            nett_price=Decimal(12),
            discount=Decimal(5),
            currency=Currency.EUR,
            created_ts_utc=utc_now,
            updated_ts_utc=utc_now,
        ),
        "101.102.104": MYHMPrice(
            article_no="101.102.104",
            gross_price=Decimal(11),
            nett_price=Decimal(13),
            discount=Decimal(0),
            currency=Currency.EUR,
            created_ts_utc=utc_now,
            updated_ts_utc=utc_now,
        ),
    }
    product_stock = {
        "101.102.103": MYHMStock(
            article_no="101.102.103",
            stock_status=Stock.IN_STOCK,
            stock=float(2),
            restock_days=None,
            created_ts_utc=utc_now,
            updated_ts_utc=utc_now,
        ),
        "101.102.104": MYHMStock(
            article_no="101.102.104",
            stock_status=Stock.IN_STOCK,
            stock=float(1),
            restock_days=None,
            created_ts_utc=utc_now,
            updated_ts_utc=utc_now,
        ),
    }

    result = get_quotation_items(configuration_products, product_prices, product_stock)

    assert len(result) == 2
    assert result[0] == QuoteItem(
        quantity="3",
        product_name="",
        product_nr="101.102.103",
        availability="no stock",  # Not in stock because quantity (3) > stock_quantity (1)
        unit_price="10",
        discount="5",
        net_price="12",
        line_total="36",
    )
    assert result[1] == QuoteItem(
        quantity="1",
        product_name="",
        product_nr="101.102.104",
        availability="in stock",
        unit_price="11",
        discount=None,
        net_price="13",
        line_total="13",
    )
