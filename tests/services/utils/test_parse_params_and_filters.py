import pytest

from holmatro_customer_portal.schemas.response_schema import SearchFiltersReq, SearchParamsReq
from holmatro_customer_portal.services.utils.parse_params_and_filters import parse_params_and_filters


@pytest.mark.parametrize(
    "params, filters, expected_result",
    [
        (
            SearchParamsReq(
                search_query="chain",
                page_number=1,
                category_path="1",
                products_per_page=6,
                filter_template_id=None,
                category_id=1,
            ),
            None,
            {},
        ),
        (
            SearchParamsReq(
                search_query="chain",
                page_number=1,
                category_path="1",
                products_per_page=12,
                filter_template_id=None,
                category_id=1,
            ),
            SearchFiltersReq(filters={"hie-max-working-pressure-bar": "720 bar / 72 MPa"}),
            {
                "tn_fk_hie-max-working-pressure-bar": "720 bar / 72 MPa",
            },
        ),
        (
            SearchParamsReq(
                search_query="chain",
                page_number=1,
                category_path="1",
                products_per_page=12,
                filter_template_id=None,
                category_id=1,
            ),
            SearchFiltersReq(
                filters={
                    "cylinder-type": ["high tonnage", "hollow plunger"],
                    "return-type": ["gravity", "hydraulic"],
                    "tonnage-t": [30, 50],
                    "acting-type": "double",
                }
            ),
            {
                "tn_fk_cylinder-type": "high tonnage|hollow plunger",
                "tn_fk_return-type": "gravity|hydraulic",
                "tn_fk_tonnage-t": "30|50",
                "tn_fk_acting-type": "double",
            },
        ),
    ],
)
def test_parse_params_and_filters(params: SearchParamsReq, filters: SearchFiltersReq | None, expected_result):
    result = parse_params_and_filters(params, filters, True)

    assert isinstance(result, dict)
    assert result == {"tn_q": "chain", "tn_p": 1, "tn_cid": "1", "tn_ps": 1000, "tn_ft": None, **expected_result}


def test_parse_params_and_filters_no_default():
    params = SearchParamsReq(
        search_query="chain",
        page_number=1,
        category_path="1",
        products_per_page=12,
        filter_template_id=None,
        category_id=1,
    )

    result = parse_params_and_filters(params, None)

    assert isinstance(result, dict)
    assert result == {
        "tn_q": "chain",
        "tn_p": 1,
        "tn_cid": "1",
        "tn_ps": 12,
        "tn_ft": None,
    }
