from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest
from fixtures.product_mapper_factory import MockProductMapper
from fixtures.query_factory import MockQueryFactory
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.schemas.response_schema import ProductPreviewRes, ProductsRes, SearchParamsReq
from holmatro_customer_portal.services.get_products_by_search import get_products_by_search_handle
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import (
    Facets,
    FacetsAttributes,
    FacetSettings,
    Product,
    ProductsResponse,
    ResponseProperties,
)
from tests.fixtures.database_models_fixture import mocked_user
from tests.fixtures.my_hm_fixture import my_hm_mock_responses
from tests.fixtures.test_client import mock_catalog_repository
from tests.fixtures.tweakwise_fixtures import (
    mock_tweakwise_facet_attributes,
    mock_tweakwise_facet_settings,
    mock_tweakwise_response_properties,
)

product_id_1 = uuid4()
product_id_2 = uuid4()

mock_tweakwise_facets = Facets(
    facet_settings=FacetSettings(**mock_tweakwise_facet_settings),
    attributes=[FacetsAttributes(**mock_tweakwise_facet_attributes)],
)


@pytest.mark.parametrize(
    "params, tweakwise_items_result, db_products, mapped_products, properties",
    [
        pytest.param(
            SearchParamsReq(search_query="chain"),
            [],
            [],
            [],
            ResponseProperties(**mock_tweakwise_response_properties),
            id="Test no products found in Tweakwise",
        ),
        pytest.param(
            SearchParamsReq(search_query="chain"),
            [Product(item_no="1", title="product", brand="Holmatro")],
            [],
            [],
            ResponseProperties(no_of_items=0, page_size=12, no_of_pages=0, current_page=1, category_id=None),
            id="Test no products found in the database",
        ),
        pytest.param(
            SearchParamsReq(search_query="chain", category_id=7411),
            [
                Product(item_no="1", title="product", brand="Holmatro"),
                Product(item_no="2", title="product 2", brand="Holmatro"),
            ],
            [MockQueryFactory(product_id=product_id_1).get_mock_product_preview()],
            [MockProductMapper(product_id=product_id_1).get_mock_product_preview()],
            ResponseProperties(no_of_items=1, page_size=12, no_of_pages=1, current_page=1, category_id=7411),
            id="Test search with category id",
        ),
        pytest.param(
            SearchParamsReq(search_query="chain"),
            [
                Product(item_no="1", title="product", brand="Holmatro"),
                Product(item_no="2", title="product 2", brand="Holmatro"),
                Product(item_no="2", title="product 2", brand="Holmatro"),
            ],
            [
                MockQueryFactory(product_id=product_id_1, category_id=1).get_mock_product_preview(),
                MockQueryFactory(product_id=product_id_2, category_id=2).get_mock_product_preview(),
                MockQueryFactory(product_id=product_id_2, category_id=2).get_mock_product_preview(),
            ],
            [
                MockProductMapper(product_id=product_id_2, category_id=2).get_mock_product_preview(),
                MockProductMapper(product_id=product_id_2, category_id=2).get_mock_product_preview(),
            ],
            ResponseProperties(no_of_items=2, page_size=12, no_of_pages=1, current_page=1, category_id=2),
            id="Test search without category id - one product each category",
        ),
    ],
)
@patch("holmatro_customer_portal.api.products.get_user")
@patch("holmatro_customer_portal.services.get_products_by_search.get_db_products")
def test_get_products_by_search_handle(
    mock_get_db_products: MagicMock,
    mock_get_user: MagicMock,
    params: SearchParamsReq,
    tweakwise_items_result: ProductsResponse,
    db_products: list[Product],
    mapped_products: list[ProductPreviewRes],
    properties: ResponseProperties,
    my_hm_mock_responses,
):
    session = UnifiedAlchemyMagicMock(name="Search Database Mock")
    mock_my_hm_client = MyHolmatroPortalApiClient("jwt_token")

    mock_catalog_repository.get_products.return_value = ProductsResponse(
        items=tweakwise_items_result, properties=mock_tweakwise_response_properties, redirects=[]
    )
    mock_get_db_products.return_value = db_products
    # mock_get_mapped_products.return_value = mapped_products
    mock_get_user.return_value = mocked_user

    result = get_products_by_search_handle(
        mock_catalog_repository, params, None, mocked_user, session, mock_my_hm_client
    )

    assert result.products == mapped_products
    assert result.properties == properties
    assert isinstance(result, ProductsRes)
