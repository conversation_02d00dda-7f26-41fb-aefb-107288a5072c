from unittest.mock import MagicMock, Mock, patch

import pytest
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.schemas.response_schema import (
    ParamsReq,
    ProductPreviewRes,
    ProductsWithFiltersRes,
    SearchFiltersReq,
)
from holmatro_customer_portal.services.get_products import _filter_out_unwanted_filters_and_attributes  # noqa
from holmatro_customer_portal.services.get_products import get_products_with_filters_handle
from holmatro_customer_portal.utils.enums import SelectionType
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import Facets, Navigation, Product, ResponseProperties
from tests.fixtures.database_models_fixture import mocked_user
from tests.fixtures.responses_fixtures import mock_product_preview
from tests.fixtures.tweakwise_fixtures import (
    mock_tweakwise_facet_attributes,
    mock_tweakwise_facet_settings,
    mock_tweakwise_response_properties,
)


class TestGetProducts:

    @pytest.mark.parametrize(
        "params, filters, tweakwise_response, db_response, properties",
        [
            pytest.param(
                ParamsReq(category_id=7411),
                [],
                Navigation(facets=[], items=[], properties=mock_tweakwise_response_properties, redirects=[]),
                [],
                ResponseProperties(
                    no_of_items=32656,
                    page_size=12,
                    no_of_pages=2722,
                    current_page=1,
                    category_id=7411,
                ),
                id="Test no products",
            ),
            pytest.param(
                ParamsReq(category_path="10000011", category_id=11),
                SearchFiltersReq(filters={"material": "aluminium"}),
                Navigation(
                    facets=[
                        Facets(
                            facet_settings=mock_tweakwise_facet_settings,
                            attributes=[mock_tweakwise_facet_attributes],
                        )
                    ],
                    properties=mock_tweakwise_response_properties,
                    items=[Product(item_no="1", title="product", brand="Holmatro")],
                    redirects=[],
                ),
                [ProductPreviewRes(**mock_product_preview)],
                ResponseProperties(
                    no_of_items=32656,
                    page_size=12,
                    no_of_pages=2722,
                    current_page=1,
                    category_id=11,
                ),
                id="Happy path",
            ),
        ],
    )
    @patch("holmatro_customer_portal.services.get_products.ProductPreviewMapper.product_preview_mapper")
    @patch("holmatro_customer_portal.services.get_products.get_db_products")
    def test_get_products_with_filters(
        self,
        mock_get_db_products: MagicMock,
        mock_get_mapped_products: MagicMock,
        params: ParamsReq,
        filters: SearchFiltersReq | None,
        tweakwise_response: Navigation,
        db_response: list[ProductPreviewRes],
        properties: ResponseProperties,
        catalog_repo_mock: MagicMock,
    ):
        session = UnifiedAlchemyMagicMock()
        mock_my_hm_client = MyHolmatroPortalApiClient("jwt_token")

        catalog_repo_mock.get_navigation.return_value = tweakwise_response
        mock_get_db_products.return_value = db_response, properties
        mock_get_mapped_products.return_value = db_response

        result = get_products_with_filters_handle(
            catalog_repo_mock, params, filters, mocked_user, session, mock_my_hm_client
        )

        assert result.products == db_response
        assert result.properties == properties
        assert isinstance(result, ProductsWithFiltersRes)

    @pytest.mark.parametrize(
        "params, filters",
        [
            pytest.param(
                ParamsReq(category_id=7411),
                None,
                id="Basic params with no filters",
            ),
            pytest.param(
                ParamsReq(category_id=7411, page_number=2, products_per_page=24),
                None,
                id="Params with pagination, no filters",
            ),
            pytest.param(
                ParamsReq(category_id=7411, search_query="pump"),
                None,
                id="Params with search query, no filters",
            ),
            pytest.param(
                ParamsReq(category_path="10008604-10007411", category_id=7411),
                SearchFiltersReq(filters={"material": "aluminium"}),
                id="Params with category path and single filter",
            ),
            pytest.param(
                ParamsReq(category_id=7411, filter_template_id=123),
                SearchFiltersReq(filters={"material": "aluminium", "pressure": "700 bar"}),
                id="Params with template and multiple filters",
            ),
        ],
    )
    @patch("holmatro_customer_portal.services.get_products.ProductPreviewMapper.product_preview_mapper")
    @patch("holmatro_customer_portal.services.get_products.get_db_products")
    def test_catalog_repository_get_navigation_called_with_correct_params(
        self,
        mock_get_db_products: MagicMock,
        mock_get_mapped_products: MagicMock,
        params: ParamsReq,
        filters: SearchFiltersReq | None,
        catalog_repo_mock: MagicMock,
    ):
        """Test that catalog_repository.get_navigation is called with the correct parameters"""
        session = UnifiedAlchemyMagicMock()
        mock_my_hm_client = MyHolmatroPortalApiClient("jwt_token")

        # Mock empty navigation response
        catalog_repo_mock.get_navigation.return_value = Navigation(
            facets=[], items=[], properties=mock_tweakwise_response_properties, redirects=[]
        )
        mock_get_db_products.return_value = [], ResponseProperties(
            no_of_items=0, page_size=12, no_of_pages=0, current_page=1, category_id=params.category_id
        )

        get_products_with_filters_handle(catalog_repo_mock, params, filters, mocked_user, session, mock_my_hm_client)

        # Verify get_navigation was called exactly once with the correct parameters
        catalog_repo_mock.get_navigation.assert_called_once_with(mocked_user, params, filters)

    @patch("holmatro_customer_portal.services.get_products.ProductPreviewMapper.product_preview_mapper")
    @patch("holmatro_customer_portal.services.get_products.get_db_products")
    def test_empty_navigation_items_returns_empty_products(
        self,
        mock_get_db_products: MagicMock,
        mock_get_mapped_products: MagicMock,
        catalog_repo_mock: MagicMock,
    ):
        """Test that when navigation returns empty items, we get empty products without calling get_db_products"""
        session = UnifiedAlchemyMagicMock()
        mock_my_hm_client = MyHolmatroPortalApiClient("jwt_token")
        params = ParamsReq(category_id=7411)

        # Mock empty navigation response
        catalog_repo_mock.get_navigation.return_value = Navigation(
            facets=[], items=[], properties=mock_tweakwise_response_properties, redirects=[]
        )

        result = get_products_with_filters_handle(
            catalog_repo_mock, params, None, mocked_user, session, mock_my_hm_client
        )

        # Verify navigation was called
        catalog_repo_mock.get_navigation.assert_called_once_with(mocked_user, params, None)

        # Verify get_db_products was NOT called when navigation has no items
        mock_get_db_products.assert_not_called()

        # Verify we get empty results
        assert result.products == []
        assert len(result.filters) == 0

    @patch("holmatro_customer_portal.services.get_products.ProductPreviewMapper.product_preview_mapper")
    @patch("holmatro_customer_portal.services.get_products.get_db_products")
    def test_catalog_repository_exception_handling(
        self,
        mock_get_db_products: MagicMock,
        mock_get_mapped_products: MagicMock,
        catalog_repo_mock: MagicMock,
    ):
        """Test that exceptions from catalog_repository.get_navigation are properly propagated"""
        session = UnifiedAlchemyMagicMock()
        mock_my_hm_client = MyHolmatroPortalApiClient("jwt_token")
        params = ParamsReq(category_id=7411)

        # Mock catalog repository to raise an exception
        catalog_repo_mock.get_navigation.side_effect = Exception("Catalog service unavailable")

        with pytest.raises(Exception, match="Catalog service unavailable"):
            get_products_with_filters_handle(catalog_repo_mock, params, None, mocked_user, session, mock_my_hm_client)

        # Verify get_navigation was called
        catalog_repo_mock.get_navigation.assert_called_once_with(mocked_user, params, None)

        # Verify downstream methods were not called due to exception
        mock_get_db_products.assert_not_called()
        mock_get_mapped_products.assert_not_called()

    def test_filter_out_unwanted_filters_and_attributes(self):
        """Test filtering logic for slider and checkbox filters."""

        # Test data
        filters = [
            # Valid slider filter (different min/max values)
            self._create_mock_filter(
                SelectionType.SLIDER.value, [self._create_mock_attribute("10.0"), self._create_mock_attribute("50.0")]
            ),
            # Invalid slider filter (same min/max values)
            self._create_mock_filter(
                SelectionType.SLIDER.value, [self._create_mock_attribute("25.0"), self._create_mock_attribute("25.0")]
            ),
            # Valid slider filter (same selected as max value)
            self._create_mock_filter(
                SelectionType.SLIDER.value,
                [
                    self._create_mock_attribute("25.0"),
                    self._create_mock_attribute("50.0", is_selected=True),
                    self._create_mock_attribute("50.0", is_selected=True),
                    self._create_mock_attribute("50.0"),
                ],
            ),
            # Valid checkbox filter (multiple attributes)
            self._create_mock_filter(
                SelectionType.CHECKBOX.value,
                [
                    self._create_mock_attribute("Option 1"),
                    self._create_mock_attribute("Option 2"),
                    self._create_mock_attribute("Option 3"),
                ],
            ),
            # Invalid checkbox filter (single attribute)
            self._create_mock_filter(SelectionType.CHECKBOX.value, [self._create_mock_attribute("Single Option")]),
            # Filter with unknown selection type (should be ignored)
            self._create_mock_filter("UNKNOWN_TYPE", [self._create_mock_attribute("Test")]),
        ]

        # Execute the function
        result = _filter_out_unwanted_filters_and_attributes(filters)

        # Assertions
        assert len(result) == 3  # Only valid slider and checkbox filters should remain

        # Check that the first valid slider filter is included
        valid_slider = result[0]
        assert valid_slider.facet_settings.selection_type == SelectionType.SLIDER.value
        assert valid_slider.attributes[0].title == "10.0"
        assert valid_slider.attributes[1].title == "50.0"

        # Check that the second valid slider filter is included
        valid_slider = result[1]
        assert valid_slider.facet_settings.selection_type == SelectionType.SLIDER.value
        assert valid_slider.attributes[0].title == "25.0"
        assert valid_slider.attributes[0].is_selected is False
        assert valid_slider.attributes[1].title == "50.0"
        assert valid_slider.attributes[1].is_selected is True
        assert valid_slider.attributes[2].title == "50.0"
        assert valid_slider.attributes[2].is_selected is True
        assert valid_slider.attributes[3].title == "50.0"
        assert valid_slider.attributes[3].is_selected is False

        # Check that the valid checkbox filter is included
        valid_checkbox = result[2]
        assert valid_checkbox.facet_settings.selection_type == SelectionType.CHECKBOX.value
        assert len(valid_checkbox.attributes) == 3

    def test_filter_out_unwanted_filters_empty_list(self):
        """Test function with empty input list."""
        result = _filter_out_unwanted_filters_and_attributes([])
        assert result == []

    def test_filter_out_unwanted_filters_edge_cases(self):
        """Test edge cases for slider filters."""

        # Edge case: slider with very close but not equal values
        filters = [
            self._create_mock_filter(
                SelectionType.SLIDER.value,
                [
                    self._create_mock_attribute("10.000"),
                    self._create_mock_attribute("10.001"),
                ],
            )
        ]

        result = _filter_out_unwanted_filters_and_attributes(filters)
        assert len(result) == 1  # Should be included as values are not equal

    @staticmethod
    def _create_mock_filter(selection_type, attributes):
        filter_mock = Mock()
        filter_mock.facet_settings.selection_type = selection_type
        filter_mock.attributes = attributes
        return filter_mock

    @staticmethod
    def _create_mock_attribute(title, is_selected=False):
        attr_mock = Mock()
        attr_mock.title = title
        attr_mock.is_selected = is_selected
        return attr_mock
