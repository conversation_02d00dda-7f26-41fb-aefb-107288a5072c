from unittest.mock import MagicMock
from uuid import uuid4

from fixtures.tweakwise_fixtures import mock_tweakwise_category_tree, mock_tweakwise_lifting_tree
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Category as DBCategory
from holmatro_customer_portal.database.models import User
from holmatro_customer_portal.services.products.get_category_tree import (  # noqa
    _remove_helper_categories,
    get_category_tree_handle,
)
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import Category, MainCategory
from tests.fixtures.database_models_fixture import mocked_language, mocked_user


def test_category_tree_returns_specific_category(catalog_repo_mock: MagicMock):
    db = UnifiedAlchemyMagicMock()
    catalog_repo_mock.get_category_tree.return_value = Category.model_validate(mock_tweakwise_lifting_tree)

    result = get_category_tree_handle(db, catalog_repo_mock, mocked_user, 20007411)

    assert result == [Category.model_validate(mock_tweakwise_lifting_tree)]


def test_category_tree_returns_only_users_category(catalog_repo_mock: MagicMock):
    db = UnifiedAlchemyMagicMock()
    mock_user = User(
        id=uuid4(),
        main_language_id=mocked_language.id,
        main_language=mocked_language,
        categories=[DBCategory(category_id="7411"), DBCategory(category_id="2838")],
    )

    result_category_tree = [
        category
        for category in mock_tweakwise_category_tree["children"]
        if category["categoryid"] in [10007411, 10002838]
    ]

    catalog_repo_mock.get_category_tree.side_effect = [
        Category.model_validate(mock_tweakwise_category_tree["children"][0]),
        Category.model_validate(mock_tweakwise_category_tree["children"][1]),
    ]

    # Uses Industrial category id to test all user's categories
    result = get_category_tree_handle(db, catalog_repo_mock, mock_user, None)

    assert result == [Category.model_validate(tree) for tree in result_category_tree]


def test_remove_helper_categories():
    mock_category = {"image_url": None, "category_id": 1, "category_path": "1", "children": []}

    category_tree = Category(
        title="Cutting",
        category_id=10002838,
        image_url=None,
        category_path="10002838",
        children=[
            MainCategory(title="Cutter", **mock_category),
            MainCategory(title="#Accessories", **mock_category),
            MainCategory(title="#Test", **mock_category),
        ],
    )

    _remove_helper_categories(category_tree)

    assert len(category_tree.children) == 1
