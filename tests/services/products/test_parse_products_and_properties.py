from unittest.mock import MagicMock, patch

from fixtures.database_models_fixture import mocked_user
from fixtures.query_factory import MockQueryFactory
from fixtures.tweakwise_fixtures import mock_tweakwise_response_properties
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.schemas.response_schema import ParamsReq
from holmatro_customer_portal.services.products.parse_products_and_properties import (
    get_db_products,
    parse_to_paginated_results,
)
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import ProductsResponse, ResponseProperties


def test_parse_to_paginated_results():
    products = [MockQueryFactory().get_mock_product_preview(), MockQueryFactory().get_mock_product_preview()]
    params = ParamsReq(products_per_page=1)
    result = parse_to_paginated_results(products, params)

    assert isinstance(result, tuple)
    products_result = result[0]
    assert isinstance(products_result, list)
    assert len(products_result) == 1

    properties_result = result[1]
    assert isinstance(properties_result, ResponseProperties)
    assert properties_result.no_of_items == 2
    assert properties_result.page_size == 1
    assert properties_result.no_of_pages == 2
    assert properties_result.category_id is None


@patch("holmatro_customer_portal.services.products.parse_products_and_properties.get_product_preview_by_article_number")
@patch("holmatro_customer_portal.services.products.parse_products_and_properties.get_db_products")
def test_get_product_previews_products_redirects(mock_get_products: MagicMock, mock_get_product_preview: MagicMock):
    session = UnifiedAlchemyMagicMock()

    product_response = ProductsResponse(
        **{
            "items": [],
            "properties": mock_tweakwise_response_properties,
            "redirects": [{"url": "111.111.111"}],
        }
    )

    get_db_products(product_response, mocked_user, session, None, None)

    mock_get_products.assert_not_called()
    mock_get_product_preview.assert_called_once_with(["111.111.111"], mocked_user, session, None)


@patch("holmatro_customer_portal.services.products.parse_products_and_properties.get_product_preview_by_article_number")
@patch("holmatro_customer_portal.services.products.parse_products_and_properties.get_query_products")
def test_get_product_previews_products(mock_get_products: MagicMock, mock_get_product_preview: MagicMock):
    session = UnifiedAlchemyMagicMock()

    product_response = ProductsResponse(
        **{
            "items": [],
            "properties": mock_tweakwise_response_properties,
            "redirects": [],
        }
    )

    get_db_products(product_response, mocked_user, session, None, None)

    mock_get_products.assert_called_once_with([], session, mocked_user, None)
    mock_get_product_preview.assert_not_called()
