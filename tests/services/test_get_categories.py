import pytest

from holmatro_customer_portal.database.models import Category, CategoryAssociation, Product
from holmatro_customer_portal.services.get_categories import map_article_to_categories, query_category_ids
from holmatro_customer_portal.utils.enums import SyncforceCategory
from tests.factories import CategoryFactory, ProductFactory
from tests.fixtures.database import session


class TestQueryCategoryIds:
    """Test the query_category_ids function."""

    @pytest.fixture(autouse=True)
    def _pass_fixtures(self, session):
        self.session = session

    def test_query_category_ids_single_product_single_category(self):
        """Test query_category_ids with one product in one category."""
        # Create a product
        product = ProductFactory.build(product_id=1, article_number="TEST001")
        self.session.add(product)
        self.session.commit()

        # Create a category
        category = CategoryFactory.build(category_id="5121")  # CYLINDERS category
        self.session.add(category)
        self.session.commit()

        # Create CategoryAssociation
        assoc = CategoryAssociation(category_id=category.id, product_id=product.id, product_sort_order=1)
        self.session.add(assoc)
        self.session.commit()

        # Test the query
        result = query_category_ids(["TEST001"], self.session)

        # Should return exactly one result
        assert len(result) == 1
        assert result[0][0] == "TEST001"  # article_number
        assert result[0][1] == "5121"  # category_id

        # Verify it's NOT a Cartesian product by testing with different article number
        result_wrong = query_category_ids(["NONEXISTENT"], self.session)
        assert len(result_wrong) == 0  # Should return empty, not all categories

    def test_query_category_ids_single_product_multiple_categories(self):
        """Test query_category_ids with one product in multiple categories."""
        # Create a product
        product = ProductFactory.build(product_id=2, article_number="TEST002")
        self.session.add(product)
        self.session.commit()

        # Create multiple categories
        category1 = CategoryFactory.build(category_id="5121")  # CYLINDERS
        category2 = CategoryFactory.build(category_id="1058")  # CYLINDER_WEDGES
        # Create a third category that should NOT be returned
        category3 = CategoryFactory.build(category_id="9999")  # Not associated
        self.session.add_all([category1, category2, category3])
        self.session.commit()

        # Create CategoryAssociations for only first two categories
        assoc1 = CategoryAssociation(category_id=category1.id, product_id=product.id, product_sort_order=1)
        assoc2 = CategoryAssociation(category_id=category2.id, product_id=product.id, product_sort_order=2)
        self.session.add_all([assoc1, assoc2])
        self.session.commit()

        # Test the query
        result = query_category_ids(["TEST002"], self.session)

        # Should return exactly 2 results, not 3 (which would indicate Cartesian product)
        assert len(result) == 2
        article_numbers = [row[0] for row in result]
        category_ids = [row[1] for row in result]

        assert all(article_num == "TEST002" for article_num in article_numbers)
        assert set(category_ids) == {"5121", "1058"}
        # Verify the third category is NOT included
        assert "9999" not in category_ids

    def test_query_category_ids_multiple_products_single_category(self):
        """Test query_category_ids with multiple products in the same category."""
        # Create products
        product1 = ProductFactory.build(product_id=3, article_number="TEST003")
        product2 = ProductFactory.build(product_id=4, article_number="TEST004")
        # Create a third product that should NOT be returned
        product3 = ProductFactory.build(product_id=5, article_number="TEST005")
        self.session.add_all([product1, product2, product3])
        self.session.commit()

        # Create a category
        category = CategoryFactory.build(category_id="6921")  # CUTTERS category
        self.session.add(category)
        self.session.commit()

        # Create CategoryAssociations for only first two products
        assoc1 = CategoryAssociation(category_id=category.id, product_id=product1.id, product_sort_order=1)
        assoc2 = CategoryAssociation(category_id=category.id, product_id=product2.id, product_sort_order=2)
        self.session.add_all([assoc1, assoc2])
        self.session.commit()

        # Test the query - asking for all three products but only two are associated
        result = query_category_ids(["TEST003", "TEST004", "TEST005"], self.session)

        # Should return exactly 2 results, not 3 (which would indicate Cartesian product)
        assert len(result) == 2
        article_numbers = [row[0] for row in result]
        category_ids = [row[1] for row in result]

        assert set(article_numbers) == {"TEST003", "TEST004"}
        assert all(category_id == "6921" for category_id in category_ids)
        # Verify the third product is NOT included
        assert "TEST005" not in article_numbers

    def test_query_category_ids_multiple_products_multiple_categories(self):
        """Test query_category_ids with multiple products in multiple categories."""
        # Create products
        product1 = ProductFactory.build(product_id=5, article_number="TEST005")
        product2 = ProductFactory.build(product_id=6, article_number="TEST006")
        self.session.add(product1)
        self.session.add(product2)
        self.session.commit()

        # Create categories
        category1 = CategoryFactory.build(category_id="1017")  # HYDRAULIC_PUMPS
        category2 = CategoryFactory.build(category_id="2371")  # LIFTING_HOSES
        category3 = CategoryFactory.build(category_id="7474")  # CUTTER_PUMPS
        self.session.add_all([category1, category2, category3])
        self.session.commit()

        # Create CategoryAssociations - specific relationships only
        # Product1 in categories 1 and 2 only
        assoc1 = CategoryAssociation(category_id=category1.id, product_id=product1.id, product_sort_order=1)
        assoc2 = CategoryAssociation(category_id=category2.id, product_id=product1.id, product_sort_order=2)
        # Product2 in categories 2 and 3 only
        assoc3 = CategoryAssociation(category_id=category2.id, product_id=product2.id, product_sort_order=1)
        assoc4 = CategoryAssociation(category_id=category3.id, product_id=product2.id, product_sort_order=2)
        self.session.add_all([assoc1, assoc2, assoc3, assoc4])
        self.session.commit()

        # Test the query
        result = query_category_ids(["TEST005", "TEST006"], self.session)

        # Should return exactly 4 results:
        # TEST005->1017, TEST005->2371, TEST006->2371, TEST006->7474
        # NOT 6 results (which would be a Cartesian product: 2 products × 3 categories)
        assert len(result) == 4

        # Convert to a more testable format
        result_tuples = [(row[0], row[1]) for row in result]
        expected_tuples = [
            ("TEST005", "1017"),  # Product1 -> Category1
            ("TEST005", "2371"),  # Product1 -> Category2
            ("TEST006", "2371"),  # Product2 -> Category2
            ("TEST006", "7474"),  # Product2 -> Category3
        ]

        assert set(result_tuples) == set(expected_tuples)

        # Verify what should NOT be present (would indicate Cartesian product)
        forbidden_tuples = [
            ("TEST005", "7474"),  # Product1 is NOT in Category3
            ("TEST006", "1017"),  # Product2 is NOT in Category1
        ]
        for forbidden in forbidden_tuples:
            assert forbidden not in result_tuples

    def test_query_category_ids_empty_article_numbers(self):
        """Test query_category_ids with empty article numbers list."""
        result = query_category_ids([], self.session)
        assert len(result) == 0

    def test_query_category_ids_nonexistent_article_numbers(self):
        """Test query_category_ids with non-existent article numbers."""
        # Create some data to ensure the query isn't just returning everything
        product = ProductFactory.build(product_id=99, article_number="EXISTS")
        category = CategoryFactory.build(category_id="9999")
        self.session.add_all([product, category])
        self.session.commit()

        assoc = CategoryAssociation(category_id=category.id, product_id=product.id, product_sort_order=1)
        self.session.add(assoc)
        self.session.commit()

        # Query for non-existent products
        result = query_category_ids(["NONEXISTENT001", "NONEXISTENT002"], self.session)
        assert len(result) == 0

    def test_query_category_ids_mixed_existing_nonexistent(self):
        """Test query_category_ids with mix of existing and non-existing article numbers."""
        # Create a product
        product = ProductFactory.build(product_id=7, article_number="TEST007")
        self.session.add(product)
        self.session.commit()

        # Create a category
        category = CategoryFactory.build(category_id="2587")  # FLOW_PANELS
        self.session.add(category)
        self.session.commit()

        # Create CategoryAssociation
        assoc = CategoryAssociation(category_id=category.id, product_id=product.id, product_sort_order=1)
        self.session.add(assoc)
        self.session.commit()

        # Test with mix of existing and non-existing
        result = query_category_ids(["TEST007", "NONEXISTENT"], self.session)

        # Should return exactly 1 result, not more
        assert len(result) == 1
        assert result[0][0] == "TEST007"
        assert result[0][1] == "2587"

    def test_query_category_ids_distinct_results(self):
        """Test that query_category_ids returns distinct results even with duplicate associations."""
        # Create a product
        product = ProductFactory.build(product_id=8, article_number="TEST008")
        self.session.add(product)
        self.session.commit()

        # Create a category
        category = CategoryFactory.build(category_id="826")  # VARI_PUMPS
        self.session.add(category)
        self.session.commit()

        # Create CategoryAssociation
        assoc = CategoryAssociation(category_id=category.id, product_id=product.id, product_sort_order=1)
        self.session.add(assoc)
        self.session.commit()

        # Query the same article number multiple times in the input
        result = query_category_ids(["TEST008", "TEST008"], self.session)

        # Should return distinct results - only one row
        assert len(result) == 1
        assert result[0][0] == "TEST008"
        assert result[0][1] == "826"


class TestMapArticleToCategories:
    """Test the map_article_to_categories function."""

    @pytest.fixture(autouse=True)
    def _pass_fixtures(self, session):
        self.session = session

    def test_map_article_to_categories_with_known_categories(self):
        """Test map_article_to_categories with categories that have default attributes."""
        # Create products
        product1 = ProductFactory.build(product_id=10, article_number="TEST010")
        product2 = ProductFactory.build(product_id=11, article_number="TEST011")
        self.session.add_all([product1, product2])
        self.session.commit()

        # Create categories with known SyncforceCategory values
        cylinders_category = CategoryFactory.build(category_id=str(SyncforceCategory.CYLINDERS.value))  # 5121
        cutters_category = CategoryFactory.build(category_id=str(SyncforceCategory.CUTTERS.value))  # 6921
        self.session.add_all([cylinders_category, cutters_category])
        self.session.commit()

        # Create CategoryAssociations
        assoc1 = CategoryAssociation(category_id=cylinders_category.id, product_id=product1.id, product_sort_order=1)
        assoc2 = CategoryAssociation(category_id=cutters_category.id, product_id=product2.id, product_sort_order=1)
        self.session.add_all([assoc1, assoc2])
        self.session.commit()

        # Create mock query result (simulating what would come from a product query)
        MockRow = type("MockRow", (), {})
        query_result = []

        row1 = MockRow()
        row1.product_article_number = "TEST010"
        query_result.append(row1)

        row2 = MockRow()
        row2.product_article_number = "TEST011"
        query_result.append(row2)

        # Test the mapping
        result = map_article_to_categories(query_result, self.session)

        assert isinstance(result, dict)
        assert "TEST010" in result
        assert "TEST011" in result

        # Check that the attributes match expected values from DefaultAttributes
        # CYLINDERS should map to tonnage, stroke, and closed height attributes
        cylinders_attrs = result["TEST010"]
        assert 98 in cylinders_attrs  # TONNAGE_METRIC
        assert 99 in cylinders_attrs  # TONNAGE_IMPERIAL
        assert 100 in cylinders_attrs  # STROKE_METRIC
        assert 101 in cylinders_attrs  # STROKE_IMPERIAL

        # CUTTERS should map to blade type, cutting opening, and weight attributes
        cutters_attrs = result["TEST011"]
        assert 7 in cutters_attrs  # BLADE_TYPE
        assert 70 in cutters_attrs  # MAX_CUTTING_OPENING_METRIC
        assert 71 in cutters_attrs  # MAX_CUTTING_OPENING_IMPERIAL

    def test_map_article_to_categories_with_unknown_categories(self):
        """Test map_article_to_categories with categories that don't have default attributes."""
        # Create a product
        product = ProductFactory.build(product_id=12, article_number="TEST012")
        self.session.add(product)
        self.session.commit()

        # Create a category with an unknown category_id
        unknown_category = CategoryFactory.build(category_id="9999")  # Not in DefaultAttributes
        self.session.add(unknown_category)
        self.session.commit()

        # Create CategoryAssociation
        assoc = CategoryAssociation(category_id=unknown_category.id, product_id=product.id, product_sort_order=1)
        self.session.add(assoc)
        self.session.commit()

        # Create mock query result
        MockRow = type("MockRow", (), {})
        row = MockRow()
        row.product_article_number = "TEST012"
        query_result = [row]

        # Test the mapping
        result = map_article_to_categories(query_result, self.session)

        # Should return empty dict or not include the article since category is unknown
        assert isinstance(result, dict)
        assert "TEST012" not in result or result["TEST012"] == []

    def test_map_article_to_categories_empty_query_result(self):
        """Test map_article_to_categories with empty query result."""
        result = map_article_to_categories([], self.session)
        assert isinstance(result, dict)
        assert len(result) == 0

    def test_map_article_to_categories_multiple_categories_per_product(self):
        """Test map_article_to_categories where one product belongs to multiple categories."""
        # Create a product
        product = ProductFactory.build(product_id=13, article_number="TEST013")
        self.session.add(product)
        self.session.commit()

        # Create multiple categories with known values
        hydraulic_pumps_category = CategoryFactory.build(
            category_id=str(SyncforceCategory.HYDRAULIC_PUMPS.value)
        )  # 1017
        flow_panels_category = CategoryFactory.build(category_id=str(SyncforceCategory.FLOW_PANELS.value))  # 2587
        self.session.add_all([hydraulic_pumps_category, flow_panels_category])
        self.session.commit()

        # Create CategoryAssociations
        assoc1 = CategoryAssociation(
            category_id=hydraulic_pumps_category.id, product_id=product.id, product_sort_order=1
        )
        assoc2 = CategoryAssociation(category_id=flow_panels_category.id, product_id=product.id, product_sort_order=2)
        self.session.add_all([assoc1, assoc2])
        self.session.commit()

        # Create mock query result
        MockRow = type("MockRow", (), {})
        row = MockRow()
        row.product_article_number = "TEST013"
        query_result = [row]

        # Test the mapping
        result = map_article_to_categories(query_result, self.session)

        assert isinstance(result, dict)
        assert "TEST013" in result

        product_attrs = result["TEST013"]
        assert len(product_attrs) > 0
