import copy
import uuid
from datetime import datetime
from unittest.mock import MagicMock, call, patch

import pytest
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Language, Product
from holmatro_customer_portal.services.import_product import (
    import_product_to_db,
    import_products_from_storage,
    soft_delete_products_not_in_list,
)
from holmatro_customer_portal.utils.env import Env
from tests.factories import ProductFactory
from tests.fixtures.categories_fixture import categories_mock
from tests.fixtures.http_requests_fixture import http_requests_mock
from tests.fixtures.product_details_fixture import product_details_mock as product_details_mock_original
from tests.fixtures.storage_utils_fixture import setup_storage_assets

mock_session = UnifiedAlchemyMagicMock(
    data=[([call.query(Language).filter_by(language_code="en").one_or_none()], Language(language_code="en"))]
)


@patch("holmatro_customer_portal.services.import_product.session_creator", return_value=mock_session)
def test_fetch_and_import_products(mock_db, s3_client, http_requests_mock):
    # Fix the bucket name here
    setup_storage_assets(s3_client)

    import_products_from_storage(["1"])


def test_product_does_not_exist(s3_client):
    # Fix the bucket name here
    mock_session = MagicMock()

    s3_client.create_bucket(Bucket=Env.HOLMATRO_ASSETS_BUCKET_NAME.get())
    with pytest.raises(ValueError):
        import_products_from_storage(["1"])
    mock_session.add.assert_not_called()


@patch("holmatro_customer_portal.services.import_product.session_creator")
@patch("holmatro_customer_portal.services.import_product.fetch_json_contents_by_ids")
def test_import_products_from_storage_does_not_soft_delete_by_default(
    mock_fetch_json_contents_by_ids, mock_session_creator, s3_client, http_requests_mock, session
):
    mock_session_creator.return_value = MagicMock(return_value=session)
    setup_storage_assets(s3_client)

    mock_product_data_a = copy.deepcopy(product_details_mock_original)
    # Create two products in the database
    product_a_umid = uuid.UUID(mock_product_data_a["Product"]["UMID"])
    product_b_umid = uuid.uuid4()

    product_a = ProductFactory.build(id=uuid.uuid4(), umid=product_a_umid, deleted_at=None)
    product_b = ProductFactory.build(id=uuid.uuid4(), umid=product_b_umid, deleted_at=None)

    session.add_all([product_a, product_b])
    session.commit()

    mock_fetch_json_contents_by_ids.return_value = {str(product_a_umid): mock_product_data_a}

    # Call the function to import products, only supplying product A's UMID (default soft_delete_others=False)
    import_products_from_storage([str(product_a_umid)])

    # Retrieve products from the database
    updated_product_a = session.query(Product).filter_by(umid=product_a_umid).one_or_none()
    updated_product_b = session.query(Product).filter_by(umid=product_b_umid).one_or_none()

    # Assertions
    assert updated_product_a is not None
    assert updated_product_a.deleted_at is None  # Product A should not be soft-deleted

    assert updated_product_b is not None
    assert updated_product_b.deleted_at is None  # Product B should NOT be soft-deleted


@patch("holmatro_customer_portal.services.import_product.session_creator")
@patch("holmatro_customer_portal.services.import_product.fetch_json_contents_by_ids")
@patch("holmatro_customer_portal.utils.env.Env.MIN_PRODUCTS_PROPOSITION_THRESHOLD.get", return_value=1)
def test_full_resync_workflow_with_soft_deletion(
    mock_min_threshold, mock_fetch_json_contents_by_ids, mock_session_creator, s3_client, http_requests_mock, session
):
    """Test the full resync workflow: import products then soft-delete others"""
    mock_session_creator.return_value = MagicMock(return_value=session)
    setup_storage_assets(s3_client)

    mock_product_data_a = copy.deepcopy(product_details_mock_original)
    # Create two products in the database
    product_a_umid = uuid.UUID(mock_product_data_a["Product"]["UMID"])
    product_b_umid = uuid.uuid4()

    product_a = ProductFactory.build(id=uuid.uuid4(), umid=product_a_umid, deleted_at=None)
    product_b = ProductFactory.build(id=uuid.uuid4(), umid=product_b_umid, deleted_at=None)

    session.add_all([product_a, product_b])
    session.commit()

    mock_fetch_json_contents_by_ids.return_value = {str(product_a_umid): mock_product_data_a}

    # Step 1: Import products (only product A)
    imported_umids = import_products_from_storage([str(product_a_umid)])

    # Step 2: Soft-delete products not in the imported list
    soft_delete_products_not_in_list(imported_umids)

    # Retrieve products from the database
    updated_product_a = session.query(Product).filter_by(umid=product_a_umid).one_or_none()
    updated_product_b = session.query(Product).filter_by(umid=product_b_umid).one_or_none()

    # Assertions
    assert updated_product_a is not None
    assert updated_product_a.deleted_at is None  # Product A should not be soft-deleted (it was imported)

    assert updated_product_b is not None
    assert updated_product_b.deleted_at is not None  # Product B should be soft-deleted (not in import list)
    assert isinstance(updated_product_b.deleted_at, datetime)


@patch("holmatro_customer_portal.services.import_product.session_creator")
def test_soft_delete_products_not_in_list_safeguards(mock_session_creator, session):
    """Test that soft_delete_products_not_in_list refuses to run with insufficient products"""
    mock_session_creator.return_value = MagicMock(return_value=session)

    # Test 1: Empty list should raise ValueError
    with pytest.raises(ValueError, match="Cannot soft-delete products with empty imported_umids list"):
        soft_delete_products_not_in_list([])

    # Test 2: Below threshold should raise ValueError (default threshold is 500)
    with pytest.raises(ValueError, match="below minimum threshold"):
        soft_delete_products_not_in_list(["single-product-id"])


@patch("holmatro_customer_portal.services.import_product.session_creator")
@patch("holmatro_customer_portal.services.import_product.fetch_json_contents_by_ids")
def test_import_products_from_storage_undelete_product(
    mock_fetch_json_contents_by_ids, mock_session_creator, s3_client, http_requests_mock, session
):
    mock_session_creator.return_value = MagicMock(return_value=session)
    setup_storage_assets(s3_client)

    mock_product_data_a = copy.deepcopy(product_details_mock_original)
    # Create two products in the database
    product_a_umid = uuid.UUID(mock_product_data_a["Product"]["UMID"])

    deleted_date = datetime(year=2024, month=1, day=15)
    product_a = ProductFactory.build(id=uuid.uuid4(), umid=product_a_umid, deleted_at=deleted_date)

    session.add(product_a)
    session.commit()

    mock_fetch_json_contents_by_ids.return_value = {str(product_a_umid): mock_product_data_a}

    import_products_from_storage([str(product_a_umid)])

    # Retrieve products from the database
    updated_product_a = session.query(Product).filter_by(umid=product_a_umid).one_or_none()

    # Assertions
    assert updated_product_a is not None
    assert updated_product_a.deleted_at is None


@patch("holmatro_customer_portal.services.import_product.session_creator")
def test_import_product_without_segmentation(mock_session_creator, session):
    # Arrange
    mock_session_creator.return_value = MagicMock(return_value=session)

    mock_product_data = copy.deepcopy(product_details_mock_original)

    # Clear segmentation to simulate a product without it
    mock_product_data["Product"]["Segmentation"] = {}

    # Act
    umid = import_product_to_db(session, mock_product_data, categories_mock)

    # Assert
    assert umid is None
    products = session.query(Product).all()
    assert len(products) == 0


@patch("holmatro_customer_portal.services.import_product.sentry_sdk.capture_exception")
@patch("holmatro_customer_portal.services.import_product.session_creator")
def test_import_product_with_pydantic_validation_error(mock_session_creator, mock_capture_exception, session):
    # Arrange
    mock_session_creator.return_value = MagicMock(return_value=session)

    mock_product_data = copy.deepcopy(product_details_mock_original)

    # Force a validation error by renaming a required field
    mock_product_data["Product"]["MasterKode"] = mock_product_data["Product"].pop("MasterCode")

    # Act
    umid = import_product_to_db(session, mock_product_data, categories_mock)

    # Assert
    assert umid is None
    products = session.query(Product).all()
    assert len(products) == 0
    # Check that sentry_sdk.capture_exception was called with the correct extras
    _, called_kwargs = mock_capture_exception.call_args
    assert "extras" in called_kwargs
    assert called_kwargs["extras"]["product_article_number"] == "100.152.048"


@patch("holmatro_customer_portal.services.import_product.session_creator")
@patch("holmatro_customer_portal.services.import_product.fetch_json_contents_by_ids")
@patch("holmatro_customer_portal.services.import_product.get_api_json_contents")
def test_import_products_resilient_to_individual_failures(
    mock_get_categories, mock_fetch_json_contents_by_ids, mock_session_creator, session
):
    """Test that import_products_from_storage continues processing even when individual products fail"""
    # Arrange
    mock_session_creator.return_value = MagicMock(return_value=session)
    mock_get_categories.return_value = categories_mock

    # Create valid product data
    valid_product_data = copy.deepcopy(product_details_mock_original)

    # Create invalid product data (missing required field)
    invalid_product_data = copy.deepcopy(product_details_mock_original)
    del invalid_product_data["Product"]["UMID"]  # This will cause a validation error

    # Mock fetch to return both valid and invalid products
    mock_fetch_json_contents_by_ids.return_value = {
        "product-details-valid.json": valid_product_data,
        "product-details-invalid.json": invalid_product_data,
    }

    # Act - disable asset processing to avoid connection issues in test environment
    imported_umids = import_products_from_storage(["valid", "invalid"], process_assets=False)

    # Assert
    # Should return only the valid product UMID, not fail completely
    assert len(imported_umids) == 1
    assert imported_umids[0] == valid_product_data["Product"]["UMID"]

    # Verify the valid product was actually saved to the database
    saved_products = session.query(Product).all()
    assert len(saved_products) == 1
    assert str(saved_products[0].umid) == valid_product_data["Product"]["UMID"]
