import uuid
from http import HTTPStatus
from unittest.mock import MagicMock, patch

import pytest
from fastapi import HTTPException
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import TextType
from holmatro_customer_portal.mappers.product_preview_mapper import ProductPreviewMapper
from holmatro_customer_portal.schemas.response_schema import ProductDetailsRes, ProductPreviewRes
from holmatro_customer_portal.services.get_product_details import get_product_details_handle
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient
from tests.fixtures.database_models_fixture import mocked_user
from tests.fixtures.my_hm_fixture import my_hm_mock_responses
from tests.fixtures.product_mapper_factory import MockProductMapper
from tests.fixtures.query_factory import MockProductDetails, MockQueryFactory
from tests.fixtures.responses_fixtures import mock_grouped_attribute_res

product_id = uuid.uuid4()


@pytest.mark.parametrize(
    "db_products, recommendations, expected_result",
    [
        (
            [MockQueryFactory(product_id=product_id).get_mock_product_details()],
            [],
            MockProductMapper(product_id=product_id).get_mock_product_details(),
        ),
        (
            [
                MockQueryFactory(product_id=product_id).get_mock_product_details(),
                MockQueryFactory(product_id=product_id).get_mock_product_details(
                    product_description_type=TextType.long_description,
                    product_description_value="product long description",
                ),
            ],
            [],
            MockProductMapper(product_id=product_id).get_mock_product_details(
                product_description={
                    "features": ["product features"],
                    "long_description": ["product long description"],
                }
            ),
        ),
        (
            [MockQueryFactory(product_id=product_id).get_mock_product_details()],
            MockProductMapper().get_mock_product_preview(),
            MockProductMapper(product_id=product_id).get_mock_product_details(),
        ),
    ],
)
@patch("holmatro_customer_portal.mappers.product_preview_mapper.get_product_preview_attributes")
@patch("holmatro_customer_portal.services.get_product_details.get_product_details")
def test_get_product_details_handle(
    mock_get_product_from_db: MagicMock,
    mock_get_attributes: MagicMock,
    db_products: list[MockProductDetails],
    recommendations: list[ProductPreviewRes],
    expected_result: ProductDetailsRes,
    my_hm_mock_responses,
):
    mock_my_hm_client = MyHolmatroPortalApiClient("jwt_token")
    mock_get_attributes.return_value = [mock_grouped_attribute_res]
    mock_get_product_from_db.return_value = db_products
    with patch.object(ProductPreviewMapper, "is_product_favorite", return_value=True):
        res = get_product_details_handle(product_id, mocked_user, UnifiedAlchemyMagicMock(), mock_my_hm_client)

    assert res.name == expected_result.name
    assert res.product_id == product_id
    assert res.thumbnail_link is None
    assert res.alternative_products == []
    assert res.combined_with == []
    assert res.favorite is True


@patch("holmatro_customer_portal.services.get_product_details.get_product_details")
def test_get_product_from_db_exception(mock_get_product_from_db: MagicMock):
    session = UnifiedAlchemyMagicMock()
    mock_my_hm_client = MyHolmatroPortalApiClient("jwt_token")
    mock_get_product_from_db.return_value = []

    with pytest.raises(HTTPException) as e:
        get_product_details_handle(product_id, mocked_user, session, mock_my_hm_client)

    assert e.value.status_code == HTTPStatus.NOT_FOUND
