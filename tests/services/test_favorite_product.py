import copy
import uuid
from http import HTTPStatus
from unittest import mock
from unittest.mock import MagicMock, patch

import pytest
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.api.products import remove_favorite_product_handle
from holmatro_customer_portal.database.models import AssetResource, Product
from holmatro_customer_portal.services.favorite_product import add_favorite_product_handle, get_favorite_products_handle
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient
from tests.fixtures.database_models_fixture import mocked_product, mocked_user
from tests.fixtures.my_hm_fixture import my_hm_mock_responses
from tests.fixtures.product_mapper_factory import MockProductMapper
from tests.fixtures.query_factory import MockQueryFactory

product_id = uuid.uuid4()
asset_id = uuid.UUID("e7378f16-4ec3-497a-8833-8f4045d9660e")


def test_add_favorite_product_handle():
    mock_user = copy.deepcopy(mocked_user)
    product = Product(id=product_id)
    session = UnifiedAlchemyMagicMock(data=[([mock.call.query(Product), mock.call.get(product_id)], [product])])

    add_favorite_product_handle(product_id, mock_user, session)

    assert product in mock_user.favorite_products
    session.commit.assert_called_once()


def test_add_favorite_product_handle_exception():
    mock_user = copy.deepcopy(mocked_user)
    product = Product(id=product_id)
    session = UnifiedAlchemyMagicMock()

    with pytest.raises(HTTPException) as error:
        add_favorite_product_handle(product_id, mock_user, session)

    assert error.value.status_code == HTTPStatus.NOT_FOUND
    assert product not in mock_user.favorite_products
    session.commit.assert_not_called()


def test_add_favorite_product_handle_product_already_favorite():
    mock_user = copy.deepcopy(mocked_user)
    product = Product(id=product_id)
    mock_user.favorite_products.append(product)
    session = UnifiedAlchemyMagicMock(data=[([mock.call.query(Product), mock.call.get(product_id)], [product])])

    add_favorite_product_handle(product_id, mock_user, session)

    assert product in mock_user.favorite_products
    session.commit.assert_not_called()


def test_remove_favorite_product_handle():
    mock_user = copy.deepcopy(mocked_user)
    product = Product(id=product_id)
    mock_user.favorite_products.append(product)
    session = UnifiedAlchemyMagicMock(data=[([mock.call.query(Product), mock.call.get(product_id)], [product])])

    remove_favorite_product_handle(product_id, mock_user, session)
    assert product not in mock_user.favorite_products
    session.commit.assert_called_once()


def test_remove_favorite_product_handle_not_product():
    mock_user = copy.deepcopy(mocked_user)
    product = Product(id=product_id)
    session = UnifiedAlchemyMagicMock()

    with pytest.raises(HTTPException) as error:
        remove_favorite_product_handle(product_id, mock_user, session)

    assert error.value.status_code == HTTPStatus.NOT_FOUND
    assert product not in mock_user.favorite_products
    session.commit.assert_not_called()


def test_remove_favorite_product_handle_product_not_in_table():
    mock_user = copy.deepcopy(mocked_user)
    product = Product(id=product_id)
    session = UnifiedAlchemyMagicMock(data=[([mock.call.query(Product), mock.call.get(product_id)], [product])])

    remove_favorite_product_handle(product_id, mock_user, session)

    assert product not in mock_user.favorite_products
    session.commit.assert_not_called()


@pytest.mark.parametrize(
    "product, db_data, query_result, expected_result",
    [
        (
            mocked_product,
            [
                (
                    [mock.call.query(AssetResource), mock.call.filter_by(asset_id=asset_id)],
                    [AssetResource(asset_id=asset_id, url="https://example.com/thumbnail.jpg")],
                )
            ],
            [MockQueryFactory(product_id=product_id).get_mock_product_preview()],
            [MockProductMapper(product_id=product_id).get_mock_product_preview()],
        ),
        (
            Product(id=uuid.UUID("171b1491-3ab3-46a8-9347-da4d4651b37a")),
            [
                (
                    [mock.call.query(AssetResource), mock.call.filter_by(asset_id=asset_id)],
                    [AssetResource(asset_id=asset_id, url="https://example.com/thumbnail.jpg")],
                )
            ],
            [],
            [],
        ),
        (
            mocked_product,
            [],
            [],
            [],
        ),
    ],
)
@patch("holmatro_customer_portal.services.favorite_product.get_product_preview_by_ids")
def test_get_favorite_products_handle(
    mock_get_product_preview: MagicMock,
    product: Product,
    db_data: list,
    query_result: list,
    expected_result: list,
    my_hm_mock_responses,
):
    session = UnifiedAlchemyMagicMock(data=db_data)
    mock_my_hm_client = MyHolmatroPortalApiClient("jwt_token")
    mock_user = copy.deepcopy(mocked_user)
    mock_user.favorite_products.append(product)
    mock_get_product_preview.return_value = query_result

    res = get_favorite_products_handle(mock_user, session, mock_my_hm_client)

    assert res == expected_result
    assert len(mock_user.favorite_products) == 1
