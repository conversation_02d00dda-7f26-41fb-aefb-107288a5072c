import io
import uuid
from unittest.mock import MagicMock, call, patch

from mock_alchemy.mocking import UnifiedAlchemyMagicMock
from pypdf import <PERSON>d<PERSON><PERSON><PERSON><PERSON>, PdfWriter

from holmatro_customer_portal.database.enums import AssetSubTypeId, AssetType, SystemType
from holmatro_customer_portal.database.models import Asset, AssetResource, ConfigurationProduct, Language
from holmatro_customer_portal.services.configurator.get_documents import get_merged_documents


def create_mock_pdf_bytes():
    # Create a PDF file in memory
    pdf_buffer = io.BytesIO()

    # Create a PDF writer object and add a blank page
    pdf_writer = PdfWriter()
    pdf_writer.add_blank_page(width=72, height=72)

    pdf_writer.write(pdf_buffer)
    pdf_buffer.seek(0)
    mock_pdf_bytes = pdf_buffer.getvalue()
    return mock_pdf_bytes


@patch("holmatro_customer_portal.services.configurator.get_documents.requests.get")
def test_get_merged_spec_sheets(mock_get):
    session = UnifiedAlchemyMagicMock(
        data=[
            (
                [call.query(Language)],
                [Language(language_code="en")],
            ),
            (
                [call.query(AssetResource.url)],
                [("http://mockurl1.com",), ("http://mockurl2.com",)],
            ),
        ]
    )
    configuration_id, language_id = uuid.uuid4(), uuid.uuid4()

    # Mock the response of requests.get
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.content = create_mock_pdf_bytes()  # Simplified representation of a PDF file
    mock_get.return_value = mock_response

    output = get_merged_documents(
        configuration_id,
        session,
        language_id,
        SystemType.METRIC,
        AssetType.PRODUCT_TECHNICAL_SPECIFICATION_SHEET,
        AssetSubTypeId.TECHNICAL_SPECIFICATION_SHEET,
    )

    pdf_buffer = io.BytesIO(output)
    pdf_reader = PdfReader(pdf_buffer)
    # Return the number of pages
    assert pdf_reader.pages[0]
    assert pdf_reader.pages[1]
    assert isinstance(output, bytes)
