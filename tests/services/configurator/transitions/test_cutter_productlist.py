from unittest.mock import MagicMock, patch

import pytest
from fixtures.test_client import mock_db_user, mock_jwt_token
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.services.configurator.configurator_enums import CutterApplication, CutterType
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.workflow import ConfiguratorFactory
from tests.fixtures.helpers import create_test_configurator

configuration = Configuration(user=mock_db_user())


@pytest.mark.parametrize(
    "data, next_state",
    [
        pytest.param(
            CutterApplication.CABLE_CUTTING.value,
            ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_WAY_OF_CUTTING,
            id="cable cutting",
        ),
        pytest.param(
            CutterApplication.METAL_RECYCLING.value,
            ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_WAY_OF_CUTTING,
            id="metla recycling",
        ),
        pytest.param(
            CutterApplication.CAR_RECYCLING.value,
            ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_WAY_OF_CUTTING,
            id="car recycling",
        ),
        pytest.param(
            CutterApplication.FRIDGE_RECYCLING.value,
            ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_SETTINGS,
            id="fridge recycling",
        ),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_cutting_cutter.get_configuration_data_value"
)
def test_back_transitions(mock_get_data: MagicMock, data: str, next_state: ConfiguratorState):
    session = UnifiedAlchemyMagicMock()
    mock_get_data.return_value = data

    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST, configuration, mock_jwt_token
    )

    configurator.back()

    assert configurator.state == next_state


@pytest.mark.parametrize(
    "data, next_state",
    [
        pytest.param(
            CutterType.BATTERY.value, ConfiguratorState.INDUSTRIAL_CUTTING_CHARGER_BATTERIES_PRODUCTLIST, id="Battery"
        ),
        pytest.param(CutterType.MOBILE.value, ConfiguratorState.INDUSTRIAL_CUTTING_HOSE_PRODUCTLIST, id="Mobile"),
        pytest.param(
            CutterType.STATIONARY.value, ConfiguratorState.INDUSTRIAL_CUTTING_HOSE_PRODUCTLIST, id="Stationary"
        ),
        pytest.param(None, ConfiguratorState.INDUSTRIAL_CUTTING_HOSE_PRODUCTLIST, id="No choice"),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_cutting_charger_batteries.ChargerBatteriesProductList.on_enter"
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_cutting_hose.HoseProductList.on_enter")
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_cutting_cutter.get_configuration_data_value"
)
def test_next_transitions(mock_get_data: MagicMock, _, __, data, next_state: ConfiguratorState):
    session = UnifiedAlchemyMagicMock()
    mock_get_data.return_value = data

    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST, configuration, mock_jwt_token
    )

    configurator.next()

    assert configurator.state == next_state
    mock_get_data.assert_called()


@pytest.mark.parametrize(
    "data, next_state",
    [
        pytest.param(
            CutterType.BATTERY.value, ConfiguratorState.INDUSTRIAL_CUTTING_CHARGER_BATTERIES_PRODUCTLIST, id="Battery"
        ),
        pytest.param(CutterType.MOBILE.value, ConfiguratorState.INDUSTRIAL_CUTTING_HOSE_PRODUCTLIST, id="Mobile"),
        pytest.param(
            CutterType.STATIONARY.value, ConfiguratorState.INDUSTRIAL_CUTTING_HOSE_PRODUCTLIST, id="Stationary"
        ),
        pytest.param(None, ConfiguratorState.INDUSTRIAL_CUTTING_HOSE_PRODUCTLIST, id="No choice"),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_cutting_charger_batteries.ChargerBatteriesProductList.on_enter"
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_cutting_hose.HoseProductList.on_enter")
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_cutting_cutter.get_configuration_data_value"
)
def test_skip_transitions(mock_get_data: MagicMock, _, __, data: str, next_state: ConfiguratorState):
    session = UnifiedAlchemyMagicMock()
    mock_get_data.return_value = data

    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST, configuration, mock_jwt_token
    )

    configurator.skip()

    assert configurator.state == next_state
    mock_get_data.assert_called()
