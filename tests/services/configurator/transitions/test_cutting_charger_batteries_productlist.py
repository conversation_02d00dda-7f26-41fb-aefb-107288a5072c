from unittest.mock import patch

from fixtures.test_client import mock_db_user, mock_jwt_token
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.workflow import ConfiguratorFactory
from tests.fixtures.helpers import create_test_configurator

configuration = Configuration(user=mock_db_user())
patch_root = "holmatro_customer_portal.services.configurator.states"


@patch(f"{patch_root}.industrial_cutting_cutter.CutterProductList.on_enter")
def test_back_transitions(_):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_CUTTING_CHARGER_BATTERIES_PRODUCTLIST, configuration, mock_jwt_token
    )

    configurator.back()  # noqa

    assert configurator.state == ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST


@patch(f"{patch_root}.industrial_cutting_accessories.AccessoriesProductList.on_enter")
def test_next_transitions(_):
    session = UnifiedAlchemyMagicMock()

    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_CUTTING_CHARGER_BATTERIES_PRODUCTLIST, configuration, mock_jwt_token
    )

    configurator.next()  # noqa

    assert configurator.state == ConfiguratorState.INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST


@patch(f"{patch_root}.industrial_cutting_accessories.AccessoriesProductList.on_enter")
def test_skip_transitions(_):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_CUTTING_CHARGER_BATTERIES_PRODUCTLIST, configuration, mock_jwt_token
    )

    configurator.skip()  # noqa

    assert configurator.state == ConfiguratorState.INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST
