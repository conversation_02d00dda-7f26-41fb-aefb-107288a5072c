from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition, TransitionTrigger


def test_names():
    class TestTransition(BaseTransition):
        trigger = TransitionTrigger.NEXT
        source = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING
        dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS

    assert (
        TestTransition().condition_method_name
        == "condition_industrial_lifting_pump_settings_acting_next_industrial_lifting_pump_settings"
    )
    assert TestTransition().definition == {
        "trigger": TransitionTrigger.NEXT.value,
        "source": ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING,
        "dest": ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS,
        "conditions": "condition_industrial_lifting_pump_settings_acting_next_industrial_lifting_pump_settings",
        "unless": [],
    }
