from unittest.mock import MagicMock, patch

import pytest
from fixtures.configurator import ConfiguratorFactory
from fixtures.test_client import mock_db_user, mock_jwt_token
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Category, Configuration
from holmatro_customer_portal.services.configurator.states import ConfiguratorState


@pytest.mark.parametrize(
    "data, category_id, next_state",
    [
        pytest.param(
            ["existing"],
            "7411",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="existing hose",
        ),
        pytest.param(
            [None, "vari", "on_top"],
            "7411",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN,
            id="vari pump on top",
        ),
        pytest.param(
            [None, "vari", "separate"],
            "7411",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
            id="vari pump separate",
        ),
        pytest.param(
            [None, "hand"],
            "7411",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
            id="hand pump",
        ),
        pytest.param(
            [None, None],
            "7411",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
            id="unknown",
        ),
        pytest.param(
            None,
            "2838",
            ConfiguratorState.INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST,
            id="cutting configuration",
        ),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListPumpToPanel.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListMain.on_enter"
)
@patch("holmatro_customer_portal.services.configurator.utils.get_configuration_data_value")
def test_overview_back(
    mock_get_data: MagicMock,
    _: MagicMock,
    __: MagicMock,
    catalog_repo_mock: MagicMock,
    data: list,
    category_id: str,
    next_state: ConfiguratorState,
):
    session = UnifiedAlchemyMagicMock()
    configuration = Configuration(user=mock_db_user(), category=Category(category_id=category_id))
    mock_get_data.side_effect = data

    configurator = ConfiguratorFactory().get_configurator_for_state(
        session,
        ConfiguratorState.INDUSTRIAL_OVERVIEW,
        configuration,
        mock_jwt_token,
        catalog_repo_mock,
    )
    configurator.back()  # type: ignore

    assert configurator.state == next_state
