from unittest.mock import Mock, patch

import pytest
from fixtures.test_client import mock_db_user, mock_jwt_token
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.services.configurator.configurator_enums import CutterType
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.workflow import ConfiguratorFactory
from tests.fixtures.helpers import create_test_configurator

configuration = Configuration(user=mock_db_user())
patch_root = "holmatro_customer_portal.services.configurator"


@pytest.mark.parametrize(
    "data, back_state",
    [
        pytest.param(
            CutterType.MOBILE.value,
            ConfiguratorState.INDUSTRIAL_CUTTING_PUMP_PRODUCTLIST,
            id="Mobile cutter",
        ),
        pytest.param(
            CutterType.STATIONARY.value,
            ConfiguratorState.INDUSTRIAL_CUTTING_PUMP_PRODUCTLIST,
            id="Stationary cutter",
        ),
        pytest.param(
            CutterType.BATTERY.value,
            ConfiguratorState.INDUSTRIAL_CUTTING_CHARGER_BATTERIES_PRODUCTLIST,
            id="Battery cutter",
        ),
    ],
)
@patch(f"{patch_root}.transitions.industrial_cutting_accessories.get_configuration_data_value")
@patch(f"{patch_root}.states.industrial_cutting_charger_batteries.ChargerBatteriesProductList.on_enter")
@patch(f"{patch_root}.states.industrial_cutting_accessories.AccessoriesProductList.on_enter")
@patch(f"{patch_root}.states.industrial_cutting_pump.PumpProductList.on_enter")
def test_back_to_cutting_pump_transition(_, __, ___, mock_get_data: Mock, data: str, back_state: ConfiguratorState):
    session = UnifiedAlchemyMagicMock()
    mock_get_data.return_value = data

    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST, configuration, mock_jwt_token
    )

    configurator.back()  # noqa

    assert configurator.state == back_state


@patch(f"{patch_root}.states.industrial_overview.Overview.on_enter")
def test_next_transitions(_):
    session = UnifiedAlchemyMagicMock()

    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST, configuration, mock_jwt_token
    )

    configurator.next()  # noqa

    assert configurator.state == ConfiguratorState.INDUSTRIAL_OVERVIEW


@patch(f"{patch_root}.states.industrial_overview.Overview.on_enter")
def test_skip_transitions(_):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST, configuration, mock_jwt_token
    )

    configurator.skip()  # noqa

    assert configurator.state == ConfiguratorState.INDUSTRIAL_OVERVIEW
