from unittest.mock import AN<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ck, patch

from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.enums import RelationshipType
from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.services.configurator.states import AccessoriesProductList
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.workflow import ConfiguratorFactory
from holmatro_customer_portal.utils.enums import SyncforceCategory
from tests.fixtures.helpers import create_test_configurator
from tests.fixtures.test_client import mock_db_user, mock_jwt_token

configuration = Configuration(user=mock_db_user())
patch_root = "holmatro_customer_portal.services.configurator.states.industrial_cutting_accessories"


@patch(f"{patch_root}.get_configuration_products")
@patch(f"{patch_root}.get_related_products_with_translation")
def test_cutting_accessories_product_list_on_enter_with_selected_cutter(
    get_related_products_with_translation: Mo<PERSON>,
    get_configuration_products: Mock,
):
    mock_cutter = Mock(id="any_cutter_id")
    get_configuration_products.return_value = [mock_cutter]

    mock_product_preview_res_1 = Mock(name="mock_product_preview_res_1")
    mock_product_preview_res_2 = Mock(name="mock_product_preview_res_2")
    get_related_products_with_translation.return_value = [
        Mock(name="mock_related_product_res_1", product=mock_product_preview_res_1),
        Mock(name="mock_related_product_res_2", product=mock_product_preview_res_2),
    ]

    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST, configuration, mock_jwt_token
    )

    # Call the on_enter method for this state
    getattr(configurator, AccessoriesProductList().on_enter_method_name)(event=Mock())

    get_configuration_products.assert_called_once_with(
        ANY, ANY, ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST
    )
    get_related_products_with_translation.assert_called_once_with(
        mock_cutter.id,
        ANY,
        ANY,
        ANY,
        RelationshipType.CONFIGURATOR_ACCESSORIES.value,
        exclude_classifications=[2109, 2110],  # Battery and charger classifications should be excluded
    )

    for c in configurator.state_definition["content"]:
        if c["type"] == "products":
            assert c["meta"]["products"] == [mock_product_preview_res_1, mock_product_preview_res_2]
            break


@patch(f"{patch_root}.get_related_products_with_translation")
@patch(f"{patch_root}.get_configuration_products")
def test_cutting_accessories_product_list_on_enter_without_selected_cutter(
    get_configuration_products: Mock, get_related_products_with_translation: Mock, catalog_repo_mock: MagicMock
):
    get_configuration_products.return_value = []

    catalog_repo_mock.get_category_path.return_value = "any_category_path"

    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST,
        configuration,
        mock_jwt_token,
        catalog_repo_mock,
    )

    # Call the on_enter method for this state
    getattr(configurator, AccessoriesProductList().on_enter_method_name)(event=Mock())

    get_related_products_with_translation.assert_not_called()

    catalog_repo_mock.get_category_path.assert_called_once_with(
        ANY, SyncforceCategory.CUTTING_ACCESSORIES, SyncforceCategory.INDUSTRIAL_CUTTING
    )

    for c in configurator.state_definition["content"]:
        if c["type"] == "filters":
            assert c["meta"]["category"] == "any_category_path"
            break
