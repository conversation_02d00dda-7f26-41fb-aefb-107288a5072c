from unittest.mock import MagicMock, patch

import pytest
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Configuration, Product
from holmatro_customer_portal.schemas.configurator import ConfiguratorProduct, ConfiguratorStateProducts
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.workflow import ConfiguratorFactory
from tests.fixtures.helpers import create_test_configurator
from tests.fixtures.test_client import mock_catalog_repository, mock_db_user, mock_jwt_token

configuration = Configuration(user=mock_db_user())


@pytest.mark.parametrize(
    "pump_type, protection_frame, data, final_state",
    [
        pytest.param(
            "vari",
            None,
            ["no"],
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN,
            id="vari pump - not chosen protection frame/cover 1 ",
        ),
        pytest.param(
            "vari",
            None,
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME,
            id="vari pump - not chosen protection frame/cover 2",
        ),
        pytest.param(
            "vari",
            Product(),
            ["no", "yes"],
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME,
            id="vari pump - chosen protection frame but not cover 1",
        ),
        pytest.param(
            "vari",
            Product(),
            [None],
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_COVER,
            id="vari pump - chosen protection frame but not cover 2",
        ),
        pytest.param(
            "vari",
            Product(),
            ["yes"],
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_COVER,
            id="vari pump - chosen protection frame and cover",
        ),
        pytest.param(
            "hand", None, None, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS, id="hand pump"
        ),
        pytest.param(
            "compact",
            None,
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="compact pump",
        ),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsCover.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsProtectionFrame.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListMain.on_enter"
)
@patch("holmatro_customer_portal.services.configurator.utils.get_configuration_data_value")
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_hose_productlist_pump_to_panel.get_or_query_products"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_hose_productlist_pump_to_panel.get_or_query_pump_type"
)
def test_back_transitions(
    mock_get_pump_type: MagicMock,
    mock_get_protection_frame_product: MagicMock,
    mock_get_data: MagicMock,
    _,
    __,
    ___,
    pump_type: str | None,
    protection_frame: Product | None,
    data: str | None,
    final_state: ConfiguratorState,
):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
        configuration,
        mock_jwt_token,
    )

    mock_get_pump_type.return_value = pump_type
    mock_get_protection_frame_product.return_value = protection_frame
    mock_get_data.side_effect = data

    configurator.back()

    assert configurator.state == final_state


@patch("holmatro_customer_portal.services.configurator.states.industrial_overview.Overview.on_enter")
def test_next_transitions(mock_get_product_hoses: MagicMock):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
        configuration,
        mock_jwt_token,
    )

    configurator.next()

    assert configurator.state == ConfiguratorState.INDUSTRIAL_OVERVIEW


@patch("holmatro_customer_portal.services.configurator.states.industrial_overview.Overview.on_enter")
def test_skip_transitions(mock_get_product_hoses: MagicMock):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
        configuration,
        mock_jwt_token,
    )

    configurator.skip()

    assert configurator.state == ConfiguratorState.INDUSTRIAL_OVERVIEW


@pytest.mark.parametrize(
    "pump_type, pump_quantity, cylinder_acting_type, hose_type, filter, default_product",
    [
        pytest.param(
            "vari",
            {"products": [], "total_quantity": 1},
            "single",
            None,
            "extension hose",
            "100.572.302",
            id="vari pump",
        ),
        pytest.param(
            "vari",
            {"products": [], "total_quantity": 1},
            "double",
            None,
            "extension hose",
            "100.572.302",
            id="vari pump - double acting type",
        ),
        pytest.param(
            None, {"products": [], "total_quantity": 4}, "single", None, "extension hose", "100.572.302", id="unknown"
        ),
        pytest.param(
            "hand",
            {"products": [], "total_quantity": 1},
            "single",
            "mounted",
            "standard hose",
            "100.572.102",
            id="hand pump mounted",
        ),
        pytest.param(
            "compact",
            {"products": [], "total_quantity": 1},
            "single",
            "extension",
            "extension hose",
            "100.572.302",
            id="compact pump extension",
        ),
        pytest.param(
            "compact",
            {"products": [], "total_quantity": 1},
            "single",
            None,
            "standard hose|extension hose",
            "100.572.302",
            id="unknown hose type",
        ),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.hose_productlist.get_or_query_value"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.hose_productlist.get_or_query_cylinder_acting_type"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.hose_productlist.get_product_quantities"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.hose_productlist.get_or_query_pump_type"
)
@patch("holmatro_customer_portal.services.configurator.workflow.Configurator.set_default_product")
def test_on_enter(
    mock_set_default_product: MagicMock,
    mock_get_pump_type: MagicMock,
    mock_get_pump_quantity: MagicMock,
    mock_get_cylinder_acting_type: MagicMock,
    mock_get_data: MagicMock,
    pump_type: str,
    pump_quantity: dict,
    cylinder_acting_type: str,
    hose_type: str,
    filter: str,
    default_product: str,
):
    session = UnifiedAlchemyMagicMock()
    mock_get_pump_type.return_value = pump_type
    mock_get_pump_quantity.return_value = pump_quantity
    mock_get_cylinder_acting_type.return_value = cylinder_acting_type
    mock_get_data.return_value = hose_type

    mock_catalog_repository.get_category_path.return_value = "path"

    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
        configuration,
        mock_jwt_token,
        mock_catalog_repository,
    )
    configurator.next()

    pump_total_quantity = pump_quantity.get("total_quantity")
    hose_quantity = pump_total_quantity if cylinder_acting_type == "single" else pump_total_quantity * 2
    mock_set_default_product.assert_called_once_with(default_product)
    for c in configurator.state_definition["content"]:
        if c["type"] == "products":
            assert c["meta"]["initial"] == hose_quantity
        if c["type"] == "filters":
            assert c["meta"]["filters"] == {f"{configuration.user.language.code}-hose-type": filter}
            assert c["meta"]["category"] == "path"


@pytest.mark.parametrize(
    "hose, hose_quantity, pump_type, hose_type, connector",
    [
        pytest.param(
            Product(),
            {"products": [], "total_quantity": 2},
            "vari",
            None,
            "100.581.101",
            id="vari pump",
        ),
        pytest.param(
            Product(),
            {"products": [], "total_quantity": 4},
            "hand",
            "extension",
            "150.581.218",
            id="hand pump",
        ),
        pytest.param(
            Product(),
            {"products": [], "total_quantity": 1},
            "compact",
            "extension",
            "100.581.101",
            id="compact",
        ),
        pytest.param(
            Product(),
            {"products": [], "total_quantity": 1},
            "compact",
            "mounted",
            None,
            id="compact mounted - no couplers added",
        ),
        pytest.param(
            None,
            None,
            None,
            None,
            None,
            id="no hose found",
        ),
    ],
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_overview.Overview.on_enter")
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.hose_couplers.ConfiguratorMapper.store_configuration_product"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.hose_couplers.get_or_query_value"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.hose_couplers.get_configuration_pump_type"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.hose_couplers.get_product_quantities"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.hose_couplers.get_or_query_products"
)
def test_on_exit(
    mock_get_hose: MagicMock,
    mock_get_hose_quantity: MagicMock,
    mock_get_pump_type: MagicMock,
    mock_get_hose_type: MagicMock,
    mock_db_add_coupler: MagicMock,
    mock_overview: MagicMock,
    hose: Product,
    hose_quantity: dict,
    pump_type: str,
    hose_type: str | None,
    connector: str,
):
    mock_get_hose.return_value = hose
    mock_get_hose_quantity.return_value = hose_quantity
    mock_get_pump_type.return_value = pump_type
    mock_get_hose_type.return_value = hose_type

    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
        configuration,
        mock_jwt_token,
    )

    configurator.next()

    if not connector:
        mock_db_add_coupler.assert_not_called()
    else:
        mock_db_add_coupler.assert_called_once_with(
            configuration,
            ConfiguratorStateProducts(
                state_id=ConfiguratorState.INDUSTRIAL_LIFTING_HOSE_ADDITIONAL_PRODUCTS.value,
                products=[
                    ConfiguratorProduct(
                        article_number="100.181.119",
                        quantity=hose_quantity.get("total_quantity"),
                        product_id=None,
                    ),
                    ConfiguratorProduct(
                        article_number=connector, quantity=hose_quantity.get("total_quantity"), product_id=None
                    ),
                ],
            ),
        )
