import copy
import uuid
from datetime import datetime
from unittest.mock import ANY, MagicMock, patch

import pytest
from fixtures.product_mapper_factory import MockProductMapper
from fixtures.query_factory import MockQueryFactory
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Category, Configuration, ConfigurationProduct
from holmatro_customer_portal.schemas.configurator import ConfiguratorProductRes, OverviewDetails, OverviewPrice
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorOverviewDataIDs, ConfiguratorState
from holmatro_customer_portal.services.configurator.workflow import ConfiguratorFactory
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient
from tests.fixtures.helpers import create_test_configurator
from tests.fixtures.my_hm_fixture import my_hm_mock_responses
from tests.fixtures.responses_fixtures import mock_overview_product, mock_product_preview
from tests.fixtures.test_client import mock_database, mock_db_user, mock_jwt_token

product_id = uuid.uuid4()
configuration_id = uuid.uuid4()


unfinished_configuration = Configuration(
    id=configuration_id,
    category=Category(category_id="7422"),
    user=mock_db_user(),
    started_at=datetime.now(),
    quotation_number="1",
    reference="unfinished configuration",
    products=[ConfigurationProduct(product_id=product_id, state_id="cylinder.productlist")],
)

finished_configuration = Configuration(
    id=configuration_id,
    category=Category(category_id="7422"),
    user=mock_db_user(),
    started_at=datetime.now(),
    quotation_number="1",
    reference="finished configuration",
    products=[ConfigurationProduct(product_id=product_id, state_id="cylinder.productlist")],
    finished_at=datetime.now(),
    last_updated_state=ConfiguratorState.INDUSTRIAL_OVERVIEW.value,
)


@pytest.mark.parametrize(
    "configuration, state",
    [
        (
            unfinished_configuration,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
        ),
        (finished_configuration, ConfiguratorState.INDUSTRIAL_OVERVIEW),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_overview.ProductPreviewMapper.configurator_product_preview_mapper"
)
def test_on_enter(
    mock_product_mapper: MagicMock,
    configuration: Configuration,
    state: ConfiguratorState,
):
    mock_database.reset_mock()
    mock_database.add(configuration)

    configurator = create_test_configurator(mock_database, state, configuration, mock_jwt_token)
    product_preview = copy.deepcopy(mock_product_preview)
    product_preview.update({"quantity": 3, "total_price_net": "300.00", "total_price_gross": "600.00"})

    mock_product_mapper.return_value = [ConfiguratorProductRes(**product_preview)]

    if not configuration.finished_at:
        old_configuration = mock_database.query(Configuration).filter_by(id=configuration_id).all()
        assert old_configuration[0].finished_at is None

    configurator.next()  # type: ignore

    if configuration.reference == "unfinished configuration":
        mock_database.merge.assert_called_once()
        mock_database.commit.assert_called_once()
    else:
        mock_database.merge.assert_not_called()
        mock_database.commit.assert_not_called()

    updated_configuration = mock_database.query(Configuration).filter_by(id=configuration_id).all()
    assert updated_configuration[0].finished_at is not None
    assert updated_configuration[0].last_updated_state == ConfiguratorState.INDUSTRIAL_OVERVIEW.value

    for content in configurator.state_definition["content"]:
        if content["id"] == ConfiguratorOverviewDataIDs.OVERVIEW_PRODUCTLIST.value:
            assert content["meta"]["products"] == [ConfiguratorProductRes(**product_preview)]
        if content["id"] == ConfiguratorOverviewDataIDs.OVERVIEW_DETAILS.value:
            assert content["meta"]["overview"] == OverviewDetails(
                configuration_id=configuration.id,
                category_id=7422,
                started_at=configuration.started_at,
                quotation_number="1",
                reference=configuration.reference,
                progress=100,
                price_net=OverviewPrice(configuration="300.00"),
                price_gross=OverviewPrice(configuration="600.00"),
                tags=[],
            )

    mock_database.query(Configuration).delete()
    mock_database.reset_mock()


@patch("holmatro_customer_portal.services.configurator.states.industrial_overview.get_configurator_products")
def test_sort_products(mock_get_product_previews: MagicMock, my_hm_mock_responses):
    mock_my_hm_client = MyHolmatroPortalApiClient("jwt_token")
    session = UnifiedAlchemyMagicMock()
    product_ids = [uuid.uuid4() for _ in range(3)]
    positions = [1, 2, 7]
    configuration = Configuration(
        user=mock_db_user(),
        category=Category(category_id=1),
        id=uuid.uuid4(),
        reference="test",
        started_at=datetime.now(),
        products=[
            ConfigurationProduct(product_id=pid, position=position) for pid, position in zip(product_ids, positions)
        ],
    )

    mock_database.add(configuration)
    overview_product_mock = MockProductMapper().get_mock_configurator_product().model_dump()
    del overview_product_mock["product_id"]

    # Mock unordered result from get_product_previews query
    products = [
        MockQueryFactory(product_id=product_ids[1]).get_mock_configurator_product(),
        MockQueryFactory(product_id=product_ids[2]).get_mock_configurator_product(),
        MockQueryFactory(product_id=product_ids[0]).get_mock_configurator_product(),
    ]
    mock_get_product_previews.return_value = products

    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
        configuration,
        mock_my_hm_client,
    )
    configurator.next()  # type: ignore

    for content in configurator.state_definition["content"]:
        if content["id"] == ConfiguratorOverviewDataIDs.OVERVIEW_PRODUCTLIST.value:
            assert content["meta"]["products"] == [
                MockProductMapper(product_id=pid).get_mock_configurator_product() for pid in product_ids
            ]

    mock_database.query(Configuration).delete()


@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_overview.ProductPreviewMapper.configurator_product_preview_mapper"
)
def test_configuration_product_prices(mock_product_mapper: MagicMock):
    session = UnifiedAlchemyMagicMock()
    configuration = Configuration(
        user=mock_db_user(),
        category=Category(category_id=1),
        id=uuid.uuid4(),
        reference="test",
        started_at=datetime.now(),
        products=[ConfigurationProduct(product_id=pid) for pid in [uuid.uuid4() for _ in range(3)]],
    )

    product_preview = copy.deepcopy(mock_overview_product)
    product_preview_no_price = copy.deepcopy(mock_overview_product)
    product_preview_no_price.update({"quantity": 1, "total_price_net": None, "total_price_gross": None})

    mock_product_mapper.return_value = [
        ConfiguratorProductRes(**product_preview_no_price),
        ConfiguratorProductRes(**product_preview),
    ]

    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
        configuration,
        ANY,
    )

    configurator.next()  # type: ignore

    for content in configurator.state_definition["content"]:
        if content["id"] == ConfiguratorOverviewDataIDs.OVERVIEW_PRODUCTLIST.value:
            assert len(content["meta"]["products"]) == 2
            assert content["meta"]["products"][0].total_price_net is None
            assert content["meta"]["products"][0].total_price_gross is None

    for content in configurator.state_definition["content"]:
        if content["id"] == ConfiguratorOverviewDataIDs.OVERVIEW_DETAILS.value:
            assert content["meta"]["overview"].price_net.configuration is None
            assert content["meta"]["overview"].price_gross.configuration is None

    mock_database.query(Configuration).delete()
