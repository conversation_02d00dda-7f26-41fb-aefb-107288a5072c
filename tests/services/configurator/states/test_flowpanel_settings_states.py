from unittest.mock import MagicMock, patch

import pytest
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.workflow import ConfiguratorFactory
from tests.fixtures.helpers import create_test_configurator
from tests.fixtures.test_client import mock_db_user, mock_jwt_token

configuration = Configuration(user=mock_db_user())


@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.get_product_valves")
def test_back_transitions(_):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS,
        configuration,
        mock_jwt_token,
    )

    configurator.back()
    assert configurator.state == ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE


@pytest.mark.parametrize(
    "gaugeset_selection, state",
    [
        pytest.param(
            ["yes"], ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET, id="selected to choose gaugeset"
        ),
        pytest.param(
            ["no", None],
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="selected to NOT choose gaugeset",
        ),
        pytest.param(
            [None, None],
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="no selection",
        ),
        pytest.param(
            [None, "existing"],
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN,
            id="vari pump existing",
        ),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsGaugeset.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListMain.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsFlowPanelProductList.on_enter"
)
@patch("holmatro_customer_portal.services.configurator.utils.get_configuration_data_value")
def test_next_transitions(
    mock_get_data: MagicMock,
    mock_flowpanel_on_enter: MagicMock,
    mock_hose_on_enter: MagicMock,
    mock_gauge_on_enter: MagicMock,
    gaugeset_selection: str | None,
    state: ConfiguratorState,
):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS,
        configuration,
        mock_jwt_token,
    )

    mock_get_data.side_effect = gaugeset_selection

    configurator.next()
    assert configurator.state == state


@pytest.mark.parametrize(
    "gaugeset_selection, state",
    [
        pytest.param(
            "yes", ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET, id="selected to choose gaugeset"
        ),
        pytest.param(
            "no",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="selected to NOT choose gaugeset",
        ),
        pytest.param(
            None, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST, id="no selection"
        ),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsGaugeset.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsFlowPanelProductList.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_flowpanel_settings.get_configuration_data_value"
)
def test_skip_transitions(
    mock_get_gaugeset_selection: MagicMock,
    _,
    __,
    gaugeset_selection: str | None,
    state: ConfiguratorState,
):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS,
        configuration,
        mock_jwt_token,
    )

    mock_get_gaugeset_selection.return_value = gaugeset_selection

    configurator.skip()
    assert configurator.state == state
