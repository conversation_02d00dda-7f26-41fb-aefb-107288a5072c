from unittest.mock import MagicMock, patch

import pytest
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.states.system_components_helpers.valve import get_product_valves
from holmatro_customer_portal.services.configurator.workflow import ConfiguratorFactory
from tests.fixtures.database_models_fixture import mocked_user
from tests.fixtures.helpers import create_test_configurator
from tests.fixtures.test_client import mock_db_user, mock_jwt_token

configuration = Configuration(user=mock_db_user())


@pytest.mark.parametrize(
    "pump_type, configuration_data, cylinder_quantity, final_state",
    [
        pytest.param(
            "hand",
            ["yes", "yes", "yes"],  # hand_pump_gaugeset_selection
            1,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET,
            id="hand pump double acting - choose gauge",
        ),
        pytest.param(
            "hand",
            ["no"],  # hand_pump_gaugeset_selection
            1,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="hand pump double acting - NOT choose gauge - ONE cylinder",
        ),
        pytest.param(
            "hand",
            ["no", "no"],  # hand_pump_gaugeset_selection
            2,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="hand pump double acting - NOT choose gauge - more than ONE cylinder",
        ),
        pytest.param(
            "vari",
            [None, None],  # hand_pump_gaugeset_selection
            2,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS,
            id="vari pump",
        ),
        pytest.param(
            None,
            [None, None, None],  # hand_pump_gaugeset_selection
            2,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET,
            id="unknown",
        ),
    ],
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_overview.Overview.on_enter")
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsFlowPanelProductList.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsGaugeset.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_valves.get_or_query_value"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_valves.get_or_query_cylinder_quantity"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_valves.get_configuration_pump_type"
)
def test_valves_next_transitions(
    mock_get_pump_type: MagicMock,
    mock_get_cylinder_quantity: MagicMock,
    mock_get_data: MagicMock,
    _,
    __,
    ___,
    pump_type: str,
    configuration_data: str | None,
    cylinder_quantity: int | None,
    final_state: ConfiguratorState,
):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE, configuration, mock_jwt_token
    )

    mock_get_pump_type.return_value = pump_type
    mock_get_data.side_effect = configuration_data
    mock_get_cylinder_quantity.return_value = cylinder_quantity

    configurator.next()

    assert configurator.state == final_state


@pytest.mark.parametrize(
    "pump_type, configuration_data, cylinder_quantity, final_state",
    [
        pytest.param(
            "hand",
            ["yes"],  # hand_pump_gaugeset_selection
            1,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET,
            id="hand pump double acting - choose gauge",
        ),
        pytest.param(
            "hand",
            ["yes", "yes", "yes"],  # hand_pump_gaugeset_selection
            2,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET,
            id="hand pump double acting - choose gauge - more than ONE cylinder",
        ),
        pytest.param(
            "hand",
            ["no"],  # hand_pump_gaugeset_selection
            1,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="hand pump double acting - NOT choose gauge - ONE cylinder",
        ),
        pytest.param(
            "hand",
            ["no", "no"],  # hand_pump_gaugeset_selection
            2,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="hand pump double acting - NOT choose gauge - more than ONE cylinder",
        ),
        pytest.param(
            "vari",
            [None, None],  # hand_pump_gaugeset_selection
            2,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS,
            id="vari pump",
        ),
    ],
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_overview.Overview.on_enter")
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsGaugeset.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsFlowPanelProductList.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_valves.get_or_query_value"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_valves.get_or_query_cylinder_quantity"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_valves.get_configuration_pump_type"
)
def test_valves_skip_transitions(
    mock_get_pump_type: MagicMock,
    mock_get_cylinder_quantity: MagicMock,
    mock_get_data: MagicMock,
    _,
    __,
    ___,
    pump_type: str,
    configuration_data: str | None,
    cylinder_quantity: int | None,
    final_state: ConfiguratorState,
):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE, configuration, mock_jwt_token
    )

    mock_get_pump_type.return_value = pump_type
    mock_get_data.side_effect = configuration_data
    mock_get_cylinder_quantity.return_value = cylinder_quantity

    configurator.skip()
    assert configurator.state == final_state


@pytest.mark.parametrize(
    "pump_type, cylinder_acting_type, call",
    [
        pytest.param(
            "hand",
            "double",
            "100.182.175",
            id="valve hand pump double acting type",
        ),
        pytest.param(
            "vari",
            "single",
            "100.181.311",
            id="valve vari pump with single gravity cylinder model 06T",
        ),
        pytest.param(
            "vari",
            "double",
            "100.181.322",
            id="valve vari pump with double hydraulic cylinder",
        ),
        pytest.param(
            None,
            None,
            ["100.182.175", "100.181.311", "100.181.322"],
            id="hand single",
        ),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.valve.ProductPreviewMapper.product_preview_mapper"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.valve.get_product_preview_by_article_number"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.valve.get_or_query_cylinder_acting_type"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.valve.get_configuration_pump_type"
)
def test_on_enter(
    mock_pump_type: MagicMock,
    mock_cylinder_acting_type: MagicMock,
    mock_get_products: MagicMock,
    mock_product_preview_mapper: MagicMock,
    pump_type: str,
    cylinder_acting_type: str,
    call: list,
):
    session = UnifiedAlchemyMagicMock()
    mock_get_products.get_products.return_value = []
    mock_cylinder_acting_type.return_value = cylinder_acting_type
    mock_pump_type.return_value = pump_type
    mock_product_preview_mapper.return_value = []

    get_product_valves(session, configuration, mock_jwt_token, {})

    mock_get_products.assert_called_once_with(call, mocked_user, session)


@pytest.mark.parametrize(
    "number_of_pumps", [{"products": [], "total_quantity": 1}, {"products": [], "total_quantity": 10}, None]
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.get_product_quantities"
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.get_product_valves")
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_pump.get_or_query_value",
    return_value="yes",
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_pump.get_or_query_pump_type",
    return_value="hand",
)
def test_valve_initial_quantity(
    mock_transition_get_pump_type,
    mock_transition_get_valve_selection,
    mock_get_products,
    mock_get_number_of_pumps,
    number_of_pumps,
):
    mock_get_number_of_pumps.return_value = number_of_pumps
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST, configuration, mock_jwt_token
    )

    configurator.next()

    for c in configurator.state_definition["content"]:
        if c["type"] == "products":
            assert c["meta"]["initial"] == number_of_pumps or 1
