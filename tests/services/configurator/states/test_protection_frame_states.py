from unittest.mock import MagicMock, patch

import pytest
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Configuration, Product
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.workflow import ConfiguratorFactory
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient
from tests.fixtures.database_models_fixture import mocked_user
from tests.fixtures.helpers import create_test_configurator
from tests.fixtures.my_hm_fixture import my_hm_mock_responses
from tests.fixtures.test_client import mock_db_user, mock_jwt_token

configuration = Configuration(user=mock_db_user())


@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListMain.on_enter"
)
def test_back_transitions(_):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME, configuration, mock_jwt_token
    )

    configurator.back()
    assert configurator.state == ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN


@pytest.mark.parametrize(
    "product, data, next_state",
    [
        pytest.param(
            Product(),
            "no",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
            id="selected to not choose a cover",
        ),
        pytest.param(
            Product(),
            "yes",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_COVER,
            id="selected to choose a cover",
        ),
        pytest.param(
            None,
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
            id="frame not selected",
        ),
        pytest.param(
            Product(), None, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_COVER, id="cover selection not made"
        ),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsCover.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListPumpToPanel.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_protection_frame.get_or_query_value"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_protection_frame.get_or_query_products"
)
def test_next_transitions(
    mock_get_product, mock_get_data, _, __, product: Product(), data, next_state: ConfiguratorState
):
    session = UnifiedAlchemyMagicMock()
    mock_get_product.return_value = product
    mock_get_data.return_value = data

    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME, configuration, mock_jwt_token
    )

    configurator.next()
    assert configurator.state == next_state


@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListPumpToPanel.on_enter"
)
def test_skip_transitions(_):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME, configuration, mock_jwt_token
    )

    configurator.skip()
    assert configurator.state == ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL


@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListMain.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_hose_productlist_main.get_or_query_value",
    return_value="yes",
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_hose_productlist_main.get_or_query_pump_type",
    return_value="vari",
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.get_product_preview_by_article_number",
    return_value=[],
)
def test_on_enter(mock_get_products: MagicMock, _, __, ___, my_hm_mock_responses):
    session = UnifiedAlchemyMagicMock()

    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN,
        configuration,
        MyHolmatroPortalApiClient("jwt_token"),
    )
    configurator.next()

    mock_get_products.assert_called_once_with("100.151.056", mocked_user, session)
