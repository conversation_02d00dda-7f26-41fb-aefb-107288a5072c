import copy
import uuid
from unittest.mock import ANY, MagicMock, call, patch

import pytest
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.enums import Currency
from holmatro_customer_portal.database.models import Configuration, Language, Product, User
from holmatro_customer_portal.schemas.response_schema import ProductPreviewRes
from holmatro_customer_portal.services.configurator.default_products import Pumps
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorPumpStageDataIDs, ConfiguratorState
from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import FacetsAttributes, ProductsResponse
from tests.fixtures.helpers import create_test_configurator
from tests.fixtures.my_hm_fixture import my_hm_mock_responses
from tests.fixtures.query_factory import MockQueryFactory
from tests.fixtures.test_client import mock_jwt_token
from tests.fixtures.tweakwise_fixtures import mock_tweakwise_response_properties

mocked_language = Language(id=uuid.uuid4(), language_code="en")
preffered_mocked_language = Language(id=uuid.uuid4(), language_code="nl")
mocked_user = User(
    id=uuid.uuid4(),
    name="User",
    email="<EMAIL>",
    last_name="Mock",
    main_language_id=mocked_language.id,
    main_language=mocked_language,
    currency=Currency.USD,
    favorite_products=[],
    preferred_language_id=preffered_mocked_language.id,
    preferred_language=preffered_mocked_language,
    configurations=[],
)
configuration = Configuration(user=mocked_user)


@pytest.mark.parametrize(
    "data, number_of_cylinders, pump_type, acting_type, category_call, filters, preview_call",
    [
        pytest.param(
            [1],  # number_of_pumps
            1,
            "hand",
            None,
            call(
                mocked_user.language.code, SyncforceCategory.HAND_AND_FOOT_PUMPS, SyncforceCategory.INDUSTRIAL_LIFTING
            ),
            {"capacity-oil": f"2-50000", "hie-pump-operator-type-multilanguage": "hand"},
            None,
            id="hand pump",
        ),
        pytest.param(
            [1, "electric"],  # number_of_pumps / powersource
            3,
            "compact",
            None,
            None,
            None,
            call(Pumps.COMPACT_PUMP_ELECTRIC.value, ANY, ANY),
            id="compact pump electric",
        ),
        pytest.param(
            [1, "air"],  # number_of_pumps / powersource
            1,
            "compact",
            None,
            call(mocked_user.language.code, SyncforceCategory.COMPACT_PUMPS, SyncforceCategory.INDUSTRIAL_LIFTING),
            {"capacity-oil": f"2-50000", "hie-pump-operator-type-multilanguage": "air"},
            None,
            id="compact pump air",
        ),
        pytest.param(
            [1, None],  # number_of_pumps / powersource
            1,
            "compact",
            None,
            call(mocked_user.language.code, SyncforceCategory.COMPACT_PUMPS, SyncforceCategory.INDUSTRIAL_LIFTING),
            {"capacity-oil": f"2-50000"},
            None,
            id="compact pump unknown power",
        ),
        pytest.param(
            [2],  # number_of_pumps
            4,
            "vari",
            "double",
            call(mocked_user.language.code, SyncforceCategory.VARI_PUMPS, SyncforceCategory.INDUSTRIAL_LIFTING),
            {"number-of-outputs": 1, "capacity-oil": f"4-50000"},
            None,
            id="vari pump double acting",
        ),
        pytest.param(
            [2],  # number_of_pumps
            4,
            "vari",
            "single",
            call(mocked_user.language.code, SyncforceCategory.VARI_PUMPS, SyncforceCategory.INDUSTRIAL_LIFTING),
            {
                "number-of-outputs": 1,
                "capacity-oil": f"4-50000",
                "first-stage-output-min-cc": "0-3200",
                "first-stage-output-min-oz": "0-105",
            },
            None,
            id="vari pump single acting",
        ),
        pytest.param(
            [1],  # number_of_pumps
            1,
            None,
            None,
            call(mocked_user.language.code, SyncforceCategory.HYDRAULIC_PUMPS, SyncforceCategory.INDUSTRIAL_LIFTING),
            {"capacity-oil": f"2-50000"},
            None,
            id="fallback",
        ),
        pytest.param(
            [None],  # number_of_pumps
            1,
            None,
            None,
            call(mocked_user.language.code, SyncforceCategory.HYDRAULIC_PUMPS, SyncforceCategory.INDUSTRIAL_LIFTING),
            {"capacity-oil": f"2-50000"},
            None,
            id="no number of pumps chosen, falls back to number of cylinders",
        ),
    ],
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.TweakwiseClient")
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.get_product_preview_by_article_number"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.get_or_query_cylinder_acting_type"
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.get_or_query_pump_type")
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.get_configuration_product_attribute"
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.get_or_query_cylinder_quantity")
@patch("holmatro_customer_portal.services.configurator.utils.get_configuration_data_value")
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_cylinder.get_configuration_cylinder_acting_type",
    return_value="single",
)
def test_productlist_on_enter(
    mock_get_cylinder_on_transition,
    mock_get_data,
    mock_get_quantity,
    mock_get_attribute,
    mock_get_pump_type,
    mock_get_acting_type,
    mock_get_preview,
    mock_tweakwise,
    my_hm_mock_responses,
    catalog_repo_mock: MagicMock,
    data,
    number_of_cylinders,
    pump_type,
    acting_type,
    category_call,
    filters,
    preview_call,
):
    session = UnifiedAlchemyMagicMock()
    catalog_repo_mock.get_category_path.return_value = "path"
    catalog_repo_mock.get_facet_attributes.return_value = [
        FacetsAttributes(title="400", is_selected=False, no_of_results=0, attribute_id=None, url=None),
        FacetsAttributes(title="50000", is_selected=False, no_of_results=0, attribute_id=None, url=None),
    ]
    mock_get_attribute.return_value = "1.5"  # cylinder_content
    mock_get_quantity.return_value = number_of_cylinders
    mock_get_pump_type.return_value = pump_type
    mock_get_acting_type.return_value = acting_type
    mock_get_preview.return_value = [MockQueryFactory().get_mock_product_preview()]
    mock_get_data.side_effect = data

    mock_my_hm_client = MyHolmatroPortalApiClient("jwt_token")
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS, configuration, mock_my_hm_client, catalog_repo_mock
    )
    configurator.next()

    if category_call:
        catalog_repo_mock.get_category_path.assert_has_calls([category_call])
    else:
        catalog_repo_mock.get_category_path.assert_not_called()

    if preview_call:
        mock_get_preview.assert_has_calls([preview_call])
        for c in configurator.state_definition["content"]:
            if c["type"] == "products":
                assert len(c["meta"]["products"]) == 1
                assert isinstance(c["meta"]["products"][0], ProductPreviewRes)

    else:
        mock_get_preview.assert_not_called()

    if filters:
        for c in configurator.state_definition["content"]:
            if c["type"] == "filters":
                assert c["meta"]["filters"] == filters

    for c in configurator.state_definition["content"]:
        if c["type"] == "products":
            assert c["meta"]["initial"] == data[0] if data[0] else 1
            assert c["meta"]["maximum"] == number_of_cylinders


@pytest.mark.parametrize(
    "configuration_data, cylinder_acting_type_response, acting_type",
    [
        pytest.param(None, ["double"], "double", id="double"),
        pytest.param(None, ["single"], "single", id="single"),
        pytest.param(
            None,
            [None, "hydraulic"],
            "double",
            id="unknown acting type - hydraulic return type",
        ),
        pytest.param(
            None,
            [None, "spring"],
            "single",
            id="unknown acting type - hydraulic return type",
        ),
        pytest.param(["single"], [None, None], "single", id="existing cylinder - single"),
        pytest.param(["double"], [None, None], "double", id="existing cylinder - double"),
        pytest.param(["single"], [None, None], "single", id="unknown acting type - hydraulic return type"),
    ],
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.TweakwiseClient.get_products")
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.get_or_query_value")
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.get_configuration_product_attribute"
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.get_or_query_cylinder_quantity")
@patch("holmatro_customer_portal.services.configurator.utils.get_configuration_data_value")
@patch("holmatro_customer_portal.services.configurator.utils.get_configuration_product_attribute")
def test_settings_acting_type_on_enter(
    mock_acting,
    mock_get_data,
    mock_quantity,
    mock_get_required_oil,
    mock_get_number_pumps,
    mock_get_products,
    configuration_data,
    cylinder_acting_type_response,
    acting_type,
):
    product = {
        "items": [
            {
                "itemno": "1",
                "title": "product 1",
                "brand": "brand name",
            }
        ],
        "properties": mock_tweakwise_response_properties,
        "redirects": [],
    }

    mock_get_data.side_effect = configuration_data
    mock_acting.side_effect = cylinder_acting_type_response
    mock_quantity.return_value = 1
    mock_get_required_oil.return_value = 1
    mock_get_number_pumps.return_value = 1
    mock_get_products.side_effect = [
        ProductsResponse(**product),
        ProductsResponse(**product),
        ProductsResponse(**{**copy.deepcopy(product), "items": []}),
    ]
    session = UnifiedAlchemyMagicMock()

    # Create a mock catalog repository with get_category_path method
    from unittest.mock import MagicMock

    from holmatro_customer_portal.domain.repositories.catalog_repository import CatalogRepository

    mock_catalog_repository = MagicMock(spec=CatalogRepository)
    mock_catalog_repository.get_category_path.side_effect = "path"

    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING,
        configuration,
        mock_jwt_token,
        mock_catalog_repository,
    )
    configurator.next()

    content = {c["id"]: c for c in configurator.state_definition["content"]}

    assert ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_NUMBER_OF_PUMPS.value not in content
    if acting_type == "single":
        assert ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_VALVE.value not in content.keys()
        assert ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_TYPE_COMPACT.value in [
            c["title"] for c in content[ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_TYPE.value]["meta"]["choices"]
        ]
    elif acting_type == "double":
        assert ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_VALVE.value in content.keys()
        assert ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_TYPE_COMPACT.value not in [
            c["title"] for c in content[ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_TYPE.value]["meta"]["choices"]
        ]


@pytest.mark.parametrize(
    "cylinder, cylinder_quantity",
    [
        (pytest.param(None, None, id="no cylinder")),
        (pytest.param(Product(), 3, id="more than one cylinder")),
        (pytest.param(Product(), 1, id="one cylinder")),
    ],
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.PumpSettings.on_enter")
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.get_configuration_products")
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.get_or_query_cylinder_quantity")
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_cylinder.get_or_query_cylinder_quantity"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_cylinder.get_configuration_cylinder_acting_type"
)
def test_settings_pump_cylinder_quantity_on_enter(
    mock_acting,
    mock_quantity_on_transition,
    mock_quantity_on_pump_state,
    mock_get_cylinder,
    mock_pump_settings_on_enter,
    cylinder,
    cylinder_quantity: int,
):
    mock_acting.return_value = cylinder
    mock_get_cylinder.return_value = cylinder
    mock_quantity_on_transition.return_value = cylinder_quantity
    mock_quantity_on_pump_state.return_value = cylinder_quantity
    session = UnifiedAlchemyMagicMock()

    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST, configuration, mock_jwt_token
    )
    configurator.next()  # type: ignore
    content = {c["id"]: c for c in configurator.state_definition["content"]}

    if not cylinder:
        assert ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_NUMBER_OF_PUMPS.value not in content
        assert len(configurator.state_definition["content"]) == 1

    else:
        if cylinder_quantity > 1:
            assert ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_ACTING_PLURALITY.value not in content
            assert len(configurator.state_definition["content"]) == 1
            assert content[ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_NUMBER_OF_PUMPS.value]["meta"]["options"] == [
                1,
                2,
                3,
            ]
        else:
            mock_pump_settings_on_enter.assert_called_once()
            assert configurator.state_definition["id"] == ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS.value


@pytest.mark.parametrize(
    "pump_type, data, cylinder_quantity, final_state",
    [
        pytest.param(
            "compact",
            ["air", "yes"],  # compact_pump_type / air_pump_gauge_selection
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET,
            id="compact air pump - choose gauge",
        ),
        pytest.param(
            "compact",
            ["air", "no"],  # compact_pump_type / air_pump_gauge_selection
            2,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="compact air pump - no gauge selection",
        ),
        pytest.param(
            "compact",
            ["electric", "gaugeandneedle"],  # compact_pump_type / electric_pump_gauge_selection
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET,
            id="compact electric pump - choose gauge and needle",
        ),
        pytest.param(
            "compact",
            ["electric", "gaugeonly"],  # compact_pump_type / electric_pump_gauge_selection
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET,
            id="compact electric pump - choose gauge only",
        ),
        pytest.param(
            "compact",
            ["electric", "nogauge"],  # compact_pump_type / electric_pump_gauge_selection
            2,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="compact electric pump - no gauge selection",
        ),
        pytest.param(
            "compact",
            ["electric", "nogauge"],  # compact_pump_type / electric_pump_gauge_selection
            1,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="compact electric pump - no gauge selection",
        ),
        pytest.param(
            "compact",
            ["air", "no"],  # compact_pump_type / air_pump_gauge_selection
            1,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="compact air pump - no gauge selection - ONE cylinder",
        ),
        pytest.param(
            "hand",
            ["yes", None],  # hand_pump_gauge / hand_pump_valve
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET,
            id="hand pump single acting - choose gauge",
        ),
        pytest.param(
            "hand",
            ["no", None],  # hand_pump_gauge / hand_pump_valve
            2,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="hand pump single acting - no gauge selection",
        ),
        pytest.param(
            "hand",
            ["yes", "no"],  # hand_pump_gauge_selection / hand_pump_valve
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET,
            id="hand pump double acting - gauge selection / no valve selection",
        ),
        pytest.param(
            "hand",
            ["yes", "yes"],  # hand_pump_gauge_selection / hand_pump_valve
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE,
            id="hand pump double acting - gauge and valve selection",
        ),
        pytest.param(
            "hand",
            ["no", "no"],  # hand_pump_gauge_selection / hand_pump_valve
            2,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="hand pump double acting - no gauge/valve selection",
        ),
        pytest.param(
            "hand",
            ["no", "no"],  # hand_pump_gauge_selection / hand_pump_valve
            1,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="hand pump double acting - no gauge/valve selection - ONE cylinder",
        ),
        pytest.param(
            "hand",
            ["no", None],  # hand_pump_gauge_selection / hand_pump_valve
            1,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="hand pump single acting - no gauge selection - ONE cylinder",
        ),
        pytest.param("vari", None, None, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE, id="vari pump"),
        pytest.param(
            None,
            None,
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="pump unknown",
        ),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsValve.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsGaugeset.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsFlowPanelProductList.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_pump.get_or_query_cylinder_quantity"
)
@patch("holmatro_customer_portal.services.configurator.utils.get_configuration_data_value")
@patch("holmatro_customer_portal.services.configurator.transitions.industrial_lifting_pump.get_or_query_pump_type")
def test_pump_product_list_next(
    mock_transition_get_pump_type: MagicMock,
    mock_transition_get_data: MagicMock,
    mock_get_cylinder_quantity: MagicMock,
    mock_flowpanel_on_enter: MagicMock,
    mock_gaugeset_on_enter: MagicMock,
    mock_valve_on_enter: MagicMock,
    pump_type: str,
    data: list,
    cylinder_quantity: int,
    final_state: ConfiguratorState,
):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST, configuration, mock_jwt_token
    )

    # data returns compact_pump_type and compact_pump_gauge_selection or hand_pump_gauge_selection and hand_pump_valve_selection
    mock_transition_get_data.side_effect = data
    mock_transition_get_pump_type.return_value = pump_type
    mock_get_cylinder_quantity.return_value = cylinder_quantity

    configurator.next()
    assert configurator.state == final_state


@pytest.mark.parametrize(
    "cylinder_quantity, data, acting_type, current_state, next_state",
    (
        pytest.param(
            {"products": [], "total_quantity": 2},
            ["pulling"],
            "single",
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS,
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING,
            id="pulling cylinder - 2x cylinder",
        ),
        pytest.param(
            {"products": [], "total_quantity": 1},
            ["pulling", "pulling"],
            "single",
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS,
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING,
            id="pulling cylinder",
        ),
        pytest.param(
            {"products": [], "total_quantity": 1},
            ["flat", None, "flat", None],
            "single",
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS,
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST,
            id="lifting",
        ),
        pytest.param(
            {"products": [], "total_quantity": 1},
            ["flat", None, "lifting", None],
            "single",
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING,
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST,
            id="lifting",
        ),
        pytest.param(
            {"products": [], "total_quantity": 2},
            ["hollow plunger", None],
            "single",
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS,
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING,
            id="hollow plunger, 2x cylinder",
        ),
    ),
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_cylinder.CylinderProductList.on_enter")
@patch("holmatro_customer_portal.services.configurator.utils.get_product_quantities")
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_pump.get_configuration_cylinder_acting_type"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_pump.get_configuration_product_attribute"
)
def test_pump_settings_back_transitions(
    mock_get_cylinder_data,
    mock_get_acting_type,
    mock_get_cylinder_quantity,
    mock_cylinder_on_enter,
    cylinder_quantity: int,
    data: str,
    acting_type: str,
    current_state: ConfiguratorState,
    next_state: ConfiguratorState,
):
    session = UnifiedAlchemyMagicMock()
    mock_get_cylinder_data.side_effect = data  # cylinder_type_attribute or tonnage
    mock_get_acting_type.return_value = acting_type
    mock_get_cylinder_quantity.return_value = cylinder_quantity

    configurator = create_test_configurator(session, ConfiguratorState(current_state), configuration, mock_jwt_token)

    configurator.back()  # type: ignore

    assert configurator.state == next_state
