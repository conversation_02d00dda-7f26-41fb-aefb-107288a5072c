import copy
from unittest.mock import ANY, MagicMock, patch

import pytest
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.schemas.response_schema import ProductPreviewRes
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.workflow import ConfiguratorFactory
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import FacetsAttributes
from tests.fixtures.helpers import create_test_configurator
from tests.fixtures.my_hm_fixture import my_hm_mock_responses
from tests.fixtures.query_factory import MockQueryFactory
from tests.fixtures.test_client import mock_catalog_repository, mock_db_user, mock_jwt_token

configuration = Configuration(user=mock_db_user())


def get_attribute_list(titles):
    tweakwise_facet_attribute = {
        "title": "",
        "isselected": False,
        "nrofresults": 0,
        "attributeid": None,
        "url": None,
    }

    attributes = []
    for t in titles:
        attribute = copy.deepcopy(tweakwise_facet_attribute)
        attribute["title"] = t
        attributes.append(FacetsAttributes(**attribute))

    return attributes


@pytest.mark.parametrize(
    "data, tweakwise_filters, filters, category_path",
    [
        pytest.param(
            ["application", "lifting", "cylinder"],
            [get_attribute_list(["flat", "high tonnage", "Standard", "hollow plunger", "pulling"]), None],
            {"hie-cylinder-types-multilanguage": "flat|high tonnage|Standard"},
            None,
            id="application lifting cylinder",
        ),
        pytest.param(
            ["application", "lifting", "cylinder"],
            [None],
            None,
            10007411,
            id="application lifting cylinder - Tweakwise facet attributes response 404",
        ),
        pytest.param(
            ["application", "lifting", "wedges"],
            None,
            None,
            None,
            id="application lifting wedges",
        ),
        pytest.param(
            ["application", "pulling", "hollow_cylinder"],
            None,
            {"hie-cylinder-types-multilanguage": "hollow_cylinder"},
            10007411,
            id="application pulling",
        ),
        pytest.param(
            ["application", "pulling", None],
            None,
            None,
            10007411,
            id="application not completely filled out",
        ),
        pytest.param(
            ["calculation", 5, "symmetrical", None],
            None,
            None,
            10007411,
            id="calculation not complete",
        ),
        pytest.param(
            ["calculation", 5, "symmetrical", None],
            None,
            None,
            10007411,
            id="calculation not complete - Tweakwise facet attributes response 404",
        ),
        pytest.param(
            ["calculation", "5.1", "symmetrical", "2"],
            None,
            None,
            10007411,
            id="calculation with strings",
        ),
        pytest.param(
            ["calculation", 5, "symmetrical", 2],
            [
                get_attribute_list(["1", "5", "8", "50", "150"]),
                get_attribute_list(["flat", "high tonnage", "Standard", "hollow plunger", "pulling"]),
            ],
            {"tonnage-t": "4-150", "hie-cylinder-types-multilanguage": "flat|high tonnage|Standard"},
            10007411,
            id="calculation symmetrical",
        ),
        pytest.param(
            ["calculation", 5, "asymmetrical", 2],
            [None, get_attribute_list(["flat", "high tonnage", "Standard", "hollow plunger", "pulling"])],
            {"tonnage-t": "5-300", "hie-cylinder-types-multilanguage": "flat|high tonnage|Standard"},
            10007411,
            id="calculation asymmetrical",
        ),
        pytest.param(
            ["existing"],
            None,
            None,
            None,
            id="no settings",
        ),
    ],
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_cylinder.get_configuration_data_value")
def test_on_enter(mock_get_data, catalog_repo_mock: MagicMock, data, tweakwise_filters, filters, category_path):
    session = UnifiedAlchemyMagicMock()

    mock_get_data.side_effect = data
    catalog_repo_mock.get_category_path.return_value = category_path
    catalog_repo_mock.get_facet_attributes.side_effect = tweakwise_filters
    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS,
        configuration,
        mock_jwt_token,
        catalog_repo_mock,
    )
    configurator.next()

    for c in configurator.state_definition["content"]:
        if c["type"] == "filters":
            assert c["meta"]["filters"] == filters
            assert c["meta"]["category"] == category_path

        if c["type"] == "products":
            assert c["meta"]["initial"] == (data[3] if (data[0] == "calculation" and data[3]) else 1)


@pytest.mark.parametrize(
    "acting_type, cylinder_type, state, cylinder_quantity",
    (
        pytest.param(["single"], "lifting", ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS, 1, id="single"),
        pytest.param(["double"], "lifting", ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS, 1, id="double"),
        pytest.param(
            [None, None], "lifting", ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING, None, id="unknown"
        ),
        pytest.param(
            [None, "hydraulic"],
            "lifting",
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS,
            1,
            id="unknown acting type - known return type",
        ),
        pytest.param(
            ["single"],
            "lifting",
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING,
            2,
            id="more than ONE cylinder",
        ),
        pytest.param(
            ["single"], "pulling", ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_CLEVIS_EYES, 1, id="pulling"
        ),
    ),
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_cylinder.CylinderPullingClevisEyes.on_enter"
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.PumpSettings.on_enter")
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.PumpSettingsActing.on_enter")
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_cylinder.get_or_query_cylinder_quantity"
)
@patch("holmatro_customer_portal.services.configurator.utils.get_configuration_product_attribute")
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_cylinder.get_configuration_product_attribute"
)
def test_cylinder_list_next(
    mock_get_cylinder_type_attribute,
    mock_get_acting_type,
    mock_get_cylinder_quantity,
    mock_pump_acting,
    mock_pump_settings,
    mock_pulling_eye_on_enter,
    acting_type,
    cylinder_type,
    state,
    cylinder_quantity,
):
    session = UnifiedAlchemyMagicMock()
    mock_get_acting_type.side_effect = acting_type
    mock_get_cylinder_quantity.return_value = cylinder_quantity
    mock_get_cylinder_type_attribute.return_value = cylinder_type

    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST, configuration, mock_jwt_token
    )
    configurator.next()

    assert configurator.state == state


@pytest.mark.parametrize(
    "transition, current_state, next_state, cylinder_quantity",
    (
        pytest.param(
            "next",
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING,
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS,
            1,
            id="pulling protection spring - next",
        ),
        pytest.param(
            "next",
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING,
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING,
            2,
            id="pulling protection spring, 2x cylinder - next",
        ),
        pytest.param(
            "back",
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING,
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_CLEVIS_EYES,
            2,
            id="pulling protection spring - back",
        ),
        pytest.param(
            "skip",
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING,
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING,
            2,
            id="pulling protection spring, 2x cylinder - skip",
        ),
        pytest.param(
            "skip",
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING,
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS,
            1,
            id="pulling protection spring - skip",
        ),
        pytest.param(
            "next",
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_CLEVIS_EYES,
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING,
            1,
            id="pulling clevis eyes - next",
        ),
        pytest.param(
            "back",
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_CLEVIS_EYES,
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST,
            1,
            id="pulling clevis eyes - back",
        ),
        pytest.param(
            "skip",
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_CLEVIS_EYES,
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING,
            1,
            id="pulling clevis eyes - skip",
        ),
    ),
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_cylinder.CylinderPullingClevisEyes.on_enter"
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_cylinder.CylinderProductList.on_enter")
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.PumpSettings.on_enter")
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.PumpSettingsActing.on_enter")
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_cylinder.get_or_query_cylinder_quantity"
)
def test_cylinder_pulling_transitions(
    mock_get_cylinder_quantity,
    mock_pump_acting,
    mock_pump_settings,
    mock_cylinder_productlist,
    mock_pulling_eye_on_enter,
    transition,
    current_state,
    next_state,
    cylinder_quantity,
):
    session = UnifiedAlchemyMagicMock()
    mock_get_cylinder_quantity.return_value = cylinder_quantity

    configurator = create_test_configurator(session, current_state, configuration, mock_jwt_token)
    if transition == "next":
        configurator.next()  # type: ignore
    elif transition == "skip":
        configurator.skip()  # type: ignore
    else:
        configurator.back()  # type: ignore

    assert configurator.state == next_state


@pytest.mark.parametrize(
    "data, tonnage, product_article_numbers",
    [
        pytest.param("pulling", "11", ["100.181.051", "100.181.056"], id="cylinder 11T"),
        pytest.param("pulling", "30", ["100.181.051", "100.181.057"], id="cylinder 30T"),
        pytest.param("pulling", "60", "100.181.052", id="cylinder 60T"),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_cylinder.get_configuration_product_attribute"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_cylinder.get_product_preview_by_article_number"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_cylinder.get_or_query_cylinder_quantity"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_cylinder.get_configuration_product_attribute"
)
def test_pulling_clevis_eye_on_enter(
    mock_get_cylinder_data,
    mock_get_cylinder_quantity,
    mock_get_product_previews,
    mock_get_cylinder_tonnage_on_enter,
    my_hm_mock_responses,
    data: str,
    tonnage: str,
    product_article_numbers: list[str] | str,
):
    session = UnifiedAlchemyMagicMock()
    mock_get_cylinder_data.return_value = data  # cylinder_type_attribute or tonnage
    mock_get_cylinder_tonnage_on_enter.return_value = tonnage
    mock_get_cylinder_quantity.return_value = 1
    mock_get_product_previews.return_value = [MockQueryFactory().get_mock_product_preview()]

    mock_my_hm_client = MyHolmatroPortalApiClient("jwt_token")
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST, configuration, mock_my_hm_client
    )

    configurator.next()  # type: ignore

    for context in configurator.state_definition["content"]:
        if context["type"] == "products":
            assert context["meta"]["initial"] == 2
            assert len(context["meta"]["products"]) == 1
            assert isinstance(context["meta"]["products"][0], ProductPreviewRes)

    mock_get_product_previews.assert_called_once_with(product_article_numbers, ANY, session)


@pytest.mark.parametrize(
    "data, tonnage, product_article_number",
    [
        pytest.param("pulling", "11", "100.581.160", id="cylinder 11T"),
        pytest.param("pulling", "30", "100.581.160", id="cylinder 30T"),
        pytest.param("pulling", "60", "100.581.161", id="cylinder 60T"),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_cylinder.get_configuration_product_attribute"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_cylinder.get_product_preview_by_article_number"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_cylinder.get_or_query_cylinder_quantity"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_cylinder.get_configuration_product_attribute"
)
def test_pulling_protection_spring_on_enter(
    mock_get_cylinder_data,
    mock_get_cylinder_quantity,
    mock_get_product_previews,
    mock_get_cylinder_tonnage_on_enter,
    my_hm_mock_responses,
    data: str,
    tonnage: str,
    product_article_number: str,
):
    session = UnifiedAlchemyMagicMock()
    mock_get_cylinder_data.return_value = data  # cylinder_type_attribute or tonnage
    mock_get_cylinder_tonnage_on_enter.return_value = tonnage
    mock_get_cylinder_quantity.return_value = 3
    mock_get_product_previews.return_value = [MockQueryFactory().get_mock_product_preview()]

    mock_my_hm_client = MyHolmatroPortalApiClient("jwt_token")
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_CLEVIS_EYES, configuration, mock_my_hm_client
    )

    configurator.next()  # type: ignore

    for context in configurator.state_definition["content"]:
        if context["type"] == "products":
            assert context["meta"]["initial"] == 3
            assert len(context["meta"]["products"]) == 1
            assert isinstance(context["meta"]["products"][0], ProductPreviewRes)

    mock_get_product_previews.assert_called_once_with(product_article_number, ANY, session)
