from unittest.mock import MagicMock, patch

from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Configuration, Product
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.workflow import ConfiguratorFactory
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient
from tests.fixtures.database_models_fixture import mocked_user
from tests.fixtures.helpers import create_test_configurator
from tests.fixtures.my_hm_fixture import my_hm_mock_responses
from tests.fixtures.test_client import mock_db_user, mock_jwt_token

configuration = Configuration(user=mock_db_user())


@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsProtectionFrame.on_enter"
)
def test_back_transitions(_):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_COVER, configuration, mock_jwt_token
    )

    configurator.back()
    assert configurator.state == ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME


@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListPumpToPanel.on_enter"
)
def test_next_transitions(_):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_COVER, configuration, mock_jwt_token
    )

    configurator.next()
    assert configurator.state == ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL


@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListPumpToPanel.on_enter"
)
def test_skip_transitions(_):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_COVER, configuration, mock_jwt_token
    )

    configurator.skip()
    assert configurator.state == ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL


@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_protection_frame.get_or_query_products",
    return_value=Product(),
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.get_product_preview_by_article_number",
    return_value=[],
)
def test_on_enter(mock_get_products: MagicMock, _, my_hm_mock_responses):
    session = UnifiedAlchemyMagicMock()

    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME,
        configuration,
        MyHolmatroPortalApiClient("jwt_token"),
    )
    configurator.next()

    mock_get_products.assert_called_once_with("100.151.040", mocked_user, session)
