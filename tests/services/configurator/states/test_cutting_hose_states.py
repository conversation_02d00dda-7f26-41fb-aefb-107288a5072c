from unittest.mock import ANY, MagicMock, patch

import pytest
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.services.configurator.configurator_enums import (
    CutterApplication,
    CutterRequirements,
    CutterType,
    FilterName,
)
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.utils.enums import SyncforceCategory
from tests.fixtures.helpers import create_test_configurator
from tests.fixtures.test_client import mock_catalog_repository, mock_db_user, mock_jwt_token

configuration = Configuration(user=mock_db_user())
patch_root = "holmatro_customer_portal.services.configurator.states.industrial_cutting_hose"


@pytest.mark.parametrize(
    "application, user_requirements, filters, cutter_quantity",
    [
        pytest.param(None, None, {}, 1, id="No application selected"),
        pytest.param(
            CutterApplication.FRIDGE_RECYCLING.value,
            None,
            {},
            1,
            id="Fridge recycling - requirements neither not selected",
        ),
        pytest.param(
            CutterApplication.FRIDGE_RECYCLING.value,
            CutterRequirements.FRIDGE_RECYCLING_TWIST_GRIP.value,
            {FilterName.CUTTER_TYPE.value: CutterType.MOBILE.value},
            1,
            id="Fridge recycling - twist-grip",
        ),
        pytest.param(
            CutterApplication.FRIDGE_RECYCLING.value,
            CutterRequirements.FRIDGE_RECYCLING_STATIONARY_ONE_HAND.value,
            {FilterName.CUTTER_TYPE.value: CutterType.STATIONARY.value},
            1,
            id="Fridge recycling - one-hand",
        ),
        pytest.param(
            CutterApplication.FRIDGE_RECYCLING.value,
            CutterRequirements.FRIDGE_RECYCLING_STATIONARY_TWO_HANDS.value,
            {FilterName.CUTTER_TYPE.value: CutterType.STATIONARY.value},
            2,
            id="Fridge recycling - two-hands",
        ),
        pytest.param(
            CutterApplication.CABLE_CUTTING.value,
            CutterType.MOBILE.value,
            {FilterName.CUTTER_TYPE.value: CutterType.MOBILE.value},
            2,
            id="Cable cutting - mobile",
        ),
        pytest.param(
            CutterApplication.CABLE_CUTTING.value,
            CutterType.STATIONARY.value,
            {FilterName.CUTTER_TYPE.value: CutterType.STATIONARY.value},
            2,
            id="Cable cutting - stationary",
        ),
        pytest.param(
            CutterApplication.CABLE_CUTTING.value,
            None,
            {},
            2,
            id="Cable cutting - no way of cutting selected",
        ),
        pytest.param(
            CutterApplication.CAR_RECYCLING.value,
            CutterType.MOBILE.value,
            {FilterName.CUTTER_TYPE.value: CutterType.MOBILE.value},
            3,
            id="Car recycling - mobile",
        ),
        pytest.param(
            CutterApplication.METAL_RECYCLING.value,
            CutterType.MOBILE.value,
            {FilterName.CUTTER_TYPE.value: CutterType.MOBILE.value},
            3,
            id="Metal recycling - mobile",
        ),
        pytest.param(
            CutterApplication.METAL_RECYCLING.value,
            CutterType.STATIONARY.value,
            {FilterName.CUTTER_TYPE.value: CutterType.STATIONARY.value},
            3,
            id="Metal recycling - stationary",
        ),
    ],
)
@patch(f"{patch_root}.get_product_quantities")
@patch(f"{patch_root}.get_configuration_data_value")
def test_cutting_hose_product_list_on_enter(
    mock_get_data: MagicMock,
    mock_get_cutter_quantity: MagicMock,
    catalog_repo_mock: MagicMock,
    application: str | None,
    user_requirements: str | None,  # It can be fridge recycling user requirements or way of cutting
    filters: dict,
    cutter_quantity: int | None,
):
    session = UnifiedAlchemyMagicMock()
    category_path = "20009050"
    catalog_repo_mock.get_category_path.return_value = category_path
    mock_get_cutter_quantity.return_value = {"total_quantity": cutter_quantity}

    mock_get_data.side_effect = [application, user_requirements]

    # Make transition to Hose Product List
    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST,
        configuration,
        mock_jwt_token,
        catalog_repo_mock,
    )
    configurator.next()

    assert len(configurator.state_definition["content"]) == 2
    mock_get_cutter_quantity.assert_called_once_with(
        session, configuration, ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST
    )
    catalog_repo_mock.get_category_path.assert_called_once_with(
        ANY, SyncforceCategory.CUTTER_HOSES, SyncforceCategory.INDUSTRIAL_CUTTING
    )

    for content in configurator.state_definition["content"]:
        if content["type"] == "filters":
            assert content["meta"]["filters"] == filters
            assert content["meta"]["category"] == category_path
            break

    for c in configurator.state_definition["content"]:
        if c["type"] == "products":
            assert c["meta"]["products"] == []
            assert c["meta"]["initial"] == cutter_quantity or 1
            break
