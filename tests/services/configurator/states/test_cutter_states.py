from unittest.mock import ANY, MagicMock, patch

import pytest
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.services.configurator.configurator_enums import (
    CutterApplication,
    CutterFilterValues,
    CutterRequirements,
    CutterType,
    CuttingFrequencyFilterValues,
    FilterName,
)
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.workflow import ConfiguratorFactory
from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.tweakwise.tweakwise_client import TweakwiseClient
from tests.fixtures.helpers import create_test_configurator
from tests.fixtures.test_client import mock_catalog_repository, mock_db_user, mock_jwt_token

configuration = Configuration(user=mock_db_user())


@pytest.mark.parametrize(
    "data, cutters",
    [
        pytest.param(
            [CutterApplication.CABLE_CUTTING.value, "low", "small"],
            [CutterType.MOBILE.value, CutterType.STATIONARY.value, CutterType.BATTERY.value],
            id="All options available",
        ),
        pytest.param(
            [CutterApplication.CABLE_CUTTING.value, "low", "large"],
            [CutterType.MOBILE.value, CutterType.BATTERY.value],
            id="Low frequency, large cable",
        ),
        pytest.param(
            [CutterApplication.CABLE_CUTTING.value, "medium", "medium"],
            [CutterType.MOBILE.value, CutterType.STATIONARY.value],
            id="Medium frequency, medium cable",
        ),
        pytest.param(
            [CutterApplication.CABLE_CUTTING.value, "medium", "large"],
            [CutterType.MOBILE.value],
            id="Medium frequency, large cable",
        ),
        pytest.param(
            [CutterApplication.CABLE_CUTTING.value, "high", "small"],
            [CutterType.STATIONARY.value],
            id="High frequency, small cable",
        ),
        pytest.param(
            [CutterApplication.CABLE_CUTTING.value, "high", "large"],
            None,
            id="High frequency, large cable",
        ),
        pytest.param(
            [CutterApplication.CAR_RECYCLING.value, "low", None],
            [CutterType.MOBILE.value, CutterType.BATTERY.value],
            id="Car, low frequency",
        ),
        pytest.param(
            [CutterApplication.CAR_RECYCLING.value, "medium", None],
            [CutterType.MOBILE.value],
            id="Car, high frequency",
        ),
        pytest.param(
            [CutterApplication.METAL_RECYCLING.value, "low", None],
            [CutterType.MOBILE.value, CutterType.STATIONARY.value, CutterType.BATTERY.value],
            id="Metal, low frequency",
        ),
        pytest.param(
            [CutterApplication.METAL_RECYCLING.value, "medium", None],
            [CutterType.MOBILE.value, CutterType.STATIONARY.value],
            id="Metal, medium frequency",
        ),
        pytest.param(
            [CutterApplication.METAL_RECYCLING.value, "high", None],
            [CutterType.STATIONARY.value],
            id="Metal, high frequency",
        ),
    ],
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_cutting_cutter.get_configuration_data_value")
def test_cutter_way_of_cutting_on_enter(mock_get_data, data, cutters):
    session = UnifiedAlchemyMagicMock()

    mock_get_data.side_effect = data
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_SETTINGS, configuration, mock_jwt_token
    )
    configurator.next()

    assert len(configurator.state_definition["content"]) == 1

    if cutters:
        assert configurator.state_definition["content"][0]["type"] == "choice"
        assert len(configurator.state_definition["content"][0]["meta"]["choices"]) == len(cutters)
        for c in configurator.state_definition["content"][0]["meta"]["choices"]:
            assert c["value"] in cutters


@pytest.mark.parametrize(
    "application, cutting_frequency, way_of_cutting, user_requirements, filter_values",
    [
        pytest.param(None, None, None, [], {}, id="No choice made"),
        pytest.param(
            CutterApplication.CABLE_CUTTING.value,
            "low",
            CutterType.MOBILE.value,
            ["medium"],
            {
                FilterName.CUTTER_APPLICATION.value: CutterFilterValues.CABLE_CUTTING.value,
                FilterName.CUTTING_FREQUENCY.value: f"{CuttingFrequencyFilterValues.LOW.value}-{CuttingFrequencyFilterValues.HIGH.value}",
                FilterName.CUTTER_DETAILS.value: CutterFilterValues.MOBILE.value,
                FilterName.CABLE_CUTTING_POWER_CABLE_DIAMETER.value: "110-160",
            },
            id="Cable cutting - mobile",
        ),
        pytest.param(
            CutterApplication.CABLE_CUTTING.value,
            "high",
            CutterType.STATIONARY.value,
            ["large"],
            {
                FilterName.CUTTER_APPLICATION.value: CutterFilterValues.CABLE_CUTTING.value,
                FilterName.CUTTING_FREQUENCY.value: f"{CuttingFrequencyFilterValues.HIGH.value}-{CuttingFrequencyFilterValues.HIGH.value}",
                FilterName.CUTTER_DETAILS.value: CutterFilterValues.STATIONARY.value,
                FilterName.CABLE_CUTTING_POWER_CABLE_DIAMETER.value: "160-160",
            },
            id="Cable cutting - stationary",
        ),
        pytest.param(
            CutterApplication.CABLE_CUTTING.value,
            "low",
            CutterType.BATTERY.value,
            ["small"],
            {
                FilterName.CUTTER_APPLICATION.value: CutterFilterValues.CABLE_CUTTING.value,
                FilterName.CUTTING_FREQUENCY.value: f"{CuttingFrequencyFilterValues.LOW.value}-{CuttingFrequencyFilterValues.HIGH.value}",
                FilterName.CUTTER_DETAILS.value: CutterFilterValues.BATTERY.value,
                FilterName.CABLE_CUTTING_POWER_CABLE_DIAMETER.value: "70-160",
            },
            id="Cable cutting - battery",
        ),
        pytest.param(
            CutterApplication.CAR_RECYCLING.value,
            "medium",
            CutterType.MOBILE.value,
            ["low"],
            {
                FilterName.CUTTER_APPLICATION.value: CutterFilterValues.CAR_RECYCLING.value,
                FilterName.CUTTING_FREQUENCY.value: f"{CuttingFrequencyFilterValues.MEDIUM.value}-{CuttingFrequencyFilterValues.HIGH.value}",
                FilterName.CUTTER_DETAILS.value: CutterFilterValues.MOBILE.value,
                FilterName.CAR_RECYCLING_CATALYTIC_CONVERTER.value: "80-99",
            },
            id="Car recycling - mobile",
        ),
        pytest.param(
            CutterApplication.CAR_RECYCLING.value,
            "low",
            CutterType.BATTERY.value,
            ["high"],
            {
                FilterName.CUTTER_APPLICATION.value: CutterFilterValues.CAR_RECYCLING.value,
                FilterName.CUTTING_FREQUENCY.value: f"{CuttingFrequencyFilterValues.LOW.value}-{CuttingFrequencyFilterValues.HIGH.value}",
                FilterName.CUTTER_DETAILS.value: CutterFilterValues.BATTERY.value,
                FilterName.CAR_RECYCLING_CATALYTIC_CONVERTER.value: "99-99",
            },
            id="Car recycling - battery",
        ),
        pytest.param(
            CutterApplication.FRIDGE_RECYCLING.value,
            None,
            None,
            [CutterRequirements.FRIDGE_RECYCLING_TWIST_GRIP.value],
            {
                FilterName.CUTTER_APPLICATION.value: CutterFilterValues.FRIDGE_RECYCLING.value,
                FilterName.CUTTER_DETAILS.value: CutterFilterValues.MOBILE.value,
            },
            id="Fridge recycling - twist grip",
        ),
        pytest.param(
            CutterApplication.FRIDGE_RECYCLING.value,
            None,
            None,
            [CutterRequirements.FRIDGE_RECYCLING_STATIONARY_ONE_HAND.value],
            {
                FilterName.CUTTER_APPLICATION.value: CutterFilterValues.FRIDGE_RECYCLING.value,
                FilterName.CUTTER_DETAILS.value: CutterFilterValues.STATIONARY.value,
            },
            id="Fridge recycling - one hand",
        ),
        pytest.param(
            CutterApplication.FRIDGE_RECYCLING.value,
            None,
            None,
            [CutterRequirements.FRIDGE_RECYCLING_STATIONARY_TWO_HANDS.value],
            {
                FilterName.CUTTER_APPLICATION.value: CutterFilterValues.FRIDGE_RECYCLING.value,
                FilterName.CUTTER_DETAILS.value: CutterFilterValues.STATIONARY_TWO_HANDS.value,
            },
            id="Fridge recycling - two hands",
        ),
        pytest.param(
            CutterApplication.METAL_RECYCLING.value,
            "medium",
            CutterType.MOBILE.value,
            [CutterRequirements.METAL_RECYCLING_ROUND_BAR.value, 10],
            {
                FilterName.CUTTER_APPLICATION.value: CutterFilterValues.METAL_RECYCLING.value,
                FilterName.CUTTING_FREQUENCY.value: f"{CuttingFrequencyFilterValues.MEDIUM.value}-{CuttingFrequencyFilterValues.HIGH.value}",
                FilterName.CUTTER_DETAILS.value: CutterFilterValues.MOBILE.value,
                FilterName.METAL_RECYCLING_ROUND_BAR_DIAMETER.value: "10-24",
            },
            id="Metal recycling - mobile",
        ),
        pytest.param(
            CutterApplication.METAL_RECYCLING.value,
            "high",
            CutterType.STATIONARY.value,
            [CutterRequirements.METAL_RECYCLING_THIN_PIPE.value, 87],
            {
                FilterName.CUTTER_APPLICATION.value: CutterFilterValues.METAL_RECYCLING.value,
                FilterName.CUTTING_FREQUENCY.value: f"{CuttingFrequencyFilterValues.HIGH.value}-{CuttingFrequencyFilterValues.HIGH.value}",
                FilterName.CUTTER_DETAILS.value: CutterFilterValues.STATIONARY.value,
                FilterName.METAL_RECYCLING_THIN_PIPE_DIAMETER.value: "87-100",
            },
            id="Metal recycling - stationary",
        ),
        pytest.param(
            CutterApplication.METAL_RECYCLING.value,
            "low",
            CutterType.BATTERY.value,
            [CutterRequirements.METAL_RECYCLING_FLAT_BAR.value, 70],
            {
                FilterName.CUTTER_APPLICATION.value: CutterFilterValues.METAL_RECYCLING.value,
                FilterName.CUTTING_FREQUENCY.value: f"{CuttingFrequencyFilterValues.LOW.value}-{CuttingFrequencyFilterValues.HIGH.value}",
                FilterName.CUTTER_DETAILS.value: CutterFilterValues.BATTERY.value,
                FilterName.METAL_RECYCLING_FLAT_BAR_THICKNESS.value: "70-100",
            },
            id="Metal recycling - battery - flat bar",
        ),
    ],
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_cutting_cutter.get_configuration_data_value")
def test_cutter_product_list_on_enter(
    mock_get_data: MagicMock,
    catalog_repo_mock: MagicMock,
    application: str | None,
    user_requirements: list,
    cutting_frequency: str,
    way_of_cutting: str,
    filter_values: dict,
):
    session = UnifiedAlchemyMagicMock()
    category_path = "20006921"

    catalog_repo_mock.get_category_path.return_value = category_path

    if application == CutterApplication.FRIDGE_RECYCLING.value:
        mock_get_data.side_effect = [application] + user_requirements
    else:
        mock_get_data.side_effect = [application, cutting_frequency, way_of_cutting] + user_requirements

    # Make transition to Cutter Product List
    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_WAY_OF_CUTTING,
        configuration,
        mock_jwt_token,
        catalog_repo_mock,
    )
    configurator.next()

    assert len(configurator.state_definition["content"]) == 2
    catalog_repo_mock.get_category_path.assert_called_once_with(
        ANY, SyncforceCategory.CUTTERS, SyncforceCategory.INDUSTRIAL_CUTTING
    )

    for content in configurator.state_definition["content"]:
        if content["type"] == "filters":
            assert content["meta"]["filters"] == filter_values
            assert content["meta"]["category"] == category_path
