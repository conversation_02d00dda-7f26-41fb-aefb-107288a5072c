from unittest.mock import MagicMock, call, patch

import pytest
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Configuration, Product
from holmatro_customer_portal.schemas.configurator import ConfiguratorProduct, ConfiguratorStateProducts
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.states.system_components_helpers.gaugeset import (
    get_product_gaugeset,
)
from holmatro_customer_portal.services.configurator.workflow import ConfiguratorFactory
from tests.fixtures.database_models_fixture import mocked_user
from tests.fixtures.helpers import create_test_configurator
from tests.fixtures.test_client import mock_db_user, mock_jwt_token

configuration = Configuration(user=mock_db_user())


@pytest.mark.parametrize(
    "pump_type, cylinder_acting_type, capacity_oil_tank, compact_gauge, call",
    [
        pytest.param(
            "hand",
            "single",
            900,
            None,
            "100.182.213",
            id="hand pump single acting type - model PA04 and PA 09",
        ),
        pytest.param(
            "hand",
            "single",
            2800,
            None,
            "100.182.214",
            id="hand pump single acting type - model PA18/PA28/PA38",
        ),
        pytest.param(
            "hand",
            "single",
            None,
            None,
            ["100.182.213", "100.182.214"],
            id="hand pump single acting type",
        ),
        pytest.param(
            "hand",
            "single",
            "900",
            None,
            "100.182.213",
            id="hand pump single acting type - capacity oil tank string",
        ),
        pytest.param(
            "hand",
            "single",
            "test",
            None,
            ["100.182.213", "100.182.214"],
            id="hand pump single acting type - capacity oil tank non integer string",
        ),
        pytest.param(
            "hand",
            "double",
            None,
            None,
            "100.182.216",
            id="hand pump double acting type",
        ),
        pytest.param(
            "hand",
            None,
            None,
            None,
            ["100.182.213", "100.182.214", "100.182.216"],
            id="hand pump unknown acting type",
        ),
        pytest.param(
            "compact",
            None,
            None,
            ["air"],
            "100.182.215",
            id="compact pump",
        ),
        pytest.param(
            "compact",
            None,
            None,
            ["electric", "gaugeandneedle"],
            "100.182.162",
            id="compact pump electric - gauge and needle",
        ),
        pytest.param(
            "compact",
            None,
            None,
            ["electric", "gaugeonly"],
            "100.182.158",
            id="compact pump electric - only gauge",
        ),
        pytest.param(
            "vari",
            "single",
            None,
            None,
            "100.582.500",
            id="vari pump",
        ),
        pytest.param(
            None,
            None,
            None,
            None,
            [
                "100.182.213",
                "100.182.214",
                "100.182.216",
                "100.182.215",
                "100.182.158",
                "100.182.162",
                "100.582.500",
            ],
            id="unknown pump type",
        ),
    ],
)
@patch("holmatro_customer_portal.services.configurator.states.system_components_helpers.gaugeset.get_or_query_value")
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.gaugeset.get_configuration_product_attribute"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.gaugeset.ProductPreviewMapper.product_preview_mapper"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.gaugeset.get_product_preview_by_article_number"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.gaugeset.get_or_query_cylinder_acting_type"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.gaugeset.get_configuration_pump_type"
)
def test_on_enter(
    mock_get_pump_type: MagicMock,
    mock_get_cylinder_acting_type: MagicMock,
    mock_get_products: MagicMock,
    mock_product_preview_mapper: MagicMock,
    mock_get_capacity_oil_tank: MagicMock,
    mock_get_compact_gauge: MagicMock,
    pump_type: str,
    cylinder_acting_type: str,
    capacity_oil_tank: int,
    compact_gauge: list,
    call,
):
    session = UnifiedAlchemyMagicMock()
    mock_get_pump_type.return_value = pump_type
    mock_get_cylinder_acting_type.return_value = cylinder_acting_type
    mock_get_capacity_oil_tank.return_value = capacity_oil_tank
    mock_get_compact_gauge.side_effect = compact_gauge
    mock_product_preview_mapper.return_value = []

    get_product_gaugeset(session, configuration, mock_jwt_token, {})

    mock_get_products.assert_called_once_with(call, mocked_user, session)


@pytest.mark.parametrize(
    "cylinder_quantity, pump_type, final_state",
    [
        pytest.param(
            None,
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="unknown type",
        ),
        pytest.param(
            {"products": [], "total_quantity": 1},
            "hand",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="hand pump ONE cylinder",
        ),
        pytest.param(
            {"products": [], "total_quantity": 1},
            "hand",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="hand pump double acting type ONE cylinder",
        ),
        pytest.param(
            {"products": [], "total_quantity": 3},
            "hand",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="hand pump double acting type more than ONE cylinder",
        ),
        pytest.param(
            {"products": [], "total_quantity": 3},
            "hand",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="hand pump single acting type more than ONE cylinder",
        ),
        pytest.param(
            {"products": [], "total_quantity": 1},
            "compact",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="compact pump ONE cylinder",
        ),
        pytest.param(
            {"products": [], "total_quantity": 4},
            "compact",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="compact pump more than ONE cylinder",
        ),
        pytest.param(
            {"products": [], "total_quantity": 4},
            "vari",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="vari pump more than ONE cylinder",
        ),
        pytest.param(
            {"products": [], "total_quantity": 1},
            "vari",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="vari pump ONE cylinder",
        ),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsFlowPanelProductList.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_gaugeset.get_configuration_pump_type"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_gaugeset.get_product_quantities"
)
def test_gaugeset_next_transition(
    mock_get_product_quantities: MagicMock,
    mock_get_pump_type: MagicMock,
    mock_flowpanel_on_enter: MagicMock,
    cylinder_quantity: int | None,
    pump_type: str | None,
    final_state: ConfiguratorState,
):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET, configuration, mock_jwt_token
    )

    mock_get_product_quantities.return_value = cylinder_quantity
    mock_get_pump_type.return_value = pump_type

    configurator.next()  # type: ignore
    assert configurator.state == final_state


@pytest.mark.parametrize(
    "cylinder_quantity, pump_type, final_state",
    [
        pytest.param(
            None,
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="unknown type",
        ),
        pytest.param(
            {"products": [], "total_quantity": 1},
            "hand",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="hand pump ONE cylinder",
        ),
        pytest.param(
            {"products": [], "total_quantity": 1},
            "hand",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="hand pump double acting type ONE cylinder",
        ),
        pytest.param(
            {"products": [], "total_quantity": 3},
            "hand",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="hand pump double acting type more than ONE cylinder",
        ),
        pytest.param(
            {"products": [], "total_quantity": 3},
            "hand",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="hand pump single acting type more than ONE cylinder",
        ),
        pytest.param(
            {"products": [], "total_quantity": 1},
            "compact",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="compact pump ONE cylinder",
        ),
        pytest.param(
            {"products": [], "total_quantity": 4},
            "compact",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="compact pump more than ONE cylinder",
        ),
        pytest.param(
            {"products": [], "total_quantity": 4},
            "vari",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="vari pump more than ONE cylinder",
        ),
        pytest.param(
            {"products": [], "total_quantity": 1},
            "vari",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="vari pump ONE cylinder",
        ),
    ],
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_overview.Overview.on_enter")
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsFlowPanelProductList.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_gaugeset.get_product_quantities"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_gaugeset.get_configuration_pump_type"
)
def test_gaugeset_skip_transition(
    mock_get_pump_type: MagicMock,
    mock_get_product_quantity: MagicMock,
    mock_flowpanel_on_enter: MagicMock,
    mock_overview: MagicMock,
    cylinder_quantity: int | None,
    pump_type: str | None,
    final_state: ConfiguratorState,
):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET, configuration, mock_jwt_token
    )

    mock_get_product_quantity.return_value = cylinder_quantity
    mock_get_pump_type.return_value = pump_type

    configurator.skip()  # type: ignore
    assert configurator.state == final_state


@pytest.mark.parametrize(
    "valve_selection, pump_type, final_state",
    [
        pytest.param(
            None,
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST,
            id="unknown type",
        ),
        pytest.param(
            "no",
            "hand",
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST,
            id="hand pump - selected to NOT choose valve",
        ),
        pytest.param(
            "yes",
            "hand",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE,
            id="hand pump - selected to choose valve",
        ),
        pytest.param(
            None,
            "hand",
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST,
            id="hand pump double acting type more than ONE cylinder",
        ),
        pytest.param(
            None,
            "hand",
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST,
            id="hand pump single acting type more than ONE cylinder",
        ),
        pytest.param(
            None, "compact", ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST, id="compact pump ONE cylinder"
        ),
        pytest.param(
            None,
            "compact",
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST,
            id="compact pump more than ONE cylinder",
        ),
        pytest.param(
            None, "vari", ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS, id="vari pump"
        ),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsValve.on_enter"
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.PumpProductList.on_enter")
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_gaugeset.get_configuration_data_value"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_gaugeset.get_configuration_pump_type"
)
def test_gaugeset_back_transition(
    mock_get_pump_type: MagicMock,
    mock_get_valve_selection: MagicMock,
    _,
    __,
    valve_selection: str | None,
    pump_type: str | None,
    final_state: ConfiguratorState,
):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET, configuration, mock_jwt_token
    )

    mock_get_pump_type.return_value = pump_type
    mock_get_valve_selection.return_value = valve_selection

    configurator.back()  # type: ignore
    assert configurator.state == final_state


@pytest.mark.parametrize(
    "pump_type, gauge, gauge_quantity, acting_type, article_number, calls",
    [
        pytest.param(
            "vari",
            Product(),
            {"products": [], "total_quantity": 1},
            "single",
            "100.181.108",
            True,
            id="happy path single acting",
        ),
        pytest.param(
            "vari",
            Product(),
            {"products": [], "total_quantity": 1},
            "double",
            "100.182.308",
            True,
            id="happy path double acting",
        ),
        pytest.param("hand", None, None, None, None, False, id="hand pump - product not added"),
        pytest.param("vari", None, None, None, None, False, id="no gauge added"),
        pytest.param("vari", Product(), None, None, "100.182.308", True, id="no quantity found"),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsFlowPanelProductList.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.gaugeset.get_or_query_cylinder_acting_type"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.gaugeset.get_product_quantities"
)
@patch("holmatro_customer_portal.services.configurator.states.system_components_helpers.gaugeset.get_or_query_products")
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.gaugeset.get_configuration_pump_type"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.gaugeset.ConfiguratorMapper.store_configuration_product"
)
def test_on_exit(
    mock_add_products: MagicMock,
    mock_get_pump_type: MagicMock,
    mock_get_gauge: MagicMock,
    mock_get_quantity: MagicMock,
    mock_get_acting_type: MagicMock,
    mock_flow_panel_list_on_enter: MagicMock,
    pump_type: str | None,
    gauge: Product | None,
    gauge_quantity: int | None,
    acting_type: str | None,
    article_number: str | None,
    calls: bool,
):
    mock_get_pump_type.return_value = pump_type
    mock_get_gauge.return_value = gauge
    mock_get_quantity.return_value = gauge_quantity
    mock_get_acting_type.return_value = acting_type

    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET, configuration, mock_jwt_token
    )

    configurator.next()  # type: ignore

    if calls:
        mock_add_products.assert_has_calls(
            [
                call(
                    configuration,
                    ConfiguratorStateProducts(
                        state_id=ConfiguratorState.INDUSTRIAL_LIFTING_GAUGE_ADDITIONAL_PRODUCT_COVER.value,
                        products=[
                            ConfiguratorProduct(
                                product_id=None,
                                article_number="350.581.160",
                                quantity=1,
                            )
                        ],
                    ),
                ),
                call(
                    configuration,
                    ConfiguratorStateProducts(
                        state_id=ConfiguratorState.INDUSTRIAL_LIFTING_GAUGE_ADDITIONAL_PRODUCT_GAUGE.value,
                        products=[
                            ConfiguratorProduct(
                                product_id=None,
                                article_number=article_number,
                                quantity=1,
                            )
                        ],
                    ),
                ),
            ]
        )
    else:
        mock_add_products.assert_not_called()


@pytest.mark.parametrize(
    "number_of_pumps", [{"products": [], "total_quantity": 1}, {"products": [], "total_quantity": 10}, None]
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.get_product_quantities"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.get_product_gaugeset"
)
def test_gauge_initial_quantity(
    mock_get_products,
    mock_get_number_of_pumps,
    number_of_pumps,
):
    mock_get_number_of_pumps.return_value = number_of_pumps
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE, configuration, mock_jwt_token
    )

    configurator.next()

    for c in configurator.state_definition["content"]:
        if c["type"] == "products":
            assert c["meta"]["initial"] == number_of_pumps or 1
