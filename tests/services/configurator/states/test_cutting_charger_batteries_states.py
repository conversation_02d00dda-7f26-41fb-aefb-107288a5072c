from unittest.mock import AN<PERSON>, <PERSON><PERSON><PERSON>, Mock, patch

from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.services.configurator.default_products import Batteries, Chargers
from holmatro_customer_portal.services.configurator.states import ChargerBatteriesProductList
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.workflow import ConfiguratorFactory
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient
from tests.fixtures.helpers import create_test_configurator
from tests.fixtures.my_hm_fixture import my_hm_mock_responses
from tests.fixtures.test_client import mock_db_user

configuration = Configuration(user=mock_db_user())
patch_root = "holmatro_customer_portal.services.configurator.states.industrial_cutting_charger_batteries"


@patch(f"{patch_root}.get_product_quantities")
@patch(f"{patch_root}.ProductPreviewMapper")
@patch(f"{patch_root}.get_product_preview_by_article_number")
def test_cutting_charger_batteries_product_list_on_enter(
    mock_get_product_preview_by_article_number: MagicMock,
    mock_product_preview_mapper_class: Mock,
    mock_get_cutter_quantity: MagicMock,
    my_hm_mock_responses,
):
    mock_get_product_preview_by_article_number.return_value = "mocked_products"
    mock_get_cutter_quantity.return_value = {"total_quantity": 2}

    mock_product_preview_mapper = Mock()
    mock_product_preview_mapper.product_preview_mapper.return_value = "mocked_previews"
    mock_product_preview_mapper_class.return_value = mock_product_preview_mapper

    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_CUTTING_CHARGER_BATTERIES_PRODUCTLIST,
        configuration,
        MyHolmatroPortalApiClient("jwt_token"),
    )

    # Mock away set_default_product while leaving the functionality as is, so that we can assert calls to it
    configurator.set_default_product = MagicMock(side_effect=configurator.set_default_product)

    # Call the on_enter method for this state
    getattr(configurator, ChargerBatteriesProductList().on_enter_method_name)(event=Mock())

    article_numbers = [Batteries.BATTERY_28V_6AH.value, Chargers.CHARGER_230V.value, Chargers.CHARGER_110V.value]
    mock_get_product_preview_by_article_number.assert_called_once_with(article_numbers, ANY, session)
    mock_get_cutter_quantity.assert_called_once_with(
        session, configuration, ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST
    )

    mock_product_preview_mapper.product_preview_mapper.assert_called_once_with(ANY, "mocked_products")

    for c in configurator.state_definition["content"]:
        if c["type"] == "products":
            assert c["meta"]["products"] == "mocked_previews"
            assert c["meta"]["initial"] == 2
            break

    configurator.set_default_product.assert_called_once_with(Batteries.BATTERY_28V_6AH.value)
