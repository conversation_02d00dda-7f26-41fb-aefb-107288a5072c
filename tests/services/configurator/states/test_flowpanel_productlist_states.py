from unittest.mock import MagicMock, call, patch

import pytest
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Configuration, Product
from holmatro_customer_portal.schemas.configurator import ConfiguratorProduct, ConfiguratorStateProducts
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.states.system_components_helpers.flowpanel_productlist import (
    get_product_flowpanel,
)
from tests.fixtures.helpers import create_test_configurator
from tests.fixtures.test_client import mock_catalog_repository, mock_db_user, mock_jwt_token

configuration = Configuration(user=mock_db_user())


@pytest.mark.parametrize(
    "cylinder_acting_type, filter",
    [
        pytest.param(
            None,
            {"hie-acting-type-multilanguage": None},
            id="unknown acting type",
        ),
        pytest.param(
            "double",
            {"hie-acting-type-multilanguage": "double"},
            id="double acting type",
        ),
        pytest.param(
            "single",
            {"hie-acting-type-multilanguage": "single"},
            id="single acting type",
        ),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.flowpanel_productlist.get_or_query_cylinder_acting_type"
)
def test_on_enter(
    mock_get_cylinder_acting_type: MagicMock,
    cylinder_acting_type: str | None,
    filter: str | None,
):
    session = UnifiedAlchemyMagicMock()
    mock_get_cylinder_acting_type.return_value = cylinder_acting_type
    mock_catalog_repository.get_category_path.return_value = "path"

    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
        configuration,
        mock_jwt_token,
        mock_catalog_repository,
    )

    get_product_flowpanel({}, session, configuration, configurator.state_definition, configurator._catalog_repository)

    for c in configurator.state_definition["content"]:
        if c["type"] == "filters":
            assert c["meta"]["filters"] == filter
            assert c["meta"]["category"] == "path"


@pytest.mark.parametrize(
    "flowpanel_valve, flow_panel_quantity, cylinder_acting_type, flow_panel_type, calls",
    [
        pytest.param(
            [Product(), [Product(article_number="")]],
            {"products": [], "total_quantity": 1},
            "single",
            "on_top",
            [
                call(
                    configuration,
                    ConfiguratorStateProducts(
                        state_id=ConfiguratorState.INDUSTRIAL_LIFTING_FLOW_PANEL_ASSEMBLY_SET.value,
                        products=[ConfiguratorProduct(article_number="100.182.111", quantity=1, product_id=None)],
                    ),
                )
            ],
            id="flow panel on top single acting - no valve selected",
        ),
        pytest.param(
            [Product(), [Product(article_number="100.181.311")]],
            {"products": [], "total_quantity": 1},
            "single",
            "on_top",
            [
                call(
                    configuration,
                    ConfiguratorStateProducts(
                        state_id=ConfiguratorState.INDUSTRIAL_LIFTING_FLOW_PANEL_ASSEMBLY_SET.value,
                        products=[ConfiguratorProduct(article_number="100.182.111", quantity=1, product_id=None)],
                    ),
                )
            ],
            id="flow panel on top single acting",
        ),
        pytest.param(
            [Product(), [Product(article_number="100.181.322")]],
            {"products": [], "total_quantity": 1},
            "single",
            "on_top",
            [
                call(
                    configuration,
                    ConfiguratorStateProducts(
                        state_id=ConfiguratorState.INDUSTRIAL_LIFTING_FLOW_PANEL_ASSEMBLY_SET.value,
                        products=[ConfiguratorProduct(article_number="100.182.112", quantity=1, product_id=None)],
                    ),
                )
            ],
            id="flow panel on top single acting with double acting valve",
        ),
        pytest.param(
            [Product(), [Product()]],
            {"products": [], "total_quantity": 1},
            "double",
            "on_top",
            [
                call(
                    configuration,
                    ConfiguratorStateProducts(
                        state_id=ConfiguratorState.INDUSTRIAL_LIFTING_FLOW_PANEL_ASSEMBLY_SET.value,
                        products=[ConfiguratorProduct(article_number="100.182.112", quantity=1, product_id=None)],
                    ),
                )
            ],
            id="flow panel on top double acting",
        ),
        pytest.param(
            [Product(), [Product()]],
            {"products": [], "total_quantity": 1},
            "single",
            "separate",
            [],
            id="flow panel separate",
        ),
        pytest.param([Product()], {"products": [], "total_quantity": 3}, "double", "separate", [], id="no valve"),
        pytest.param(
            [Product(), [Product()]], {"products": [], "total_quantity": 1}, None, "on_top", [], id="no acting type"
        ),
        pytest.param(
            [None, [Product()]], {"products": [], "total_quantity": 1}, "single", "on_top", [], id="no flow panel"
        ),
        pytest.param(
            [Product(), [Product()]], {"products": [], "total_quantity": 1}, "single", None, [], id="no flow panel type"
        ),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListMain.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.flowpanel_productlist.get_or_query_value"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.flowpanel_productlist.get_or_query_cylinder_acting_type"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.flowpanel_productlist.get_product_quantities"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.flowpanel_productlist.get_or_query_products"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.system_components_helpers.flowpanel_productlist.ConfiguratorMapper.store_configuration_product"
)
def test_on_exit(
    mock_mapper: MagicMock,
    mock_get_product,
    mock_get_product_quantity,
    mock_get_acting_type,
    mock_get_data,
    mock_hose_main,
    flowpanel_valve: list[Product],
    flow_panel_quantity: int,
    cylinder_acting_type: str | None,
    flow_panel_type: str | None,
    calls,
):
    session = UnifiedAlchemyMagicMock()
    mock_get_product.side_effect = flowpanel_valve
    mock_get_product_quantity.return_value = flow_panel_quantity
    mock_get_acting_type.return_value = cylinder_acting_type
    mock_get_data.return_value = flow_panel_type

    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
        configuration,
        mock_jwt_token,
    )
    configurator.next()

    mock_mapper.assert_has_calls(calls)


@pytest.mark.parametrize(
    "data, pump_type, cylinder_acting_type, final_state",
    [
        pytest.param(
            ["yes"],  # hand_pump_gaugeset_selection
            None,
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET,
            id="hand pump - choose a gauge",
        ),
        pytest.param(
            [
                "no",
                None,
                None,
                None,
                "yes",
            ],  # gaugeset_selection: hand_pump / compact_pump_air / vari_pump - hand_pump_valve
            None,
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE,
            id="hand pump - NOT choose a gauge and choose a valve",
        ),
        pytest.param(
            [None, "yes"],  # hand_pump_gaugeset_selection / compact_pump_gaugeset_selection
            None,
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET,
            id="compact pump - choose a gauge",
        ),
        pytest.param(
            [None, None, "gaugeonly"],  # gaugeset_selection: hand_pump / compact_pump_air / compact_pump_electric
            None,
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET,
            id="compact pump electric - choose a gauge",
        ),
        pytest.param(
            [None, None, "gaugeandneedle"],  # gaugeset_selection: hand_pump / compact_pump_air / compact_pump_electric
            None,
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET,
            id="compact pump electric - choose a gauge and needle",
        ),
        pytest.param(
            [
                None,
                None,
                None,
                "yes",
            ],  # hand_pump_gaugeset / compact_pump_gaugeset / compact_pump_electric / vari_pump_gaugeset
            None,
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET,
            id="vari pump - choose a gauge",
        ),
        pytest.param(
            [
                None,
                None,
                None,
                "no",
                "separate",
            ],  # hand_pump_gaugeset / compact_pump_gaugeset / compact_pump_electric / vari_pump_gaugeset / flow_panel_type
            "vari",
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS,
            id="vari pump separate - NOT choose a gauge",
        ),
        pytest.param(
            [None, None, None, None],  # hand_pump_gaugeset / compact_pump_gaugeset / vari_pump_gaugeset / cylinder
            None,
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING,
            id="unknown pump type and cylinder type",
        ),
        pytest.param(
            [None, None, None, None],  # hand_pump_gaugeset / compact_pump_gaugeset / vari_pump_gaugeset
            None,
            "single",
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS,
            id="unknown pump type - cylinder selected",
        ),
        pytest.param(
            [
                None,
                None,
                None,
                None,
                None,
            ],  # hand_pump_gaugeset / compact_pump_gaugeset / vari_pump_gaugeset / cylinder
            "vari",
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST,
            id="vari pump with no selections",
        ),
        pytest.param(
            [
                None,
                None,
                None,
                None,
                "on_top",
            ],  # hand_pump_gaugeset / compact_pump_gaugeset / vari_pump_gaugeset / cylinder
            "vari",
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS,
            id="vari pump on top",
        ),
        pytest.param(
            [None, None, None, None],  # hand_pump_gaugeset / compact_pump_gaugeset / vari_pump_gaugeset / cylinder
            "hand",
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST,
            id="hand pump with no selections",
        ),
    ],
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.get_product_valves")
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.get_product_gaugeset"
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.PumpSettings.on_enter")
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.PumpProductList.on_enter")
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_flowpanel_productlist.get_or_query_cylinder_acting_type"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_flowpanel_productlist.get_or_query_pump_type"
)
@patch("holmatro_customer_portal.services.configurator.utils.get_configuration_data_value")
def test_back_transitions(
    mock_get_data: MagicMock,
    mock_get_pump_type: MagicMock,
    mock_get_cylinder_acting_type: MagicMock,
    mock_pump_product_list: MagicMock,
    mock_pump_settings: MagicMock,
    mock_get_product_gaugeset: MagicMock,
    mock_get_product_valves: MagicMock,
    data,
    pump_type: str | None,
    cylinder_acting_type: str | None,
    final_state: ConfiguratorState,
):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
        configuration,
        mock_jwt_token,
    )

    mock_get_data.side_effect = data
    mock_get_pump_type.return_value = pump_type
    mock_get_cylinder_acting_type.return_value = cylinder_acting_type

    configurator.back()
    assert configurator.state == final_state


@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListMain.on_enter",
)
def test_next_transitions(
    mock_hose_main_on_enter: MagicMock,
):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
        configuration,
        mock_jwt_token,
    )

    configurator.next()
    assert configurator.state == ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN


@pytest.mark.parametrize(
    "data, pump_type, cylinder_acting_type, final_state",
    [
        pytest.param(
            [None, None],
            "hand",
            "double",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="hand pump double acting",
        ),
        pytest.param(
            None,  # protection_frame_cover_selection / flowpanel_setup
            "compact",
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="compact pump",
        ),
        pytest.param(
            ["on_top", None],  # flowpanel_setup
            "vari",
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
            id="vari pump flow panel on top",
        ),
        pytest.param(
            ["existing", None],  # protection_frame_cover_selection / flowpanel_setup
            "vari",
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
            id="vari pump existing",
        ),
        pytest.param(
            None,  # protection_frame_cover_selection / flowpanel_setup
            None,
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
            id="unknown pump type",
        ),
    ],
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_overview.Overview.on_enter")
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListMain.on_enter",
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListPumpToPanel.on_enter",
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_flowpanel_productlist.get_or_query_cylinder_acting_type"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_flowpanel_productlist.get_or_query_pump_type"
)
@patch("holmatro_customer_portal.services.configurator.utils.get_configuration_data_value")
def test_skip_transitions(
    mock_get_data: MagicMock,
    mock_get_pump_type: MagicMock,
    mock_get_cylinder_acting_type: MagicMock,
    mock_hose_main_on_enter: MagicMock,
    mock_hose_extra_on_enter: MagicMock,
    mock_overview: MagicMock,
    data,
    pump_type: str | None,
    cylinder_acting_type: str | None,
    final_state: ConfiguratorState,
):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
        configuration,
        mock_jwt_token,
    )

    mock_get_data.side_effect = data
    mock_get_pump_type.return_value = pump_type
    mock_get_cylinder_acting_type.return_value = cylinder_acting_type

    configurator.skip()
    assert configurator.state == final_state
