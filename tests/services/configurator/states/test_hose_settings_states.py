from unittest.mock import MagicMock, patch

import pytest
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Configuration, Product
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.workflow import ConfiguratorFactory
from tests.fixtures.helpers import create_test_configurator
from tests.fixtures.test_client import mock_db_user, mock_jwt_token

configuration = Configuration(user=mock_db_user())


@pytest.mark.parametrize(
    "cylinder_quantity, data, flow_panel_product, final_state",
    [
        pytest.param(
            None,
            [None, None, None],  # hand_pump_gaugeset / compact_pump_electric_gaugeset / compact_pump_air_gaugeset
            Product(),
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN,
            id="Unknown",
        ),
        pytest.param(
            1,
            [None, None, None],  # hand_pump_gaugeset / compact_pump_electric_gaugeset / compact_pump_air_gaugeset
            Product(),
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN,
            id="Default",
        ),
        pytest.param(
            2,
            [None, None, None],  # hand_pump_gaugeset / compact_pump_electric_gaugeset / compact_pump_air_gaugeset
            Product(),
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN,
            id="More than ONE cylinder",
        ),
        pytest.param(
            1,
            ["yes", None, None],  # hand_pump_gaugeset / compact_pump_electric_gaugeset / compact_pump_air_gaugeset
            Product(),
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET,
            id="Hand pump ONE cylinder - choose a gauge",
        ),
        pytest.param(
            1,
            ["no", "yes", None],  # hand_pump_gaugeset / hanp_pump_valve
            Product(),
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE,
            id="Hand pump ONE cylinder - NOT choose a gauge, choose a valve",
        ),
        pytest.param(
            1,
            ["yes", "no", "no"],  # hand_pump_gaugeset / hanp_pump_valve
            Product(),
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST,
            id="Hand pump ONE cylinder - NOT choose a gauge neither choose a valve",
        ),
        pytest.param(
            1,
            [None, None, "yes"],  # hand_pump_gaugeset / compact_pump_electric_gaugeset / compact_pump_air_gaugeset
            Product(),
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET,
            id="Compact pump air ONE cylinder - choose a gauge",
        ),
        pytest.param(
            1,
            [None, None, "no"],  # hand_pump_gaugeset / compact_pump_electric_gaugeset / compact_pump_air_gaugeset
            Product(),
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST,
            id="Compact pump air ONE cylinder - NOT choose a gauge",
        ),
        pytest.param(
            1,
            [
                None,
                "gaugeonly",
                None,
            ],  # hand_pump_gaugeset / compact_pump_electric_gaugeset / compact_pump_air_gaugeset
            Product(),
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET,
            id="Compact pump electric ONE cylinder - choose a gauge/valve",
        ),
        pytest.param(
            1,
            [
                None,
                "gaugeandneedle",
                None,
            ],  # hand_pump_gaugeset / compact_pump_electric_gaugeset / compact_pump_air_gaugeset
            Product(),
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET,
            id="Compact pump electric ONE cylinder - choose a gauge/valve",
        ),
        pytest.param(
            1,
            [None, "nogauge", None],  # hand_pump_gaugeset / compact_pump_electric_gaugeset / compact_pump_air_gaugeset
            Product(),
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST,
            id="Compact pump electric ONE cylinder - NOT choose a gauge/valve",
        ),
        pytest.param(
            1,
            [None, None, None],  # hand_pump_gaugeset / compact_pump_electric_gaugeset / compact_pump_air_gaugeset
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="If flow panel not selected",
        ),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsFlowPanelProductList.on_enter",
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListMain.on_enter",
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsValve.on_enter",
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsGaugeset.on_enter",
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_lifting_pump.PumpProductList.on_enter")
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_hose_settings.get_or_query_products"
)
@patch("holmatro_customer_portal.services.configurator.utils.get_configuration_data_value")
@patch("holmatro_customer_portal.services.configurator.queries.get_product_quantities")
def test_back_transitions(
    mock_get_cylinder_quantity: MagicMock,
    mock_get_data: MagicMock,
    mock_get_product: MagicMock,
    mock_on_enter_pump_productlist: MagicMock,
    mock_on_enter_gaugeset: MagicMock,
    mock_on_enter_valve: MagicMock,
    mock_on_enter_hose: MagicMock,
    mock_on_enter_flowpanel_product_list: MagicMock,
    cylinder_quantity: int,
    data,
    flow_panel_product: Product,
    final_state: ConfiguratorState,
):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS, configuration, mock_jwt_token
    )

    mock_get_cylinder_quantity.return_value = cylinder_quantity
    mock_get_data.side_effect = data
    mock_get_product.return_value = flow_panel_product

    configurator.back()
    assert configurator.state == final_state


@pytest.mark.parametrize(
    "hose_type, state",
    [
        ("mounted", ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL),
        ("extension", ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL),
        ("existing", ConfiguratorState.INDUSTRIAL_OVERVIEW),
    ],
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_overview.Overview.on_enter")
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListPumpToPanel.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_hose_settings.get_or_query_value"
)
def test_next_transitions(
    mock_get_hose_type: MagicMock,
    mock_hoses_product_list: MagicMock,
    mock_overview_on_enter: MagicMock,
    hose_type: str,
    state: ConfiguratorState,
):
    session = UnifiedAlchemyMagicMock()
    mock_get_hose_type.return_value = hose_type
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS, configuration, mock_jwt_token
    )

    configurator.next()
    assert configurator.state == state


@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListPumpToPanel.on_enter"
)
def test_skip_transitions(mock_hoses_product_list: MagicMock):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS, configuration, mock_jwt_token
    )

    configurator.skip()
    assert configurator.state == ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL
