from holmatro_customer_portal.services.configurator.states.base import BaseState, ConfiguratorState


def test_names():
    class TestState(BaseState):
        state = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING

    assert TestState().on_enter_method_name == "on_enter_industrial_lifting_pump_settings_acting"
    assert TestState().definition == {
        "name": ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING,
        "on_enter": ["load_state_definition", "on_enter_industrial_lifting_pump_settings_acting"],
        "on_exit": ["on_exit_industrial_lifting_pump_settings_acting"],
    }
