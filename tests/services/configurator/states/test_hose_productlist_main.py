from unittest.mock import MagicMock, patch

import pytest
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.workflow import ConfiguratorFactory
from tests.fixtures.helpers import create_test_configurator
from tests.fixtures.my_hm_fixture import my_hm_mock_responses
from tests.fixtures.test_client import mock_catalog_repository, mock_db_user, mock_jwt_token

configuration = Configuration(user=mock_db_user())


@pytest.mark.parametrize(
    "pump_type, flowpanel_setup, final_state",
    [
        pytest.param(
            "vari",
            "on_top",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="vari pump 'on top' type",
        ),
        pytest.param(
            "vari",
            "separate",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="vari pump 'separate' type",
        ),
        pytest.param(
            "vari",
            "existing",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS,
            id="vari pump 'existing' type",
        ),
        pytest.param(
            "hand", None, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST, id="hand pump"
        ),
        pytest.param(
            "compact",
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            id="compact pump",
        ),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsFlowPanelProductList.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListPumpToPanel.on_enter"
)
@patch("holmatro_customer_portal.services.configurator.utils.get_configuration_data_value")
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_hose_productlist_main.get_or_query_pump_type"
)
def test_back_transitions(
    mock_get_pump_type: MagicMock,
    mock_get_data: MagicMock,
    mock_hoses_product_list: MagicMock,
    mock_flowpanel_on_enter: MagicMock,
    pump_type: str | None,
    flowpanel_setup: str | None,
    final_state: ConfiguratorState,
):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN,
        configuration,
        mock_jwt_token,
    )
    mock_get_pump_type.return_value = pump_type
    mock_get_data.return_value = flowpanel_setup

    configurator.back()

    assert configurator.state == final_state


@pytest.mark.parametrize(
    "pump_type, data, next_state",
    [
        pytest.param(
            "vari",
            [None, None],  # flow panel type, frame_cover_choice
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
            id="vari pump without a choice",
        ),
        pytest.param(
            "vari",
            ["separate", "no"],  # flow panel type, frame_cover_choice
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
            id="vari pump separate - frame cover not selected",
        ),
        pytest.param(
            "vari",
            ["separate", "yes"],  # flow panel type, frame_cover_choice
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME,
            id="vari pump separate - frame cover selected",
        ),
        pytest.param(
            "vari",
            ["existing", None],  # flow panel type, frame_cover_choice
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
            id="vari pump existing",
        ),
        pytest.param(
            "hand",
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="hand pump",
        ),
        pytest.param(
            "compact",
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="compact pump",
        ),
        pytest.param(
            None,
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
            id="unknown",
        ),
        pytest.param(
            "vari", ["on_top"], ConfiguratorState.INDUSTRIAL_OVERVIEW, id="vari pump on top"
        ),  # flow panel type
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsProtectionFrame.on_enter"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListPumpToPanel.on_enter"
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_overview.Overview.on_enter")
@patch("holmatro_customer_portal.services.configurator.utils.get_configuration_data_value")
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_hose_productlist_main.get_or_query_pump_type"
)
def test_next_transitions(
    mock_get_pump_type: MagicMock,
    mock_get_data: MagicMock,
    mock_overview: MagicMock,
    _,
    __,
    pump_type: str,
    data: str,
    next_state: ConfiguratorState,
):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN,
        configuration,
        mock_jwt_token,
    )

    mock_get_pump_type.return_value = pump_type
    mock_get_data.side_effect = data

    configurator.next()

    assert configurator.state == next_state


@pytest.mark.parametrize(
    "pump_type, flowpanel_setup, next_state",
    [
        pytest.param("vari", "on_top", ConfiguratorState.INDUSTRIAL_OVERVIEW, id="vari pump on top"),
        pytest.param(
            "vari",
            "separate",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
            id="vari pump separate",
        ),
        pytest.param(
            "vari",
            "existing",
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
            id="vari pump existing",
        ),
        pytest.param(
            "hand",
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="hand pump",
        ),
        pytest.param(
            "compact",
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS,
            id="compact pump",
        ),
        pytest.param(
            None,
            None,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL,
            id="unknown",
        ),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.SystemComponentsHoseProductListPumpToPanel.on_enter"
)
@patch("holmatro_customer_portal.services.configurator.states.industrial_overview.Overview.on_enter")
@patch("holmatro_customer_portal.services.configurator.utils.get_configuration_data_value")
@patch(
    "holmatro_customer_portal.services.configurator.transitions.industrial_lifting_system_components_hose_productlist_main.get_or_query_pump_type"
)
def test_skip_transitions(
    mock_get_pump_type: MagicMock,
    mock_get_data: MagicMock,
    mock_overview: MagicMock,
    mock_hoses_product_list: MagicMock,
    pump_type: str,
    flowpanel_setup: str,
    next_state: ConfiguratorState,
):
    session = UnifiedAlchemyMagicMock()
    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN,
        configuration,
        mock_jwt_token,
    )

    mock_get_pump_type.return_value = pump_type
    mock_get_data.return_value = flowpanel_setup

    configurator.skip()

    assert configurator.state == next_state


@pytest.mark.parametrize(
    "cylinder_quantity, cylinder_type, minimum_quantity",
    [
        pytest.param(
            1,
            "single",
            1,
            id="single acting",
        ),
        pytest.param(
            4,
            "double",
            8,
            id="double acting",
        ),
        pytest.param(
            2,
            None,
            2,
            id="no info",
        ),
    ],
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.get_or_query_cylinder_acting_type"
)
@patch(
    "holmatro_customer_portal.services.configurator.states.industrial_lifting_system_components.get_or_query_cylinder_quantity"
)
@patch("holmatro_customer_portal.services.configurator.workflow.Configurator.set_default_product")
def test_on_enter(
    mock_set_default_product: MagicMock,
    mock_get_cylinder_quantity: MagicMock,
    mock_get_cylinder_acting_type: MagicMock,
    cylinder_quantity: int,
    cylinder_type: str | None,
    minimum_quantity: int,
):
    session = UnifiedAlchemyMagicMock()
    mock_get_cylinder_quantity.return_value = cylinder_quantity
    mock_get_cylinder_acting_type.return_value = cylinder_type
    mock_catalog_repository.get_category_path.return_value = "path"

    configurator = create_test_configurator(
        session,
        ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
        configuration,
        mock_jwt_token,
        mock_catalog_repository,
    )
    configurator.next()

    mock_set_default_product.assert_called_once_with("100.572.302")
    for c in configurator.state_definition["content"]:
        if c["type"] == "products":
            assert c["meta"]["initial"] == minimum_quantity
        if c["type"] == "filters":
            assert c["meta"]["filters"] == {f"{configuration.user.language.code}-hose-type": "extension hose"}
            assert c["meta"]["category"] == "path"
