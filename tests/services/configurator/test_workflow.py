import enum
from unittest.mock import MagicMock, mock_open, patch

from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.services.configurator.states.base import BaseState, ConfiguratorState
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition
from holmatro_customer_portal.services.configurator.workflow import Configurator
from tests.fixtures.helpers import create_test_configurator
from tests.fixtures.test_client import mock_jwt_token


@patch("holmatro_customer_portal.services.configurator.workflow.Configurator.load", return_value={"test": "test"})
def test_configurator_loads_definition(mock_load, catalog_repo_mock: MagicMock):
    configurator = Configurator(
        UnifiedAlchemyMagicMock(),
        [],
        ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS,
        Configuration(),
        mock_jwt_token,
        catalog_repository=catalog_repo_mock,
    )
    assert configurator.state == ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS.name
    assert configurator.state_definition == {"test": "test"}


@patch(
    "holmatro_customer_portal.services.configurator.workflow.Env.HOLMATRO_ASSETS_BUCKET_NAME.get",
    return_value="real-bucket",
)
@patch(
    "builtins.open",
    new_callable=mock_open,
    read_data='{"icon": "https://dummy-bucket.s3.eu-west-1.amazonaws.com/file"}',
)
def test_configurator_load(mock_file, mock_env):
    loaded = Configurator.load(ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS)
    assert "https://real-bucket.s3.eu-west-1.amazonaws.com/file" == loaded["icon"]


def test_configurator_factory_get_configurator_for_state():
    class TestTrigger(enum.Enum):
        TEST = "test"

    class TestState(BaseState):
        state = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS

        def on_enter(self, event):
            self.entered_test_state = True

    class TestTransition(BaseTransition):
        trigger = TestTrigger.TEST
        source = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS
        dest = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS

        def condition(self, event):
            self.passed_test_condition = True
            return True

    configurator = create_test_configurator(
        UnifiedAlchemyMagicMock(),
        ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS,
        Configuration(),
        mock_jwt_token,
    )
    configurator.test()

    assert configurator.passed_test_condition
    assert configurator.entered_test_state
