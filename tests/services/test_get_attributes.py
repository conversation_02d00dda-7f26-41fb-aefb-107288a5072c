from unittest.mock import call

from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.models import Attribute, AttributeTranslation, Language
from holmatro_customer_portal.services.get_attributes import get_attributes
from tests.fixtures.database_models_fixture import (
    mocked_attribute,
    mocked_attribute_no_content,
    mocked_language,
    mocked_translation,
    mocked_translation_no_content,
    mocked_user,
)


def test_get_attributes():
    # TODO: fix test

    session = UnifiedAlchemyMagicMock(
        data=[
            (
                [call.query(Language), call.filter(Language.language_code == mocked_user.language.code)],
                [mocked_language],
            ),
            (
                [
                    call.query(Attribute, AttributeTranslation),
                    call.join(AttributeTranslation, Attribute.translations),
                ],
                [(mocked_attribute, mocked_translation)],
            ),
        ]
    )

    attribute_groups = get_attributes(session, mocked_attribute.product_id, mocked_user, article_number="123")

    attribute_groups[0].group == mocked_translation.attribute_group_name
    attribute_groups[0].grouped_attributes[0].content == mocked_attribute.content


def test_get_attributes_without_content():
    # TODO: fix test
    session = UnifiedAlchemyMagicMock(
        data=[
            (
                [call.query(Language), call.filter(Language.language_code == mocked_user.language.code)],
                [mocked_language],
            ),
            (
                [
                    call.query(Attribute, AttributeTranslation),
                    call.join(AttributeTranslation, Attribute.translations),
                ],
                [(mocked_attribute_no_content, mocked_translation_no_content)],
            ),
        ]
    )
    attribute_groups = get_attributes(session, mocked_attribute.product_id, mocked_user, article_number="123")
    attribute_groups == []
