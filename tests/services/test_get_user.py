from unittest.mock import MagicMock, call, patch

from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.enums import Currency
from holmatro_customer_portal.database.models import Language, User
from holmatro_customer_portal.services.get_user import resync_user
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient
from tests.fixtures.my_hm_fixture import my_hm_mock_responses
from tests.fixtures.test_client import mock_user_claims


@patch("holmatro_customer_portal.services.get_user.fetch_user_categories")
@patch("holmatro_customer_portal.services.get_user.DatabaseOperations")
def test_resync_user(mock_operations, mock_fetch_user_categories, my_hm_mock_responses):
    categories = [MagicMock(name="cat 1"), MagicMock(name="cat 2")]
    mock_operations.return_value = (mock_db_ops := MagicMock())
    mock_fetch_user_categories.return_value = categories
    mock_db = UnifiedAlchemyMagicMock()
    mock_db_ops.upsert.return_value = "language"

    resync_user(mock_user_claims, mock_db, MyHolmatroPortalApiClient("jwt_token"))

    mock_db_ops.upsert.assert_has_calls(
        [
            call(Language, filter_by={"language_code": "en"}, language_code="en"),
            call(
                User,
                filter_by={"email": mock_user_claims.emails[0]},
                email=mock_user_claims.emails[0],
                last_name=mock_user_claims.family_name,
                name=mock_user_claims.name,  # Unknown standard B2C Claims output
                country="DE",
                currency=Currency.USD,
                relation_name="Holmatro",
                main_language="language",
                categories=categories,
            ),
        ]
    )


@patch("holmatro_customer_portal.services.get_user.sentry_sdk")
@patch("holmatro_customer_portal.services.get_user.fetch_user_categories")
@patch("holmatro_customer_portal.services.get_user.DatabaseOperations")
def test_resync_user_handles_no_matching_categories(
    mock_operations,
    mock_fetch_user_categories,
    mock_sentry_sdk,
    my_hm_mock_responses,
):
    mock_operations.return_value = (mock_db_ops := MagicMock())
    mock_fetch_user_categories.return_value = []
    mock_db = UnifiedAlchemyMagicMock()

    resync_user(mock_user_claims, mock_db, MyHolmatroPortalApiClient("jwt_token"))

    expected_error = RuntimeError("No matching categories found <NAME_EMAIL>!")
    mock_sentry_sdk.capture_exception.assert_called_once()

    actual_error = mock_sentry_sdk.capture_exception.call_args[0][0]
    assert str(actual_error) == str(expected_error)

    mock_db_ops.upsert.assert_not_called()
