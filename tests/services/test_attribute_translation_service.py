from unittest.mock import MagicMock, patch

import pytest
from sqlalchemy.exc import NoResultFound

from holmatro_customer_portal.database.models import Language
from holmatro_customer_portal.services.attribute_translation_service import AttributeTranslationService
from tests.fixtures.database import session


class TestAttributeTranslationService:
    """Test cases for AttributeTranslationService optimization."""

    def test_get_attribute_translations(self, session):
        """Test the new comprehensive method that combines both translation types."""
        service = AttributeTranslationService(session)

        # Mock comprehensive query results that include both metadata and value translations
        mock_comprehensive_result = [
            # Attribute metadata rows (name and unit)
            (123, "Test Attribuut", "kg", None, None),  # Dutch attribute name
            (456, "Another Attribute", "m", None, None),  # English attribute name
            # Attribute value translation rows
            (123, None, None, "red", "Rood"),  # Dutch value translation
            (123, None, None, "blue", "Blauw"),  # Dutch value translation
            (456, None, None, "large", "Groot"),  # Dutch value translation
        ]

        # Setup mock query
        mock_comprehensive_query = MagicMock()
        mock_comprehensive_query.join.return_value.join.return_value.filter.return_value.distinct.return_value.all.return_value = (
            mock_comprehensive_result
        )

        with patch.object(session, "query") as mock_session_query:
            mock_session_query.return_value = mock_comprehensive_query

            # Test data
            attribute_ids = [123, 456]
            attribute_values_by_id = {123: ["red", "blue"], 456: ["large"]}

            comprehensive_translations = service.get_attribute_translations(attribute_values_by_id, "nl")

            # Verify attribute translations
            assert 123 in comprehensive_translations
            assert 456 in comprehensive_translations
            assert comprehensive_translations[123]["title"] == "Test Attribuut"
            assert comprehensive_translations[123]["unit"] == "kg"
            assert comprehensive_translations[456]["title"] == "Another Attribute"
            assert comprehensive_translations[456]["unit"] == "m"

            # Verify value translations
            assert comprehensive_translations[123]["value_translations"]["red"] == "Rood"
            assert comprehensive_translations[123]["value_translations"]["blue"] == "Blauw"
            assert comprehensive_translations[456]["value_translations"]["large"] == "Groot"

    def test_get_attribute_translations_empty_inputs(self, session):
        """Test method with empty inputs."""
        service = AttributeTranslationService(session)

        # Test empty attribute_values_by_id
        result = service.get_attribute_translations({}, "nl")
        assert result == {}

    def test_get_attribute_translations_no_values(self, session):
        """Test method with only attribute metadata (no values to translate)."""
        service = AttributeTranslationService(session)

        # Mock query results with only attribute metadata
        mock_result = [
            (123, "Test Attribuut", "kg", None, None),
            (456, "Another Attribute", "m", None, None),
        ]

        mock_query = MagicMock()
        mock_query.join.return_value.join.return_value.filter.return_value.distinct.return_value.all.return_value = (
            mock_result
        )

        with patch.object(session, "query") as mock_session_query:
            mock_session_query.return_value = mock_query

            comprehensive_translations = service.get_attribute_translations({123: [], 456: []}, "nl")

            # Should have attribute translations but no value translations
            assert len(comprehensive_translations) == 2
            assert comprehensive_translations[123]["title"] == "Test Attribuut"
            assert comprehensive_translations[456]["title"] == "Another Attribute"
            # Value translations should be empty dictionaries
            assert comprehensive_translations[123]["value_translations"] == {}
            assert comprehensive_translations[456]["value_translations"] == {}

    def test_get_attribute_translations_attribute_metadata_only(self, session):
        """Test method focusing on attribute metadata functionality."""
        service = AttributeTranslationService(session)

        # Mock query results with only attribute metadata (no value translations)
        mock_result = [
            (100, "Color", None, None, None),  # English attribute
            (200, "Couleur", "unit", None, None),  # French attribute with unit
            (300, "Weight", "kg", None, None),  # English attribute with unit
        ]

        mock_query = MagicMock()
        mock_query.join.return_value.join.return_value.filter.return_value.distinct.return_value.all.return_value = (
            mock_result
        )

        with patch.object(session, "query") as mock_session_query:
            mock_session_query.return_value = mock_query

            # Test with multiple attribute IDs and no values to translate
            result = service.get_attribute_translations({100: [], 200: [], 300: []}, "fr")

            # Verify all attributes are present
            assert len(result) == 3
            assert 100 in result
            assert 200 in result
            assert 300 in result

            # Verify attribute metadata
            assert result[100]["title"] == "Color"
            assert result[100]["unit"] is None
            assert result[200]["title"] == "Couleur"
            assert result[200]["unit"] == "unit"
            assert result[300]["title"] == "Weight"
            assert result[300]["unit"] == "kg"

            # Verify no value translations
            assert result[100]["value_translations"] == {}
            assert result[200]["value_translations"] == {}
            assert result[300]["value_translations"] == {}

    def test_get_attribute_translations_value_translations_only(self, session):
        """Test method focusing on value translation functionality."""
        service = AttributeTranslationService(session)

        # Mock query results with only value translations (no attribute metadata)
        mock_result = [
            (100, None, None, "red", "rouge"),  # French value translation
            (100, None, None, "blue", "bleu"),  # French value translation
            (100, None, None, "green", "vert"),  # French value translation
        ]

        mock_query = MagicMock()
        mock_query.join.return_value.join.return_value.filter.return_value.distinct.return_value.all.return_value = (
            mock_result
        )

        with patch.object(session, "query") as mock_session_query:
            mock_session_query.return_value = mock_query

            # Test with single attribute and multiple values to translate
            attribute_values_by_id = {100: ["red", "blue", "green"]}
            result = service.get_attribute_translations(attribute_values_by_id, "fr")

            # Verify attribute is present
            assert 100 in result

            # Verify no attribute metadata (since none provided in mock)
            assert result[100]["title"] is None
            assert result[100]["unit"] is None

            # Verify value translations
            assert len(result[100]["value_translations"]) == 3
            assert result[100]["value_translations"]["red"] == "rouge"
            assert result[100]["value_translations"]["blue"] == "bleu"
            assert result[100]["value_translations"]["green"] == "vert"

    def test_get_attribute_translations_mixed_languages(self, session):
        """Test method with mixed language scenarios."""
        service = AttributeTranslationService(session)

        # Mock query results with mixed translations (some translated, some not)
        mock_result = [
            (100, "Kleur", None, None, None),  # Dutch attribute name
            (100, None, None, "red", "rood"),  # Dutch value translation
            (100, None, None, "blue", "blauw"),  # Dutch value translation
            (200, "Size", "cm", None, None),  # English attribute (no Dutch translation available)
            # Note: "green" has no translation, "large" has no translation
        ]

        mock_query = MagicMock()
        mock_query.join.return_value.join.return_value.filter.return_value.distinct.return_value.all.return_value = (
            mock_result
        )

        with patch.object(session, "query") as mock_session_query:
            mock_session_query.return_value = mock_query

            # Test with multiple attributes and mixed translation availability
            attribute_values_by_id = {
                100: ["red", "blue", "green"],  # green has no translation
                200: ["large", "small"],  # no value translations available
            }
            result = service.get_attribute_translations(attribute_values_by_id, "nl")

            # Verify both attributes are present
            assert 100 in result
            assert 200 in result

            # Verify attribute 100 (has Dutch translations)
            assert result[100]["title"] == "Kleur"
            assert result[100]["unit"] is None
            assert result[100]["value_translations"]["red"] == "rood"
            assert result[100]["value_translations"]["blue"] == "blauw"
            # "green" should not be in value_translations since it wasn't in mock results
            assert "green" not in result[100]["value_translations"]

            # Verify attribute 200 (English fallback, no value translations)
            assert result[200]["title"] == "Size"
            assert result[200]["unit"] == "cm"
            assert result[200]["value_translations"] == {}

    def test_get_attribute_translations_partial_value_filtering(self, session):
        """Test that only requested attribute values are included in translations."""
        service = AttributeTranslationService(session)

        # Mock query results with more translations than requested
        mock_result = [
            (100, "Color", None, None, None),
            (100, None, None, "red", "rouge"),  # Requested
            (100, None, None, "blue", "bleu"),  # Requested
            (100, None, None, "yellow", "jaune"),  # NOT requested - should be filtered out
            (100, None, None, "green", "vert"),  # NOT requested - should be filtered out
        ]

        mock_query = MagicMock()
        mock_query.join.return_value.join.return_value.filter.return_value.distinct.return_value.all.return_value = (
            mock_result
        )

        with patch.object(session, "query") as mock_session_query:
            mock_session_query.return_value = mock_query

            # Only request translations for "red" and "blue"
            attribute_values_by_id = {100: ["red", "blue"]}
            result = service.get_attribute_translations(attribute_values_by_id, "fr")

            # Verify only requested values are included
            assert len(result[100]["value_translations"]) == 2
            assert result[100]["value_translations"]["red"] == "rouge"
            assert result[100]["value_translations"]["blue"] == "bleu"
            # These should NOT be included even though they were in the mock results
            assert "yellow" not in result[100]["value_translations"]
            assert "green" not in result[100]["value_translations"]
