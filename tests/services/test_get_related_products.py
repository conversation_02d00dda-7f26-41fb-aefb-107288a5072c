import uuid
from dataclasses import dataclass
from unittest.mock import MagicMock, patch
from uuid import uuid4

from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.enums import RelationshipType
from holmatro_customer_portal.services.get_related_products import get_related_products_with_translation
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient
from tests.fixtures.database_models_fixture import mocked_user
from tests.fixtures.my_hm_fixture import my_hm_mock_responses
from tests.fixtures.query_factory import MockQueryFactory


@dataclass
class MockRelatedProductQueryClass:
    related_product_id: uuid.UUID
    name: str
    relationship_id: int


@patch("holmatro_customer_portal.services.get_related_products.get_product_preview_by_ids")
@patch("holmatro_customer_portal.services.get_related_products.fetch_related_products")
def test_get_related_products_with_translation(
    mock_fetch_related_products: MagicMock, mock_get_product_preview_by_ids: <PERSON>Mock, my_hm_mock_responses
):
    product_id = uuid4()
    mock_session = UnifiedAlchemyMagicMock()

    query_factory = MockQueryFactory(product_id=product_id)

    mock_fetch_related_products.return_value = [MockQueryFactory(product_id=product_id).get_related_product()]
    mock_get_product_preview_by_ids.return_value = [
        query_factory.get_mock_product_preview(
            product_article_number="100.100.101",
            product_classification_id=2109,  # Battery, should be filtered out
        ),
        query_factory.get_mock_product_preview(
            product_article_number="100.100.102",
            product_classification_id=2100,
        ),
        query_factory.get_mock_product_preview(
            product_article_number="100.100.103",
            product_classification_id=2110,  # Battery Charger, should be filtered out
        ),
    ]
    mock_my_hm_client = MyHolmatroPortalApiClient("1")

    result = get_related_products_with_translation(
        product_id=product_id,
        user=mocked_user,
        db=mock_session,
        my_hm_client=mock_my_hm_client,
        relationship_ids=RelationshipType.ALTERNATIVE_PRODUCTS.value,
        exclude_classifications=[2109, 2110],
    )

    assert isinstance(result, list)
    assert len(result) == 1
    assert result[0].name == "Alternative products"
    assert result[0].product.article_number == "100.100.102"
