from unittest.mock import Mock

import pytest

from holmatro_customer_portal.data.holmatro_catalog_repository import HolmatroCatalogRepository
from holmatro_customer_portal.utils.enums import get_language_code_from_id
from tests.factories import CategoryFactory, CategoryTranslationFactory, LanguageFactory


class TestIntegrationScenarios:
    """Integration tests for complete category tree scenarios"""

    @pytest.fixture
    def mock_opensearch_client(self):
        """Mock OpenSearch client."""
        mock_client = Mock()
        mock_client.search_products = Mock()
        return mock_client

    @pytest.fixture
    def repository(self, session, mock_opensearch_client):
        return HolmatroCatalogRepository(db=session, opensearch_client=mock_opensearch_client)

    def test_complete_category_tree_scenario(self, repository, session):
        """Test complete scenario: Dutch user requesting Industrial Lifting category"""
        # Setup: Dutch language (2000) + Industrial Lifting category (7411)
        category_id = 20007411

        dutch_language = LanguageFactory.build(language_code="nl")
        session.add(dutch_language)
        session.flush()

        # Create category hierarchy: Industrial Lifting (7411) -> Cylinders (5121)
        # Create parent category (Industrial Lifting)
        lifting_category = CategoryFactory.build(category_id="7411")
        session.add(lifting_category)
        session.flush()

        # Create child category (Cylinders)
        cylinders_category = CategoryFactory.build(category_id="5121", parent_id=lifting_category.id)
        session.add(cylinders_category)
        session.flush()

        # The parent-child relationship is already set up via parent_id

        # Create translations using factory
        lifting_translation = CategoryTranslationFactory.build(
            name="Industrieel Heffen", language_id=dutch_language.id, category_id=lifting_category.id
        )
        cylinders_translation = CategoryTranslationFactory.build(
            name="Cilinders", language_id=dutch_language.id, category_id=cylinders_category.id
        )

        session.add(lifting_translation)
        session.add(cylinders_translation)
        session.commit()

        # Test the splitting logic
        category_id_str = str(category_id)
        language_id = category_id_str[:4]  # "2000"
        remaining_category_id = category_id_str[4:]  # "7411"

        assert language_id == "2000"
        assert remaining_category_id == "7411"

        # Test language code conversion
        language_code = get_language_code_from_id(int(language_id))
        assert language_code == "NL"

        # Test category loading (should return actual Category object)
        result = repository.get_category_tree(category_id)

        # Verify the result
        assert result is not None
        assert result.category_id == 20007411  # language_id (2000) + category_id (7411)
        assert result.title == "Industrieel Heffen"  # Dutch translation
        assert result.category_path == "20007411"  # No parent, so just the category itself
        assert len(result.children) == 1  # Has one child (cylinders)

        # Verify child category
        child = result.children[0]
        assert child.category_id == 20005121  # language_id (2000) + category_id (5121)
        assert child.title == "Cilinders"
        assert child.category_path == "20007411-20005121"  # parent-child path (1 level up)

    def test_english_industrial_cutting_scenario(self, repository, session):
        """Test scenario: English user requesting Industrial Cutting category"""
        # Setup: English language (1000) + Industrial Cutting category (2838)
        category_id = 10002838

        # Create English language using factory
        english_language = LanguageFactory.build(language_code="en")
        session.add(english_language)
        session.flush()

        # Create category hierarchy: Industrial Cutting (2838) -> Cutters (6921)
        # Create parent category (Industrial Cutting)
        cutting_category = CategoryFactory.build(category_id="2838")
        session.add(cutting_category)
        session.flush()

        # Create child category (Cutters)
        cutters_category = CategoryFactory.build(category_id="6921", parent_id=cutting_category.id)
        session.add(cutters_category)
        session.flush()

        # The parent-child relationship is already set up via parent_id

        # Create translations using factory
        cutting_translation = CategoryTranslationFactory.build(
            name="Industrial Cutting", language_id=english_language.id, category_id=cutting_category.id
        )
        cutters_translation = CategoryTranslationFactory.build(
            name="Cutters", language_id=english_language.id, category_id=cutters_category.id
        )

        session.add(cutting_translation)
        session.add(cutters_translation)
        session.commit()

        # Test the complete flow
        result = repository.get_category_tree(category_id)

        # Verify the result
        assert result is not None
        assert result.category_id == 10002838  # language_id (1000) + category_id (2838)
        assert result.title == "Industrial Cutting"  # English translation
        assert result.category_path == "10002838"  # No parent, so just the category itself
        assert len(result.children) == 1  # Has one child (cutters)

        # Verify child category
        child = result.children[0]
        assert child.category_id == 10006921  # language_id (1000) + category_id (6921)
        assert child.title == "Cutters"
        assert child.category_path == "10002838-10006921"  # parent-child path (1 level up)

    def test_sort_children_by_sort_index(self, repository, session):
        """Test that children are sorted by sort_index while maintaining tree structure"""
        # Create English language using factory
        english_language = LanguageFactory.build(language_code="en")
        session.add(english_language)
        session.flush()

        # Create parent category
        parent_category = CategoryFactory.build(category_id="1000", sort_index=0)
        session.add(parent_category)
        session.flush()

        # Create child categories with different sort_index values (in random order)
        child_c = CategoryFactory.build(
            category_id="1003", parent_id=parent_category.id, sort_index=30  # Should be third
        )
        child_a = CategoryFactory.build(
            category_id="1001", parent_id=parent_category.id, sort_index=10  # Should be first
        )
        child_b = CategoryFactory.build(
            category_id="1002", parent_id=parent_category.id, sort_index=20  # Should be second
        )

        session.add_all([child_c, child_a, child_b])
        session.flush()

        # Create translations for all categories
        parent_translation = CategoryTranslationFactory.build(
            name="Parent Category", language_id=english_language.id, category_id=parent_category.id
        )
        child_a_translation = CategoryTranslationFactory.build(
            name="Child A", language_id=english_language.id, category_id=child_a.id
        )
        child_b_translation = CategoryTranslationFactory.build(
            name="Child B", language_id=english_language.id, category_id=child_b.id
        )
        child_c_translation = CategoryTranslationFactory.build(
            name="Child C", language_id=english_language.id, category_id=child_c.id
        )

        session.add_all([parent_translation, child_a_translation, child_b_translation, child_c_translation])
        session.commit()

        # Test the get_category_tree method
        result = repository.get_category_tree(10001000)  # 1000 + 1000 (English)

        # Verify children are sorted by sort_index
        assert len(result.children) == 3
        assert result.children[0].title == "Child A"  # sort_index: 10
        assert result.children[1].title == "Child B"  # sort_index: 20
        assert result.children[2].title == "Child C"  # sort_index: 30
