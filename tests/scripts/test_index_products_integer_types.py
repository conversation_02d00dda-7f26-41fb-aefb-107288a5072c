"""Test integer type detection in product indexing."""

import pytest
from unittest.mock import Mock
from holmatro_customer_portal.scripts.index_products import ProductIndexer


class TestProductIndexerIntegerTypes:
    """Test the integer type detection logic in ProductIndexer."""

    def setup_method(self):
        """Set up test fixtures."""
        # Create a mock OpenSearch client to avoid connection issues
        mock_client = Mock()
        self.indexer = ProductIndexer(mock_client)

    def test_is_integer_type_with_integer_keywords(self):
        """Test that attributes with integer-related keywords are detected as integers."""
        # Test various integer type indicators
        assert self.indexer._is_integer_type("integer") is True
        assert self.indexer._is_integer_type("INTEGER") is True
        assert self.indexer._is_integer_type("int") is True
        assert self.indexer._is_integer_type("INT") is True
        assert self.indexer._is_integer_type("whole") is True
        assert self.indexer._is_integer_type("count") is True
        assert self.indexer._is_integer_type("quantity") is True
        assert self.indexer._is_integer_type("number_of") is True
        assert self.indexer._is_integer_type("nr_of") is True

    def test_is_integer_type_with_float_keywords(self):
        """Test that attributes with float-related keywords are not detected as integers."""
        # Test various non-integer type indicators
        assert self.indexer._is_integer_type("float") is False
        assert self.indexer._is_integer_type("decimal") is False
        assert self.indexer._is_integer_type("double") is False
        assert self.indexer._is_integer_type("numeric") is False
        assert self.indexer._is_integer_type("text") is False
        assert self.indexer._is_integer_type("string") is False

    def test_is_integer_type_with_none_or_empty(self):
        """Test that None or empty strings return False."""
        assert self.indexer._is_integer_type(None) is False
        assert self.indexer._is_integer_type("") is False
        assert self.indexer._is_integer_type("   ") is False

    def test_is_integer_type_case_insensitive(self):
        """Test that the detection is case-insensitive."""
        assert self.indexer._is_integer_type("Integer") is True
        assert self.indexer._is_integer_type("Count") is True
        assert self.indexer._is_integer_type("NUMBER_OF") is True
        assert self.indexer._is_integer_type("Nr_Of") is True

    def test_is_integer_type_with_partial_matches(self):
        """Test that partial matches work correctly."""
        # These should match because they contain integer indicators
        assert self.indexer._is_integer_type("integer_value") is True
        assert self.indexer._is_integer_type("count_items") is True
        assert self.indexer._is_integer_type("quantity_available") is True
        
        # These should not match
        assert self.indexer._is_integer_type("floating_point") is False
        assert self.indexer._is_integer_type("description") is False
