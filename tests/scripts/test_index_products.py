import unittest
import uuid
from datetime import datetime
from unittest.mock import MagicMock, patch

import pytest

from holmatro_customer_portal.database.enums import LanguageEnum
from holmatro_customer_portal.database.models import (
    Attribute,
    AttributeTranslation,
    Category,
    CategoryAssociation,
    CategoryTranslation,
    Language,
    Product,
    ProductNameTranslation,
)
from holmatro_customer_portal.schemas.opensearch_schemas import FilterableAttribute, ProductDocument
from holmatro_customer_portal.scripts.index_products import ProductIndexer
from tests.factories import CategoryFactory, CategoryTranslationFactory, FilterFactory, LanguageFactory, ProductFactory
from tests.fixtures.database import session


class TestIndexProducts(unittest.TestCase):

    @pytest.fixture(autouse=True)
    def _pass_fixtures(self, session):
        self.session = session

    def setUp(self):
        # Create real Language objects for translations
        self.language_en = LanguageFactory.build(language_code="en")
        self.language_nl = LanguageFactory.build(language_code="nl")
        self.session.add_all([self.language_en, self.language_nl])
        self.session.commit()

        # Mock Product and related models
        self.mock_product_name_en = MagicMock(spec=ProductNameTranslation)
        self.mock_product_name_en.language = self.language_en
        self.mock_product_name_en.value = "Test Product EN"

        self.mock_product_name_nl = MagicMock(spec=ProductNameTranslation)
        self.mock_product_name_nl.language = self.language_nl
        self.mock_product_name_nl.value = "Test Product NL"

        self.mock_master_product_name_en = MagicMock(spec=ProductNameTranslation)
        self.mock_master_product_name_en.language = self.language_en
        self.mock_master_product_name_en.value = "Master Product EN"

        # Mock Category and CategoryTranslation
        self.mock_category_translation_en = MagicMock()
        self.mock_category_translation_en.language = self.language_en
        self.mock_category_translation_en.name = "Category EN"

        self.mock_category_translation_nl = MagicMock()
        self.mock_category_translation_nl.language = self.language_nl
        self.mock_category_translation_nl.name = "Category NL"

        self.mock_category = MagicMock()
        self.mock_category.translations = [self.mock_category_translation_en, self.mock_category_translation_nl]
        self.mock_category.category_id = "CAT001"

        # Mock CategoryAssociation
        self.mock_category_association = MagicMock()
        self.mock_category_association.category = self.mock_category

        self.mock_product = MagicMock(spec=Product)
        self.mock_product.id = uuid.uuid4()
        self.mock_product.product_id = 1
        self.mock_product.umid = uuid.uuid4()
        self.mock_product.article_number = "ART001"
        self.mock_product.product_names = [self.mock_product_name_en, self.mock_product_name_nl]
        self.mock_product.master_product_names = [self.mock_master_product_name_en]
        self.mock_product.categories = [self.mock_category_association]
        self.mock_product.last_modified = "2023-01-01T10:00:00"
        self.mock_product.attributes = []  # Initialize attributes list

    def test_map_product_to_document(self):
        """Test mapping a product to a ProductDocument, including filterable attributes."""
        # Setup filterable attributes in the database
        filter_attr_id_1 = 101
        filter_attr_id_2 = 102
        filter_attr_id_3 = 103  # Not a filterable attribute

        self.session.add_all(
            [
                FilterFactory.build(attribute_id=filter_attr_id_1),
                FilterFactory.build(attribute_id=filter_attr_id_2),
            ]
        )
        self.session.commit()

        # Create mock attributes for the product
        mock_attribute_1 = MagicMock(spec=Attribute)
        mock_attribute_1.attribute_id = filter_attr_id_1
        mock_attribute_1.content = "Value String"
        mock_attribute_1_trans = MagicMock(spec=AttributeTranslation)
        mock_attribute_1_trans.language = self.language_en
        mock_attribute_1_trans.name = "Attribute One"
        mock_attribute_1.translations = [mock_attribute_1_trans]

        mock_attribute_2 = MagicMock(spec=Attribute)
        mock_attribute_2.attribute_id = filter_attr_id_2
        mock_attribute_2.content = "123.45"
        mock_attribute_2_trans = MagicMock(spec=AttributeTranslation)
        mock_attribute_2_trans.language = self.language_en
        mock_attribute_2_trans.name = "Attribute Two"
        mock_attribute_2.translations = [mock_attribute_2_trans]

        mock_attribute_3 = MagicMock(spec=Attribute)
        mock_attribute_3.attribute_id = filter_attr_id_3  # Not in Filter table
        mock_attribute_3.content = "Ignored Value"
        mock_attribute_3_trans = MagicMock(spec=AttributeTranslation)
        mock_attribute_3_trans.language = self.language_en
        mock_attribute_3_trans.name = "Attribute Three"
        mock_attribute_3.translations = [mock_attribute_3_trans]

        self.mock_product.attributes = [mock_attribute_1, mock_attribute_2, mock_attribute_3]

        # Create a mock OpenSearchClient for the indexer
        mock_opensearch_client = MagicMock()
        indexer = ProductIndexer(opensearch_client=mock_opensearch_client)
        doc = indexer._map_product_to_document(self.mock_product, self.session)  # Pass session here

        self.assertIsInstance(doc, ProductDocument)
        self.assertEqual(doc.id, self.mock_product.id)
        self.assertEqual(doc.product_id, 1)
        self.assertEqual(doc.umid, self.mock_product.umid)
        self.assertEqual(doc.article_number, "ART001")
        self.assertEqual(len(doc.product_names), 2)
        self.assertEqual(doc.product_names[0].language_code, "en")
        self.assertEqual(doc.product_names[0].value, "Test Product EN")
        self.assertEqual(len(doc.master_product_names), 1)
        self.assertEqual(doc.master_product_names[0].language_code, "en")
        self.assertEqual(doc.master_product_names[0].value, "Master Product EN")
        self.assertEqual(len(doc.category_names), 2)
        self.assertEqual(doc.category_names[0].language_code, "en")
        self.assertEqual(doc.category_names[0].value, "Category EN")
        self.assertEqual(doc.category_names[1].language_code, "nl")
        self.assertEqual(doc.category_names[1].value, "Category NL")
        self.assertEqual(len(doc.category_ids), 1)
        self.assertEqual(doc.category_ids[0], "CAT001")
        self.assertEqual(doc.last_modified, datetime(2023, 1, 1, 10, 0, 0))

        # Assert filterable_attributes
        self.assertEqual(len(doc.filterable_attributes), 2)

        # Check attribute 1 (string value)
        attr1 = next((fa for fa in doc.filterable_attributes if fa.attribute_id == filter_attr_id_1), None)
        self.assertIsNotNone(attr1)
        self.assertEqual(attr1.attribute_name, "Attribute One")
        self.assertEqual(attr1.value_string, "Value String")
        self.assertIsNone(attr1.value_numeric)

        # Check attribute 2 (numeric value)
        attr2 = next((fa for fa in doc.filterable_attributes if fa.attribute_id == filter_attr_id_2), None)
        self.assertIsNotNone(attr2)
        self.assertEqual(attr2.attribute_name, "Attribute Two")
        self.assertIsNone(attr2.value_string)
        self.assertEqual(attr2.value_numeric, 123.45)

        # Ensure attribute 3 (non-filterable) is not included
        attr3 = next((fa for fa in doc.filterable_attributes if fa.attribute_id == filter_attr_id_3), None)
        self.assertIsNone(attr3)

    @patch("holmatro_customer_portal.scripts.index_products.OpenSearchClient")
    @patch("holmatro_customer_portal.scripts.index_products.PRODUCT_INDEX_MAPPINGS", {})
    def test_index_products_success(self, mock_opensearch_client_cls):
        # Mock OpenSearchClient instance
        mock_opensearch_client = MagicMock()
        mock_opensearch_client_cls.return_value = mock_opensearch_client
        mock_opensearch_client.bulk_index.return_value = (1, [])
        mock_opensearch_client.client.indices.exists.return_value = False  # Force full reindex

        product = ProductFactory.build(product_id=1, article_number="ART001", deleted_at=None, dirty=True)
        self.session.add(product)
        self.session.commit()

        # Use ProductIndexer instance
        indexer = ProductIndexer(opensearch_client=mock_opensearch_client)
        indexer.index_products(self.session, force_full_reindex=True)

        mock_opensearch_client.create_index.assert_called_once_with({})
        mock_opensearch_client.bulk_index.assert_called_once()

    @patch("holmatro_customer_portal.scripts.index_products.OpenSearchClient")
    @patch("holmatro_customer_portal.scripts.index_products.PRODUCT_INDEX_MAPPINGS", {})
    def test_index_products_create_index_failure(self, mock_opensearch_client_cls):
        mock_opensearch_client = MagicMock()
        mock_opensearch_client_cls.return_value = mock_opensearch_client
        mock_opensearch_client.create_index.side_effect = Exception("Index creation failed")
        product = ProductFactory.build(product_id=1, article_number="ART001", deleted_at=None)
        self.session.add(product)
        self.session.commit()

        # Use ProductIndexer instance
        indexer = ProductIndexer(opensearch_client=mock_opensearch_client)

        with self.assertRaises(Exception) as context:
            indexer.index_products(self.session, force_full_reindex=True)

        mock_opensearch_client.create_index.assert_called_once()
        mock_opensearch_client.bulk_index.assert_not_called()  # Should not attempt bulk index

    @patch("holmatro_customer_portal.scripts.index_products.OpenSearchClient")
    @patch("holmatro_customer_portal.scripts.index_products.PRODUCT_INDEX_MAPPINGS", {})
    def test_index_products_db_query_failure(self, mock_opensearch_client_cls):
        mock_opensearch_client = MagicMock()
        mock_opensearch_client_cls.return_value = mock_opensearch_client
        mock_opensearch_client.bulk_index.return_value = (1, [])

        mock_db_session = MagicMock()
        mock_db_session.query.side_effect = Exception("DB query failed")

        with self.assertRaises(Exception) as context:
            indexer = ProductIndexer(opensearch_client=mock_opensearch_client)
            indexer.index_products(mock_db_session, force_full_reindex=True)

        mock_opensearch_client.delete_index.assert_not_called()
        mock_opensearch_client.create_index.assert_not_called()
        mock_db_session.query.assert_called_once()
        mock_opensearch_client.bulk_index.assert_not_called()  # Bulk index should not be called if query fails

    @patch("holmatro_customer_portal.scripts.index_products.OpenSearchClient")
    @patch("holmatro_customer_portal.scripts.index_products.PRODUCT_INDEX_MAPPINGS", {})
    def test_index_products_bulk_index_failure(self, mock_opensearch_client_cls):
        mock_opensearch_client = MagicMock()
        mock_opensearch_client_cls.return_value = mock_opensearch_client
        mock_opensearch_client.bulk_index.side_effect = Exception("Bulk index failed")
        mock_opensearch_client.client.indices.exists.return_value = False

        product = ProductFactory.build(product_id=1, article_number="ART001", deleted_at=None)
        self.session.add(product)
        self.session.commit()

        # The function should raise the exception since bulk_index fails
        with self.assertRaises(Exception) as context:
            indexer = ProductIndexer(opensearch_client=mock_opensearch_client)
            indexer.index_products(self.session, force_full_reindex=True)

        self.assertEqual(str(context.exception), "Bulk index failed")
        mock_opensearch_client.create_index.assert_called_once()
        mock_opensearch_client.bulk_index.assert_called_once()

    @patch("holmatro_customer_portal.scripts.index_products.OpenSearchClient")
    def test_index_products_excludes_deleted_products_with_real_session(self, mock_opensearch_client_cls):
        # Create two products - one normal, one deleted
        product_1 = ProductFactory.build(product_id=1, article_number="ART001", deleted_at=None)  # Not deleted
        product_2 = ProductFactory.build(product_id=2, article_number="ART002", deleted_at=datetime.now())  # Deleted

        self.session.add_all([product_1, product_2])
        self.session.commit()

        # Mock OpenSearchClient instance
        mock_opensearch_client = MagicMock()
        mock_opensearch_client_cls.return_value = mock_opensearch_client
        mock_opensearch_client.bulk_index.return_value = (1, [])
        mock_opensearch_client.client.indices.exists.return_value = False  # Index doesn't exist

        # Use ProductIndexer instance
        indexer = ProductIndexer(opensearch_client=mock_opensearch_client)
        indexer.index_products(self.session, force_full_reindex=True)

        # Verify bulk_index was called once
        mock_opensearch_client.bulk_index.assert_called_once()

        # Get the documents that were passed to bulk_index
        call_args = mock_opensearch_client.bulk_index.call_args
        documents = call_args[0][0]

        # Should only have 1 document (the non-deleted product)
        self.assertEqual(len(documents), 1)
        self.assertEqual(documents[0].product_id, 1)
        self.assertEqual(documents[0].article_number, "ART001")

    @patch("holmatro_customer_portal.scripts.index_products.OpenSearchClient")
    def test_delta_update_with_dirty_products(self, mock_opensearch_client_cls):
        """Test delta update functionality with dirty active and deleted products."""
        # Create products with different states
        active_dirty_product = ProductFactory.build(product_id=1, article_number="ART001", deleted_at=None, dirty=True)
        active_clean_product = ProductFactory.build(product_id=2, article_number="ART002", deleted_at=None, dirty=False)
        deleted_dirty_product = ProductFactory.build(
            product_id=3, article_number="ART003", deleted_at=datetime.now(), dirty=True
        )
        deleted_clean_product = ProductFactory.build(
            product_id=4, article_number="ART004", deleted_at=datetime.now(), dirty=False
        )

        self.session.add_all([active_dirty_product, active_clean_product, deleted_dirty_product, deleted_clean_product])
        self.session.commit()

        # Mock OpenSearchClient instance
        mock_opensearch_client = MagicMock()
        mock_opensearch_client_cls.return_value = mock_opensearch_client
        mock_opensearch_client.delta_update.return_value = {"upserted": (1, []), "deleted": (1, [])}

        # Use ProductIndexer instance for delta update (not full reindex)
        indexer = ProductIndexer(opensearch_client=mock_opensearch_client)
        indexer.index_products(self.session, force_full_reindex=False)

        # Verify delta_update was called
        mock_opensearch_client.delta_update.assert_called_once()

        # Check the arguments passed to delta_update
        call_args = mock_opensearch_client.delta_update.call_args
        upsert_documents, delete_ids = call_args[0]

        # Should have 1 document to upsert (active dirty product)
        self.assertEqual(len(upsert_documents), 1)
        self.assertEqual(upsert_documents[0].product_id, 1)

        # Should have 1 ID to delete (deleted dirty product)
        self.assertEqual(len(delete_ids), 1)
        self.assertEqual(delete_ids[0], str(deleted_dirty_product.id))

        # Verify products were marked as clean
        self.session.refresh(active_dirty_product)
        self.session.refresh(deleted_dirty_product)
        self.assertFalse(active_dirty_product.dirty)
        self.assertFalse(deleted_dirty_product.dirty)

    def test_map_product_to_document_with_category_association(self):
        """Test mapping a product with CategoryAssociation to ProductDocument."""
        # Create category with translations
        category = CategoryFactory.build(category_id="CAT001")
        self.session.add(category)
        self.session.commit()

        category_translation_en = CategoryTranslationFactory.build(
            category_id=category.id, language_id=self.language_en.id, name="Test Category EN"
        )
        category_translation_nl = CategoryTranslationFactory.build(
            category_id=category.id, language_id=self.language_nl.id, name="Test Category NL"
        )
        self.session.add_all([category_translation_en, category_translation_nl])
        self.session.commit()

        # Create product with CategoryAssociation
        product = ProductFactory.build(product_id=1, article_number="ART001")
        self.session.add(product)
        self.session.commit()

        # Create CategoryAssociation
        category_association = CategoryAssociation(category_id=category.id, product_id=product.id, product_sort_order=1)
        self.session.add(category_association)
        self.session.commit()

        # Refresh to get the relationships loaded
        self.session.refresh(product)

        # Test the mapping
        mock_opensearch_client = MagicMock()
        indexer = ProductIndexer(opensearch_client=mock_opensearch_client)
        doc = indexer._map_product_to_document(product, self.session)

        # Verify the document was created successfully
        self.assertIsInstance(doc, ProductDocument)
        self.assertEqual(doc.id, product.id)
        self.assertEqual(doc.product_id, 1)
        self.assertEqual(doc.article_number, "ART001")

        # Verify category data is correctly mapped
        self.assertEqual(len(doc.category_names), 2)
        category_name_values = [cn.value for cn in doc.category_names]
        self.assertIn("Test Category EN", category_name_values)
        self.assertIn("Test Category NL", category_name_values)

        self.assertEqual(len(doc.category_ids), 1)
        self.assertEqual(doc.category_ids[0], category.category_id)

    def test_get_attribute_ids(self):
        """Test retrieving unique attribute IDs from the Filter table."""

        # Create some Filter objects with different attribute_ids
        filter1 = FilterFactory.build(attribute_id=101)
        filter2 = FilterFactory.build(attribute_id=102)
        filter3 = FilterFactory.build(attribute_id=101)  # Duplicate attribute_id
        filter4 = FilterFactory.build(attribute_id=103)

        self.session.add_all([filter1, filter2, filter3, filter4])
        self.session.commit()

        # Call the static method
        attribute_ids = ProductIndexer._get_attribute_ids(self.session)

        # Assert that the returned list contains unique attribute_ids
        expected_ids = [101, 102, 103]
        self.assertCountEqual(attribute_ids, expected_ids)
        self.assertEqual(len(attribute_ids), len(set(attribute_ids)))  # Ensure uniqueness
