from unittest.mock import MagicMock, <PERSON><PERSON>

import pytest

from holmatro_customer_portal.database.enums import DBFilterType, LanguageEnum
from holmatro_customer_portal.database.models import DBFilter
from holmatro_customer_portal.mappers.opensearch_response_mapper import OpenSearchResponseMapper
from holmatro_customer_portal.schemas.opensearch_schemas import OpenSearchSearchResponse
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import FilterAttribute


class TestOpenSearchResponseMapper:
    """Unit tests for OpenSearchResponseMapper methods that create FilterAttribute objects."""

    @pytest.fixture
    def mock_filter(self):
        mock_filter = MagicMock(spec=DBFilter)
        mock_filter.filter_type = DBFilterType.CHECKBOX
        mock_filter.show_nr_of_results = True
        return mock_filter

    @pytest.fixture
    def mapper(self):
        """Create a mapper instance with mocked dependencies."""
        mock_session = Mock()
        mapper = OpenSearchResponseMapper(session=mock_session)
        # Replace the real service with a mock for testing
        mapper.attribute_translation_service = Mock()
        return mapper

    def test_extract_filter_options_from_bucket_english_string_attributes(self, mapper, mock_filter):
        """Test that string attributes for English users create FilterAttribute with both title and display_name."""
        # Arrange
        attribute_bucket = {
            "key": 100,
            "values_string": {
                "buckets": [
                    {"key": "red", "doc_count": 5},
                    {"key": "blue", "doc_count": 3},
                ]
            },
        }
        user_language_code = LanguageEnum.ENGLISH.value

        # Act
        comprehensive_translations = {}  # Empty for English
        result = mapper._extract_filter_options_from_bucket(
            attribute_bucket, mock_filter, user_language_code, comprehensive_translations
        )

        # Assert
        assert len(result) == 2

        # Check first FilterAttribute
        assert isinstance(result[0], FilterAttribute)
        assert result[0].title == "red"
        assert result[0].display_name == "red"  # Same as title for English
        assert result[0].is_selected is False

        # Check second FilterAttribute
        assert isinstance(result[1], FilterAttribute)
        assert result[1].title == "blue"
        assert result[1].display_name == "blue"  # Same as title for English
        assert result[1].is_selected is False

    def test_extract_filter_options_from_bucket_non_english_string_attributes(self, mapper, mock_filter):
        """Test that string attributes for non-English users use translations for display_name."""
        # Arrange
        attribute_bucket = {
            "key": 100,
            "values_string": {
                "buckets": [
                    {"key": "red", "doc_count": 5},
                    {"key": "blue", "doc_count": 3},
                ]
            },
        }
        user_language_code = "nl"

        # Mock the translation service to return Dutch translations
        comprehensive_translations = {
            100: {
                "title": "Kleur",
                "unit": None,
                "value_translations": {
                    "red": "rood",
                    "blue": "blauw",
                },
            }
        }

        # Act
        result = mapper._extract_filter_options_from_bucket(
            attribute_bucket, mock_filter, user_language_code, comprehensive_translations
        )

        # Assert
        assert len(result) == 2

        # Check first FilterAttribute
        assert isinstance(result[0], FilterAttribute)
        assert result[0].title == "red"  # Original English value
        assert result[0].display_name == "rood"  # Translated Dutch value
        assert result[0].is_selected is False

        # Check second FilterAttribute
        assert isinstance(result[1], FilterAttribute)
        assert result[1].title == "blue"  # Original English value
        assert result[1].display_name == "blauw"  # Translated Dutch value
        assert result[1].is_selected is False

        # Note: With the new comprehensive approach, translations are pre-fetched
        # so we don't need to verify individual translation service calls

    def test_extract_filter_options_from_bucket_numeric_attributes(self, mapper):
        """Test that numeric attributes create FilterAttribute objects with min/max values."""
        # Arrange
        attribute_bucket = {
            "key": 200,
            "values_numeric_stats": {
                "count": 10,
                "min": 5.5,
                "max": 25.0,
            },
        }
        user_language_code = "en"

        # Create a new mock with slider filter type for numeric attributes
        mock_filter = MagicMock(spec=DBFilter)
        mock_filter.filter_type = DBFilterType.SLIDER
        mock_filter.show_nr_of_results = True

        # Act
        comprehensive_translations = {}  # Not used for numeric attributes
        result = mapper._extract_filter_options_from_bucket(
            attribute_bucket, mock_filter, user_language_code, comprehensive_translations
        )

        # Assert
        assert len(result) == 2

        # Check min value FilterAttribute
        assert isinstance(result[0], FilterAttribute)
        assert result[0].title == "5.5"
        assert result[0].display_name == "5.5"  # Same for numeric values
        assert result[0].is_selected is False

        # Check max value FilterAttribute
        assert isinstance(result[1], FilterAttribute)
        assert result[1].title == "25.0"
        assert result[1].display_name == "25.0"  # Same for numeric values
        assert result[1].is_selected is False

    def test_extract_filter_options_from_bucket_no_values(self, mapper, mock_filter):
        """Test that empty buckets return empty list."""
        # Arrange
        attribute_bucket = {"key": 300}
        user_language_code = "en"

        # Act
        comprehensive_translations = {}  # Empty bucket case
        result = mapper._extract_filter_options_from_bucket(
            attribute_bucket, mock_filter, user_language_code, comprehensive_translations
        )

        # Assert
        assert result == []

    def test_map_attribute_aggregation_to_filter_attributes_string_values(self, mapper, mock_filter):
        """Test mapping aggregation results with string values to FilterAttribute objects."""
        # Arrange - structure from OpenSearchClient.get_attribute_aggregation()
        aggregation_results = OpenSearchSearchResponse(
            hits=None,
            aggregations={
                "filterable_attributes": {
                    "filter_by_attribute_id": {
                        "values_string": {
                            "buckets": [
                                {"key": "red", "doc_count": 12},
                                {"key": "blue", "doc_count": 8},
                            ]
                        },
                        "values_numeric_stats": {"count": 0, "min": None, "max": None},
                    }
                }
            },
        )
        attribute_id = 100
        user_language_code = LanguageEnum.ENGLISH.value

        # Act
        result = mapper.map_attribute_aggregation_to_filter_attributes(
            attribute_id, aggregation_results, mock_filter, user_language_code
        )

        # Assert
        assert len(result) == 2
        assert isinstance(result[0], FilterAttribute)
        assert result[0].title == "red"
        assert result[0].display_name == "red"
        assert result[0].is_selected is False
        assert isinstance(result[1], FilterAttribute)
        assert result[1].title == "blue"
        assert result[1].display_name == "blue"
        assert result[1].is_selected is False

    def test_map_attribute_aggregation_to_filter_attributes_numeric_values(self, mapper):
        """Test mapping aggregation results with numeric values to FilterAttribute objects."""
        # Arrange - structure from OpenSearchClient.get_attribute_aggregation()
        aggregation_results = OpenSearchSearchResponse(
            hits=None,
            aggregations={
                "filterable_attributes": {
                    "filter_by_attribute_id": {
                        "values_string": {"buckets": []},
                        "values_numeric_stats": {"count": 15, "min": 10.5, "max": 50.0},
                    }
                }
            },
        )
        attribute_id = 200
        mock_filter = Mock(spec=DBFilter)
        mock_filter.filter_type = DBFilterType.SLIDER
        user_language_code = LanguageEnum.ENGLISH.value

        # Act
        result = mapper.map_attribute_aggregation_to_filter_attributes(
            attribute_id, aggregation_results, mock_filter, user_language_code
        )

        # Assert
        assert len(result) == 2  # min and max values
        assert isinstance(result[0], FilterAttribute)
        assert result[0].title == "10.5"
        assert result[0].display_name == "10.5"
        assert result[0].is_selected is False
        assert isinstance(result[1], FilterAttribute)
        assert result[1].title == "50.0"
        assert result[1].display_name == "50.0"
        assert result[1].is_selected is False

    def test_map_attribute_aggregation_to_filter_attributes_empty_data(self, mapper, mock_filter):
        """Test mapping aggregation results with no data returns empty list."""
        # Arrange
        aggregation_results = OpenSearchSearchResponse(
            hits=None,
            aggregations={
                "filterable_attributes": {
                    "filter_by_attribute_id": {
                        "values_string": {"buckets": []},
                        "values_numeric_stats": {"count": 0, "min": None, "max": None},
                    }
                }
            },
        )
        attribute_id = 300
        user_language_code = LanguageEnum.ENGLISH.value

        # Act
        result = mapper.map_attribute_aggregation_to_filter_attributes(
            attribute_id, aggregation_results, mock_filter, user_language_code
        )

        # Assert
        assert result == []

    def test_map_attribute_aggregation_to_filter_attributes_none_input(self, mapper, mock_filter):
        """Test mapping None aggregation results returns empty list."""
        # Arrange
        attribute_id = 400
        filter_type = DBFilterType.CHECKBOX
        user_language_code = LanguageEnum.ENGLISH.value

        # Act
        result = mapper.map_attribute_aggregation_to_filter_attributes(
            attribute_id, OpenSearchSearchResponse(hits=None, aggregations=None), mock_filter, user_language_code
        )

        # Assert
        assert result == []

    def test_extract_filter_options_from_bucket_translation_fallback(self, mapper, mock_filter):
        """Test that missing translations fall back to original values."""
        # Arrange
        attribute_bucket = {
            "key": 100,
            "values_string": {
                "buckets": [
                    {"key": "red", "doc_count": 5},
                    {"key": "unknown_color", "doc_count": 1},
                ]
            },
        }
        user_language_code = "nl"

        # Mock translation service to return partial translations
        comprehensive_translations = {
            100: {
                "title": "Kleur",
                "unit": None,
                "value_translations": {
                    "red": "rood",
                    # "unknown_color" has no translation
                },
            }
        }

        # Act
        result = mapper._extract_filter_options_from_bucket(
            attribute_bucket, mock_filter, user_language_code, comprehensive_translations
        )

        # Assert
        assert len(result) == 2

        # Check translated value
        assert result[0].title == "red"
        assert result[0].display_name == "rood"

        # Check fallback value (no translation available)
        assert result[1].title == "unknown_color"
        assert result[1].display_name == "unknown_color"  # Falls back to original
