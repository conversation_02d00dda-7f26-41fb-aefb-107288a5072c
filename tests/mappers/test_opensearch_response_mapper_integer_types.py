"""Test integer type detection in OpenSearchResponseMapper."""

import pytest
from holmatro_customer_portal.mappers.opensearch_response_mapper import OpenSearchResponseMapper


class TestOpenSearchResponseMapperIntegerTypes:
    """Test the integer type detection logic in OpenSearchResponseMapper."""

    def test_is_integer_type_with_integer_keywords(self):
        """Test that attributes with integer-related keywords are detected as integers."""
        # Test various integer type indicators
        assert OpenSearchResponseMapper._is_integer_type("integer") is True
        assert OpenSearchResponseMapper._is_integer_type("INTEGER") is True
        assert OpenSearchResponseMapper._is_integer_type("int") is True
        assert OpenSearchResponseMapper._is_integer_type("INT") is True
        assert OpenSearchResponseMapper._is_integer_type("whole") is True
        assert OpenSearchResponseMapper._is_integer_type("count") is True
        assert OpenSearchResponseMapper._is_integer_type("quantity") is True
        assert OpenSearchResponseMapper._is_integer_type("number_of") is True
        assert OpenSearchResponseMapper._is_integer_type("nr_of") is True

    def test_is_integer_type_with_float_keywords(self):
        """Test that attributes with float-related keywords are not detected as integers."""
        # Test various non-integer type indicators
        assert OpenSearchResponseMapper._is_integer_type("float") is False
        assert OpenSearchResponseMapper._is_integer_type("decimal") is False
        assert OpenSearchResponseMapper._is_integer_type("double") is False
        assert OpenSearchResponseMapper._is_integer_type("numeric") is False
        assert OpenSearchResponseMapper._is_integer_type("text") is False
        assert OpenSearchResponseMapper._is_integer_type("string") is False

    def test_is_integer_type_with_none_or_empty(self):
        """Test that None or empty strings return False."""
        assert OpenSearchResponseMapper._is_integer_type(None) is False
        assert OpenSearchResponseMapper._is_integer_type("") is False
        assert OpenSearchResponseMapper._is_integer_type("   ") is False

    def test_is_integer_type_case_insensitive(self):
        """Test that the detection is case-insensitive."""
        assert OpenSearchResponseMapper._is_integer_type("Integer") is True
        assert OpenSearchResponseMapper._is_integer_type("Count") is True
        assert OpenSearchResponseMapper._is_integer_type("NUMBER_OF") is True
        assert OpenSearchResponseMapper._is_integer_type("Nr_Of") is True

    def test_is_integer_type_with_partial_matches(self):
        """Test that partial matches work correctly."""
        # These should match because they contain integer indicators
        assert OpenSearchResponseMapper._is_integer_type("integer_value") is True
        assert OpenSearchResponseMapper._is_integer_type("count_items") is True
        assert OpenSearchResponseMapper._is_integer_type("quantity_available") is True

        # These should not match
        assert OpenSearchResponseMapper._is_integer_type("floating_point") is False
        assert OpenSearchResponseMapper._is_integer_type("description") is False
