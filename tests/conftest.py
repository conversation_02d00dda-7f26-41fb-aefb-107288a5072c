from unittest.mock import MagicMock

import boto3
import dotenv
import pytest
from moto import mock_aws

from holmatro_customer_portal import get_project_root
from holmatro_customer_portal.domain.repositories.catalog_repository import CatalogRepository
from tests.fixtures.database import session  # noqa

result = dotenv.load_dotenv(dotenv_path=f"{get_project_root()}/.env.test", override=True)


@pytest.fixture(scope="function")
def s3_client():
    with mock_aws():
        yield boto3.client("s3", region_name="us-east-1")


@pytest.fixture
def catalog_repo_mock() -> MagicMock:
    """Provides a fresh MagicMock for the CatalogRepository for each test."""
    mock = MagicMock(spec=CatalogRepository)
    return mock
