import tempfile
from pathlib import Path
from unittest.mock import patch

from holmatro_customer_portal.scripts.synonym_processor import SynonymProcessor


class TestSynonymProcessor:

    def setup_method(self):
        """Set up test fixtures."""
        self.processor = SynonymProcessor()

    @staticmethod
    def _create_temp_csv_file(csv_content: str) -> str:
        """
        Create a temporary CSV file with the given content.

        Args:
            csv_content: The CSV content to write to the file

        Returns:
            Path to the temporary file
        """
        temp_file = tempfile.NamedTemporaryFile(mode="w", suffix=".csv", delete=False)
        temp_file.write(csv_content)
        temp_file.close()
        return temp_file.name

    def _test_with_temp_csv(self, csv_content: str, test_func):
        """
        Helper method to create a temporary CSV file, run a test function, and clean up.

        Args:
            csv_content: The CSV content to write to the file
            test_func: Function to call with the temporary file path
        """
        temp_file_path = self._create_temp_csv_file(csv_content)
        try:
            return test_func(temp_file_path)
        finally:
            Path(temp_file_path).unlink()

    def _create_csv_content(self, rows: list[dict]) -> str:
        """
        Create CSV content from a list of row dictionaries.

        Args:
            rows: List of dictionaries with keys: volgorde, zoekwoord, redirect

        Returns:
            CSV content string with header
        """
        header = "volgorde;zoekwoord;eigenschapId;redirect (art.no);categorieid;RedirectTypeId"
        csv_lines = [header]

        for row in rows:
            line = f"{row.get('volgorde', '1')};{row.get('zoekwoord', '')};;" f"{row.get('redirect', '')};;0"
            csv_lines.append(line)

        return "\n".join(csv_lines)

    def test_init(self):
        """Test SynonymProcessor initialization."""
        processor = SynonymProcessor()
        assert processor.synonym_lookup == {}

    def test_load_synonyms_from_csv_file_not_found(self):
        """Test loading synonyms when CSV file doesn't exist."""
        result = self.processor.load_synonyms_from_csv("nonexistent.csv")

        assert result == {}
        assert self.processor.synonym_lookup == {}

    def test_load_synonyms_from_csv_valid_file(self):
        """Test loading synonyms from a valid CSV file."""
        rows = [
            {"zoekwoord": "RC53", "redirect": "100112213"},
            {"zoekwoord": "RC55", "redirect": "100112215"},
            {"zoekwoord": "hydraulic cylinder", "redirect": "100123456"},
            {"zoekwoord": "pump", "redirect": "100234567"},
        ]
        csv_content = self._create_csv_content(rows)

        def test_logic(temp_file_path):
            result = self.processor.load_synonyms_from_csv(temp_file_path)

            # Verify return value
            expected_result = {
                "100.112.213": {"rc53"},
                "100.112.215": {"rc55"},
                "100.123.456": {"hydraulic cylinder"},
                "100.234.567": {"pump"},
            }
            assert result == expected_result

            # Verify internal state
            assert self.processor.synonym_lookup == expected_result

        self._test_with_temp_csv(csv_content, test_logic)

    def test_load_synonyms_from_csv_multiple_synonyms_same_article(self):
        """Test loading multiple synonyms for the same article number."""
        rows = [
            {"zoekwoord": "RC53", "redirect": "100112213"},
            {"zoekwoord": "rescue cutter", "redirect": "100112213"},
            {"zoekwoord": "hydraulic cutter", "redirect": "100112213"},
        ]
        csv_content = self._create_csv_content(rows)

        def test_logic(temp_file_path):
            result = self.processor.load_synonyms_from_csv(temp_file_path)

            expected_result = {"100.112.213": {"rc53", "rescue cutter", "hydraulic cutter"}}
            assert result == expected_result

        self._test_with_temp_csv(csv_content, test_logic)

    def test_load_synonyms_from_csv_empty_fields(self):
        """Test loading synonyms with empty zoekwoord or article number fields."""
        rows = [
            {"zoekwoord": "", "redirect": "100112213"},  # Empty search term
            {"zoekwoord": "RC55", "redirect": ""},  # Empty article number
            {"zoekwoord": "valid term", "redirect": "100123456"},  # Valid row
        ]
        csv_content = self._create_csv_content(rows)

        def test_logic(temp_file_path):
            result = self.processor.load_synonyms_from_csv(temp_file_path)

            # Only the valid row should be processed
            expected_result = {"100.123.456": {"valid term"}}
            assert result == expected_result

        self._test_with_temp_csv(csv_content, test_logic)

    def test_load_synonyms_from_csv_invalid_article_numbers(self):
        """Test loading synonyms with invalid article number formats."""
        rows = [
            {"zoekwoord": "RC53", "redirect": "12345"},  # Too short
            {"zoekwoord": "RC55", "redirect": "100112215abc"},  # Contains letters
            {"zoekwoord": "valid", "redirect": "100123456"},  # Valid
        ]
        csv_content = self._create_csv_content(rows)

        def test_logic(temp_file_path):
            result = self.processor.load_synonyms_from_csv(temp_file_path)

            # Only the valid article number should be processed
            expected_result = {"100.123.456": {"valid"}}
            assert result == expected_result

        self._test_with_temp_csv(csv_content, test_logic)

    def test_transform_article_number_valid(self):
        """Test transforming valid article numbers."""
        # Test without dots
        result = SynonymProcessor._transform_article_number("100112213")
        assert result == "100.112.213"

        # Test with existing dots (should be cleaned and reformatted)
        result = SynonymProcessor._transform_article_number("100.112.213")
        assert result == "100.112.213"

    def test_transform_article_number_invalid(self):
        """Test transforming invalid article numbers."""
        # Too short
        result = SynonymProcessor._transform_article_number("12345")
        assert result is None

        # Too long
        result = SynonymProcessor._transform_article_number("1234567890")
        assert result is None

        # Contains letters
        result = SynonymProcessor._transform_article_number("100112abc")
        assert result is None

        # Empty string
        result = SynonymProcessor._transform_article_number("")
        assert result is None

    def test_synonym_lookup_direct_access(self):
        """Test direct access to synonym_lookup after loading."""
        rows = [{"zoekwoord": "RC53", "redirect": "100112213"}, {"zoekwoord": "rescue cutter", "redirect": "100112213"}]
        csv_content = self._create_csv_content(rows)

        def test_logic(temp_file_path):
            self.processor.load_synonyms_from_csv(temp_file_path)

            # Test direct access to synonym_lookup
            assert "100.112.213" in self.processor.synonym_lookup
            assert self.processor.synonym_lookup["100.112.213"] == {"rc53", "rescue cutter"}

            # Test non-existing article
            assert "999.999.999" not in self.processor.synonym_lookup

        self._test_with_temp_csv(csv_content, test_logic)

    def test_load_synonyms_from_csv_file_read_error(self):
        """Test handling of file read errors."""

        def test_logic(temp_file_path):
            # Mock open to raise an exception
            with patch("builtins.open", side_effect=IOError("Permission denied")):
                result = self.processor.load_synonyms_from_csv(temp_file_path)

                # Should return empty dict on error
                assert result == {}
                assert self.processor.synonym_lookup == {}

        # Create a dummy file (content doesn't matter since we're mocking the open)
        self._test_with_temp_csv("test content", test_logic)

    def test_load_synonyms_case_insensitive(self):
        """Test that synonyms are stored in lowercase."""
        rows = [
            {"zoekwoord": "RC53", "redirect": "100112213"},
            {"zoekwoord": "Hydraulic Cylinder", "redirect": "100123456"},
            {"zoekwoord": "PUMP", "redirect": "100234567"},
        ]
        csv_content = self._create_csv_content(rows)

        def test_logic(temp_file_path):
            result = self.processor.load_synonyms_from_csv(temp_file_path)

            expected_result = {"100.112.213": {"rc53"}, "100.123.456": {"hydraulic cylinder"}, "100.234.567": {"pump"}}
            assert result == expected_result

        self._test_with_temp_csv(csv_content, test_logic)

    def test_load_synonyms_whitespace_handling(self):
        """Test that whitespace is properly stripped from synonyms."""
        # Create CSV with extra whitespace manually since our helper trims it
        csv_content = """volgorde;zoekwoord;eigenschapId;redirect (art.no);categorieid;RedirectTypeId
1;  RC53  ;;  100112213  ;;0
2; hydraulic cylinder ;;100123456;;0"""

        def test_logic(temp_file_path):
            result = self.processor.load_synonyms_from_csv(temp_file_path)

            expected_result = {"100.112.213": {"rc53"}, "100.123.456": {"hydraulic cylinder"}}
            assert result == expected_result

        self._test_with_temp_csv(csv_content, test_logic)

    def test_load_synonyms_resets_previous_data(self):
        """Test that loading synonyms resets previous data."""
        # Set up initial data
        self.processor.synonym_lookup = {"999.999.999": {"old data"}}

        rows = [{"zoekwoord": "RC53", "redirect": "100112213"}]
        csv_content = self._create_csv_content(rows)

        def test_logic(temp_file_path):
            result = self.processor.load_synonyms_from_csv(temp_file_path)

            # Should only contain new data, not old data
            expected_result = {"100.112.213": {"rc53"}}
            assert result == expected_result
            assert self.processor.synonym_lookup == expected_result
            assert "999.999.999" not in self.processor.synonym_lookup

        self._test_with_temp_csv(csv_content, test_logic)
