from holmatro_customer_portal.utils.transforms import validate_and_transform_article_numbers


def test_validate_and_transform_article_numbers():
    response = [
        "myportal.holmatro.com/product/87e0019d-e1b7-4473-a49a-ed8a47354197",
        "100112213",
        "100.123.123",
    ]
    result = validate_and_transform_article_numbers(response)

    assert result == ["100.112.213", "100.123.123"]
