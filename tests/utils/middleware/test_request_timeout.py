import asyncio

from fastapi import <PERSON><PERSON><PERSON>
from fastapi.testclient import <PERSON><PERSON><PERSON>

from holmatro_customer_portal.utils.middleware.request_timeout import RequestTimeoutMiddleware

app = FastAPI()

app.add_middleware(RequestTimeoutMiddleware, timeout_seconds=1)


@app.get("/")
async def read_main():
    await asyncio.sleep(0.3)
    return {"msg": "Hello World"}


@app.get("/timeout")
async def read_main():
    await asyncio.sleep(1.1)
    return {"msg": "Hello World"}


client = TestClient(app)


def test_no_timeout():
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"msg": "Hello World"}


def test_timeout():
    response = client.get("/timeout")
    assert response.status_code == 504
    assert response.json() == {"details": "Request processing time exceeded limit"}
