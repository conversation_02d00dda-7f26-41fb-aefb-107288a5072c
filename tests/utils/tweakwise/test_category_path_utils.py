import pytest

from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.tweakwise.category_path_utils import find_category_path_in_tree
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import Category, MainCategory, SubCategory


def test_find_category_path_in_tree_found():
    """Test find_category_path_in_tree when the category is found."""
    # Create a mock category tree structure
    tree = Category(
        title="Industrial Equipment",
        category_id=10008604,
        category_path="10008604",
        children=[
            MainCategory(
                title="Cutting",
                category_id=10002838,
                category_path="10008604-10002838",
                children=[
                    SubCategory(
                        title="Cutters",
                        category_id=10006921,  # 1000 (EN) + 6921
                        category_path="10008604-10002838-10006921",
                    ),
                    SubCategory(
                        title="Accessories",
                        category_id=1000831,  # 1000 (EN) + 831
                        category_path="10008604-10002838-1000831",
                    ),
                ],
            )
        ],
    )

    # Test finding the cutters category
    result = find_category_path_in_tree(tree, "en", SyncforceCategory.CUTTERS)
    assert result == "10008604-10002838-10006921"

    # Test finding the accessories category
    result = find_category_path_in_tree(tree, "en", SyncforceCategory.CUTTING_ACCESSORIES)
    assert result == "10008604-10002838-1000831"


def test_find_category_path_in_tree_not_found():
    """Test find_category_path_in_tree when the category is not found."""
    # Create a mock category tree structure without the target category
    tree = Category(
        title="Industrial Equipment",
        category_id=10008604,
        category_path="10008604",
        children=[
            MainCategory(
                title="Cutting",
                category_id=10002838,
                category_path="10008604-10002838",
                children=[
                    SubCategory(
                        title="Cutters",
                        category_id=10006921,
                        category_path="10008604-10002838-10006921",
                    )
                ],
            )
        ],
    )

    # Test looking for a category that doesn't exist in the tree
    result = find_category_path_in_tree(tree, "en", SyncforceCategory.CYLINDERS)
    assert result is None


def test_find_category_path_in_tree_empty_tree():
    """Test find_category_path_in_tree with an empty tree."""
    # Create a mock category tree with no children
    tree = Category(title="Industrial Equipment", category_id=10008604, category_path="10008604", children=[])

    # Test looking for any category in an empty tree
    result = find_category_path_in_tree(tree, "en", SyncforceCategory.CUTTERS)
    assert result is None


def test_find_category_path_in_tree_nested_structure():
    """Test find_category_path_in_tree with deeply nested structure."""
    # Create a mock category tree with nested structure
    tree = Category(
        title="Industrial Lifting",
        category_id=20007411,
        category_path="20007411",
        children=[
            MainCategory(
                title="Pumps",
                category_id=20001017,
                category_path="20007411-20001017",
                children=[
                    SubCategory(
                        title="Hand and Foot Pumps",
                        category_id=20003118,  # 2000 (NL) + 3118
                        category_path="20007411-20001017-20003118",
                    ),
                    SubCategory(
                        title="Compact Pumps",
                        category_id=20003963,  # 2000 (NL) + 3963
                        category_path="20007411-20001017-20003963",
                    ),
                ],
            )
        ],
    )

    # Test finding the nested category
    result = find_category_path_in_tree(tree, "nl", SyncforceCategory.HAND_AND_FOOT_PUMPS)
    assert result == "20007411-20001017-20003118"

    # Test finding another nested category
    result = find_category_path_in_tree(tree, "nl", SyncforceCategory.COMPACT_PUMPS)
    assert result == "20007411-20001017-20003963"


def test_find_category_path_in_tree_different_languages():
    """Test find_category_path_in_tree with different languages."""
    # Create a mock category tree structure
    tree = Category(
        title="Industrial Equipment",
        category_id=30008604,  # German (3000) + 8604
        category_path="30008604",
        children=[
            MainCategory(
                title="Schneiden",
                category_id=30002838,  # German (3000) + 2838
                category_path="30008604-30002838",
                children=[
                    SubCategory(
                        title="Schneider",
                        category_id=30006921,  # German (3000) + 6921
                        category_path="30008604-30002838-30006921",
                    )
                ],
            )
        ],
    )

    # Test finding with German language
    result = find_category_path_in_tree(tree, "de", SyncforceCategory.CUTTERS)
    assert result == "30008604-30002838-30006921"

    # Test with wrong language (should not find)
    result = find_category_path_in_tree(tree, "en", SyncforceCategory.CUTTERS)
    assert result is None
