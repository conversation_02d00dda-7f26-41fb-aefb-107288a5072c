from http import HTTPStatus

import pytest
import responses
from fastapi import HTT<PERSON>Exception
from responses import matchers

from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.tweakwise.tweakwise_client import TweakwiseClient
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import (
    Autocomplete,
    Category,
    MainCategory,
    Navigation,
    ProductAttributes,
    ProductsResponse,
    SubCategory,
    Template,
    TemplateAttribute,
)
from tests.fixtures.tweakwise_fixtures import (
    mock_tweakwise_facet_settings,
    mock_tweakwise_product_attributes,
    mock_tweakwise_response_properties,
)

tweakwise_client = TweakwiseClient()
tweakwise_key = tweakwise_client.frontend_config.get("instance_key")
frontend_url = tweakwise_client.frontend_config.get("base_url")
backend_url = tweakwise_client.backend_config.get("base_url")


@responses.activate
def test_get_products():
    mock_result = {
        "items": [
            {
                "itemno": "1",
                "title": "mock-title",
                "price": 0,
                "brand": "Holmatro",
                "url": "mock-url",
                "image": None,
                "attributes": [],
            }
        ],
        "properties": mock_tweakwise_response_properties,
        "redirects": [],
    }

    responses.get(url=f"{frontend_url}/products/{tweakwise_key}", json=mock_result)

    result = tweakwise_client.get_products()
    assert result == ProductsResponse(**mock_result)


@responses.activate
def test_get_category_tree():
    category_id = 1
    mock_response = {
        "categories": [
            {
                "title": "Holmatro Corporate website",
                "categoryid": 1,
                "categorypath": "1",
                "children": [],
            }
        ]
    }

    responses.get(
        url=f"{frontend_url}/categorytree/{tweakwise_key}",
        match=[matchers.query_param_matcher({"catid": category_id})],
        json=mock_response,
    )

    result = tweakwise_client.get_category_tree(category_id)

    assert result == Category(**mock_response["categories"][0])


@responses.activate
def test_get_category_tree_not_found():
    category_id = 1111
    responses.get(
        url=f"{frontend_url}/categorytree/{tweakwise_key}",
        match=[matchers.query_param_matcher({"catid": category_id})],
        json={},
    )

    with pytest.raises(HTTPException):
        tweakwise_client.get_category_tree(category_id)


@responses.activate
def test_get_navigation_flow():
    mock_result = {
        "facets": [{"facetsettings": mock_tweakwise_facet_settings, "attributes": []}],
        "items": [],
        "properties": mock_tweakwise_response_properties,
        "redirects": [],
    }
    responses.get(url=f"{frontend_url}/navigation/{tweakwise_key}", json=mock_result)
    result = tweakwise_client.get_navigation()

    assert result == Navigation(**mock_result)


@responses.activate
def test_get_product_by_search():
    params = {"tn_q": "chain"}
    mock_result = {
        "items": [{"itemno": "1", "title": "mock-title", "price": 0, "brand": "Holmatro"}],
        "facets": [],
        "properties": mock_tweakwise_response_properties,
        "redirects": [],
    }

    responses.get(
        url=f"{frontend_url}/navigation-search/{tweakwise_key}",
        match=[matchers.query_param_matcher(params)],
        json=mock_result,
    )

    result = tweakwise_client.get_products_by_search({"tn_q": "chain"})

    assert result == Navigation(**mock_result)


@responses.activate
def test_get_product_details():
    mock_result = {"Results": ProductAttributes(**mock_tweakwise_product_attributes)}

    responses.get(url=f"{backend_url}/product/1", json={"Results": mock_tweakwise_product_attributes})

    result = tweakwise_client.get_product_details("1")

    assert result == mock_result.get("Results")


@responses.activate
def test_get_product_details_raises():
    responses.get(
        url=f"{backend_url}/product/1",
        status=HTTPStatus.NOT_FOUND,
    )

    with pytest.raises(HTTPException):
        tweakwise_client.get_product_details("1")


@responses.activate
def test_get_templates():
    mock_result = [{"FilterTemplateId": 1, "Name": "Template", "MaxFacets": None}]

    responses.get(
        url=f"{backend_url}/filtertemplate",
        json=mock_result,
    )
    result = tweakwise_client.get_templates()

    assert result == [Template(**mock_result[0])]


@responses.activate
def test_get_template_attributes():
    mock_result = {"FilterTemplateId": 1, "AttributeId": 10, "Name": "attribute name"}

    responses.get(url=f"{backend_url}/filtertemplate/1/attribute", json=[mock_result])

    result = tweakwise_client.get_template_attributes(1)

    assert result == [TemplateAttribute(**mock_result)]


@responses.activate
def test_get_autocomplete():
    mock_result = {
        "items": [
            {
                "itemno": "10002000-1000-10",
                "title": "Pulling Chains Set PCS 01",
                "price": 0.0,
                "brand": "Holmatro",
                "url": None,
                "attributes": [],
            }
        ],
        "suggestions": [{"title": "chain adapters"}],
        "instantsearch": {"isactive": True, "searchterm": "chain"},
    }

    responses.get(
        url=f"{frontend_url}/autocomplete/{tweakwise_key}",
        match=[matchers.query_param_matcher({"tn_q": "chain", "tn_instant": True})],
        json=mock_result,
    )

    result = tweakwise_client.get_autocomplete({"tn_q": "chain"})

    assert result == Autocomplete(**mock_result)


@responses.activate
def test_get_category_path_found():
    """Test get_category_path when the category is found in the tree."""
    language = "en"
    category = SyncforceCategory.CUTTERS  # 6921
    parent_category = SyncforceCategory.INDUSTRIAL  # 8604

    # Expected parent category path: 1000 (EN) + 8604 = 10008604
    parent_category_id = 10008604

    # Mock category tree response based on actual Tweakwise API structure
    mock_tree_response = {
        "categories": [
            {
                "title": "Industrial Equipment",
                "categoryid": 10008604,
                "categorypath": "10008604",
                "children": [
                    {
                        "title": "Lifting",
                        "categoryid": 10007411,
                        "categorypath": "10008604-10007411",
                        "children": [
                            {
                                "title": "Cylinders",
                                "categoryid": 10005121,
                                "categorypath": "10008604-10007411-10005121",
                                "children": [],
                            }
                        ],
                    },
                    {
                        "title": "Cutting",
                        "categoryid": 10002838,
                        "categorypath": "10008604-10002838",
                        "children": [
                            {
                                "title": "Cutters",
                                "categoryid": 10006921,  # 1000 (EN) + 6921
                                "categorypath": "10008604-10002838-10006921",
                                "children": [],
                            },
                            {
                                "title": "Hoses",
                                "categoryid": 10009050,
                                "categorypath": "10008604-10002838-10009050",
                                "children": [],
                            },
                            {
                                "title": "Accessories",
                                "categoryid": 1000831,  # 1000 (EN) + 831
                                "categorypath": "10008604-10002838-1000831",
                                "children": [],
                            },
                        ],
                    },
                ],
            }
        ]
    }

    # Mock the API call to get_category_tree
    responses.get(
        url=f"{frontend_url}/categorytree/{tweakwise_key}",
        match=[matchers.query_param_matcher({"catid": parent_category_id})],
        json=mock_tree_response,
    )

    result = tweakwise_client.get_category_path(language, category, parent_category)

    # Should find the cutters category and return its path
    assert result == "10008604-10002838-10006921"


@responses.activate
def test_get_category_path_not_found():
    """Test get_category_path when the category is not found in the tree."""
    language = "en"
    category = SyncforceCategory.CUTTER_PUMPS  # 7474 - will be in cutting but we'll exclude it
    parent_category = SyncforceCategory.INDUSTRIAL  # 8604

    # Expected parent category path: 1000 (EN) + 8604 = 10008604
    parent_category_id = 10008604

    # Mock category tree response without the target category (exclude pumps from cutting)
    mock_tree_response = {
        "categories": [
            {
                "title": "Industrial Equipment",
                "categoryid": 10008604,
                "categorypath": "10008604",
                "children": [
                    {
                        "title": "Cutting",
                        "categoryid": 10002838,
                        "categorypath": "10008604-10002838",
                        "children": [
                            {
                                "title": "Cutters",
                                "categoryid": 10006921,
                                "categorypath": "10008604-10002838-10006921",
                                "children": [],
                            },
                            {
                                "title": "Hoses",
                                "categoryid": 10009050,
                                "categorypath": "10008604-10002838-10009050",
                                "children": [],
                            },
                            # Note: Pumps (7474) is intentionally excluded
                        ],
                    }
                ],
            }
        ]
    }

    # Mock the API call to get_category_tree
    responses.get(
        url=f"{frontend_url}/categorytree/{tweakwise_key}",
        match=[matchers.query_param_matcher({"catid": parent_category_id})],
        json=mock_tree_response,
    )

    result = tweakwise_client.get_category_path(language, category, parent_category)

    # Should return None when category is not found
    assert result is None


@responses.activate
def test_get_category_path_with_nested_categories():
    """Test get_category_path with deeply nested category structure."""
    language = "nl"  # Dutch
    category = SyncforceCategory.HAND_AND_FOOT_PUMPS  # 3118
    parent_category = SyncforceCategory.INDUSTRIAL_LIFTING  # 7411

    # Expected parent category path: 2000 (NL) + 7411 = 20007411
    parent_category_id = 20007411

    # Mock complex nested tree structure based on actual API response
    mock_tree_response = {
        "categories": [
            {
                "title": "Heffen",
                "categoryid": 20007411,
                "categorypath": "20007411",
                "children": [
                    {"title": "Cilinders", "categoryid": 20005121, "categorypath": "20007411-20005121", "children": []},
                    {
                        "title": "Pompen",
                        "categoryid": 20001017,  # 2000 (NL) + 1017
                        "categorypath": "20007411-20001017",
                        "children": [
                            {
                                "title": "Hand en Voet Pompen",
                                "categoryid": 20003118,  # 2000 (NL) + 3118
                                "categorypath": "20007411-20001017-20003118",
                                "children": [],
                            },
                            {
                                "title": "Compacte Pompen",
                                "categoryid": 20003963,  # 2000 (NL) + 3963
                                "categorypath": "20007411-20001017-20003963",
                                "children": [],
                            },
                            {
                                "title": "Elektrische en Benzine Pompen",
                                "categoryid": 2000826,  # 2000 (NL) + 826
                                "categorypath": "20007411-20001017-2000826",
                                "children": [],
                            },
                        ],
                    },
                ],
            }
        ]
    }

    # Mock the API call to get_category_tree
    responses.get(
        url=f"{frontend_url}/categorytree/{tweakwise_key}",
        match=[matchers.query_param_matcher({"catid": parent_category_id})],
        json=mock_tree_response,
    )

    result = tweakwise_client.get_category_path(language, category, parent_category)

    # Should find the nested category and return its path
    assert result == "20007411-20001017-20003118"


@responses.activate
def test_get_category_path_empty_tree():
    """Test get_category_path when the tree has no children."""
    language = "en"
    category = SyncforceCategory.CUTTERS  # 6921
    parent_category = SyncforceCategory.INDUSTRIAL  # 8604

    # Expected parent category path: 1000 (EN) + 8604 = 10008604
    parent_category_id = 10008604

    # Mock empty tree response
    mock_tree_response = {
        "categories": [
            {
                "title": "Industrial Equipment",
                "categoryid": 10008604,
                "categorypath": "10008604",
                "children": [],  # Empty children
            }
        ]
    }

    # Mock the API call to get_category_tree
    responses.get(
        url=f"{frontend_url}/categorytree/{tweakwise_key}",
        match=[matchers.query_param_matcher({"catid": parent_category_id})],
        json=mock_tree_response,
    )

    result = tweakwise_client.get_category_path(language, category, parent_category)

    # Should return None when tree is empty
    assert result is None


@responses.activate
def test_get_category_path_with_actual_api_structure():
    """Test get_category_path using the exact structure from actual Tweakwise API response."""
    language = "en"
    category = SyncforceCategory.CUTTING_ACCESSORIES  # 831
    parent_category = SyncforceCategory.INDUSTRIAL  # 8604

    # Expected parent category path: 1000 (EN) + 8604 = 10008604
    parent_category_id = 10008604

    # Use a subset of the actual API response structure
    mock_tree_response = {
        "categories": [
            {
                "title": "Industrial Equipment",
                "categoryid": 10008604,
                "categorypath": "10008604",
                "children": [
                    {
                        "title": "Lifting",
                        "categoryid": 10007411,
                        "categorypath": "10008604-10007411",
                        "children": [
                            {
                                "title": "Cylinders",
                                "categoryid": 10005121,
                                "categorypath": "10008604-10007411-10005121",
                                "children": [],
                            },
                            {
                                "title": "Pumps",
                                "categoryid": 10001017,
                                "categorypath": "10008604-10007411-10001017",
                                "children": [
                                    {
                                        "title": "Hand and Foot pumps",
                                        "categoryid": 10003118,
                                        "categorypath": "10008604-10007411-10001017-10003118",
                                        "children": [],
                                    }
                                ],
                            },
                        ],
                    },
                    {
                        "title": "Cutting",
                        "categoryid": 10002838,
                        "categorypath": "10008604-10002838",
                        "children": [
                            {
                                "title": "Cutters",
                                "categoryid": 10006921,
                                "categorypath": "10008604-10002838-10006921",
                                "children": [],
                            },
                            {
                                "title": "Accessories",
                                "categoryid": 1000831,  # 1000 (EN) + 831
                                "categorypath": "10008604-10002838-1000831",
                                "children": [],
                            },
                        ],
                    },
                ],
            }
        ]
    }

    # Mock the API call to get_category_tree
    responses.get(
        url=f"{frontend_url}/categorytree/{tweakwise_key}",
        match=[matchers.query_param_matcher({"catid": parent_category_id})],
        json=mock_tree_response,
    )

    result = tweakwise_client.get_category_path(language, category, parent_category)

    # Should find the accessories category and return its path
    assert result == "10008604-10002838-1000831"
