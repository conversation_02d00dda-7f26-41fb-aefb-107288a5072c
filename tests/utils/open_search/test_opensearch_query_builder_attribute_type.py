"""Test that OpenSearchQueryBuilder includes attribute_type in aggregations."""

import pytest
from holmatro_customer_portal.utils.open_search.opensearch_query_builder import OpenSearchQueryBuilder


class TestOpenSearchQueryBuilderAttributeType:
    """Test that attribute_type is included in aggregation queries."""

    def test_build_aggregations_includes_attribute_type(self):
        """Test that the main build aggregations method includes attribute_type."""
        query_builder = OpenSearchQueryBuilder(fuzzy_enabled=False)
        
        # Build aggregations
        aggregations = query_builder._build_aggregations()
        
        # Check that attribute_type is included in the aggregation structure
        assert "filterable_attributes" in aggregations
        assert "aggs" in aggregations["filterable_attributes"]
        assert "attribute_ids" in aggregations["filterable_attributes"]["aggs"]
        assert "aggs" in aggregations["filterable_attributes"]["aggs"]["attribute_ids"]
        
        attribute_aggs = aggregations["filterable_attributes"]["aggs"]["attribute_ids"]["aggs"]
        
        # Verify that attribute_type is included alongside other fields
        assert "attribute_name" in attribute_aggs
        assert "attribute_type" in attribute_aggs
        assert "values_string" in attribute_aggs
        assert "values_numeric_stats" in attribute_aggs
        assert "values_numeric_terms" in attribute_aggs
        
        # Verify the attribute_type aggregation structure
        attribute_type_agg = attribute_aggs["attribute_type"]
        assert "terms" in attribute_type_agg
        assert attribute_type_agg["terms"]["field"] == "filterable_attributes.attribute_type"
        assert attribute_type_agg["terms"]["size"] == 1

    def test_build_attribute_aggregation_includes_attribute_type(self):
        """Test that the specific attribute aggregation method includes attribute_type."""
        attribute_id = 123
        category_id = 456
        
        # Build attribute-specific aggregation
        query = OpenSearchQueryBuilder.build_attribute_aggregation(attribute_id, category_id)
        
        # Check the overall structure
        assert "aggs" in query
        assert "filterable_attributes" in query["aggs"]
        
        filterable_aggs = query["aggs"]["filterable_attributes"]
        assert "nested" in filterable_aggs
        assert filterable_aggs["nested"]["path"] == "filterable_attributes"
        assert "aggs" in filterable_aggs
        
        # Check the filter_by_attribute_id structure
        filter_aggs = filterable_aggs["aggs"]["filter_by_attribute_id"]
        assert "filter" in filter_aggs
        assert filter_aggs["filter"]["term"]["filterable_attributes.attribute_id"] == attribute_id
        assert "aggs" in filter_aggs
        
        # Verify that attribute_type is included in the sub-aggregations
        sub_aggs = filter_aggs["aggs"]
        assert "attribute_type" in sub_aggs
        assert "values_string" in sub_aggs
        assert "values_numeric_stats" in sub_aggs
        
        # Verify the attribute_type aggregation structure
        attribute_type_agg = sub_aggs["attribute_type"]
        assert "terms" in attribute_type_agg
        assert attribute_type_agg["terms"]["field"] == "filterable_attributes.attribute_type"
        assert attribute_type_agg["terms"]["size"] == 1

    def test_build_attribute_aggregation_with_category_filter(self):
        """Test that category filtering works correctly with attribute_type included."""
        attribute_id = 789
        category_id = 101
        
        query = OpenSearchQueryBuilder.build_attribute_aggregation(attribute_id, category_id)
        
        # Verify category filter is applied
        assert "query" in query
        assert "term" in query["query"]
        assert query["query"]["term"]["category_ids"] == category_id
        
        # Verify attribute_type is still included in aggregations
        filter_aggs = query["aggs"]["filterable_attributes"]["aggs"]["filter_by_attribute_id"]["aggs"]
        assert "attribute_type" in filter_aggs

    def test_build_attribute_aggregation_without_category_filter(self):
        """Test that match_all query is used when no category is specified."""
        attribute_id = 999
        
        query = OpenSearchQueryBuilder.build_attribute_aggregation(attribute_id, None)
        
        # Verify match_all query is used
        assert "query" in query
        assert "match_all" in query["query"]
        
        # Verify attribute_type is still included in aggregations
        filter_aggs = query["aggs"]["filterable_attributes"]["aggs"]["filter_by_attribute_id"]["aggs"]
        assert "attribute_type" in filter_aggs
