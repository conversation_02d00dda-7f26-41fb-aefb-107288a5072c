import json
from unittest.mock import patch

import pytest
import requests

from holmatro_customer_portal.utils.storage_utils import fetch_json_contents_by_ids, get_api_json_contents


@pytest.fixture
def mock_db_session(mocker):
    return mocker.MagicMock()


@patch("holmatro_customer_portal.utils.storage_utils.requests.get")
def test_get_api_json_contents(mock_get):
    content = {"id": 1, "name": "ProductA"}
    mock_response = requests.Response()
    mock_response.status_code = 200
    mock_response._content = json.dumps(content).encode("utf-8")
    mock_response.headers["Content-Type"] = "application/json"
    mock_get.return_value = mock_response

    result = get_api_json_contents("product-details-1.json")
    assert result == content


@patch("holmatro_customer_portal.utils.storage_utils.requests.get")
def test_fetch_json_contents_by_ids(mock_get):
    sample_data_1 = {"id": 1, "name": "ProductA"}
    sample_data_2 = {"id": 2, "name": "ProductB"}

    def side_effect(url, params, headers, timeout):
        if params["file_name"] == "product-details-1.json":
            mock_response = requests.Response()
            mock_response.status_code = 200
            mock_response._content = json.dumps(sample_data_1).encode("utf-8")
            mock_response.headers["Content-Type"] = "application/json"
            return mock_response
        elif params["file_name"] == "product-details-2.json":
            mock_response = requests.Response()
            mock_response.status_code = 200
            mock_response._content = json.dumps(sample_data_2).encode("utf-8")
            mock_response.headers["Content-Type"] = "application/json"
            return mock_response
        else:
            mock_response = requests.Response()
            mock_response.status_code = 404
            return mock_response

    mock_get.side_effect = side_effect

    results = fetch_json_contents_by_ids([1, 2])
    assert results == {
        "product-details-1.json": sample_data_1,
        "product-details-2.json": sample_data_2,
    }
