from datetime import datetime, timedelta, timezone
from http import HTTPStatus

import pytest
import responses

from holmatro_customer_portal.database.enums import Currency
from holmatro_customer_portal.schemas.my_hm_api_schema import MYHMProductInfo
from holmatro_customer_portal.schemas.response_schema import Stock
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient

stock_fixture = {
    "ArticleNo": "1",
    "Stock": 0.0,
    "RestockDays": 7,
    "Unit": "piece",
    "Warehouse": "HIE300",
    "CreatedTsUtc": datetime.now(timezone.utc).isoformat(),
    "UpdatedTsUtc": datetime.now(timezone.utc).isoformat(),
}


@pytest.mark.parametrize(
    "stock_quantity, stock_status, status_code",
    [
        pytest.param(100.00, Stock.IN_STOCK, HTTPStatus.OK, id="in stock"),
        pytest.param(0.0, Stock.NO_STOCK, HTTPStatus.OK, id="no stock"),
        pytest.param(None, Stock.ON_REQUEST, HTTPStatus.OK, id="on request"),
    ],
)
@responses.activate
def test_get_stock(stock_quantity: float | None, stock_status: Stock, status_code: HTTPStatus):
    stock_fixture["Stock"] = stock_quantity
    res = stock_fixture

    responses.get(url="http://example.com/products/1/availability", json=res, status=status_code)
    client = MyHolmatroPortalApiClient("jwt_token")

    result = client.get_stock("1")

    assert result.stock_status == stock_status
    assert result.article_no == "1"
    assert result.stock_quantity == (stock_quantity if stock_quantity is not None else None)


@responses.activate
def test_get_price_for_currency():
    res = {
        "ArticleNo": "1",
        "GrossPrice": "200.00",
        "NettPrice": "100.00",
        "Discount%": "20.000",
        "Currency": "EUR",
        "CreatedTsUtc": datetime.now().isoformat(),
        "UpdatedTsUtc": datetime.now().isoformat(),
    }

    responses.get(url="http://example.com/products/1/salesprices", json=res, status=HTTPStatus.OK)
    client = MyHolmatroPortalApiClient("jwt_token")

    result = client.get_product_sales_prices("1", Currency.EUR)  # matching currency

    assert result.article_no == "1"
    assert result.gross_price == 200.0
    assert result.nett_price == 100.0
    assert result.currency == Currency.EUR
    assert result.discount == 20

    result = client.get_product_sales_prices("1", Currency.USD)  # non-matching currency
    assert result.gross_price is None

    result = client.get_product_sales_prices("1", None)  # no currency
    assert result.gross_price is None


@responses.activate
def test_get_price_for_products_happy_path():
    sales_info = {
        "GrossPrice": 200.0,
        "NettPrice": 100.0,
        "Discount%": 20.0,
        "Currency": "EUR",
        "Unit": "piece",
        "Stock": 2.0,
        "RestockDays": 28,
        "CreatedTsUtc": str(datetime.today()),
        "UpdatedTsUtc": "2024-04-18T08:56:49.7970000",
    }

    res = {"SalesInfo": [{**sales_info, "ArticleNo": "1"}, {**sales_info, "ArticleNo": "2"}]}

    responses.get(url="http://example.com/products/salesinfo", json=res, status=HTTPStatus.OK)
    client = MyHolmatroPortalApiClient("jwt_token")

    result = client.get_sales_info(["1", "2"], Currency.EUR)  # matching currency
    assert isinstance(result, list)
    assert isinstance(result[0], MYHMProductInfo)

    result = result[0]
    assert result.article_no == "1"
    assert result.gross_price == 200.0
    assert result.nett_price == 100.0
    assert result.currency == Currency.EUR
    assert result.discount == 20
    assert result.stock_quantity == 2
    assert result.stock_status == Stock.IN_STOCK
    assert result.available_from.isoformat() == (datetime.today().date() + timedelta(days=28)).isoformat()


@pytest.mark.parametrize(
    "res, status_code, article_numbers",
    [
        pytest.param({"SalesInfo": []}, HTTPStatus.OK, ["1"], id="Response list is empty"),
        pytest.param(None, HTTPStatus.NOT_FOUND, ["1"], id="Response not found"),
    ],
)
@responses.activate
def test_get_price_for_products(res: dict | None, status_code: HTTPStatus, article_numbers: list):
    responses.get(url="http://example.com/products/salesinfo", json=res, status=status_code)
    client = MyHolmatroPortalApiClient("jwt_token")

    result = client.get_sales_info(article_numbers, Currency.EUR)  # matching currency
    assert isinstance(result, list)
    assert isinstance(result[0], MYHMProductInfo)

    result = result[0]
    assert result.article_no == "1"
    assert result.gross_price is None
    assert result.nett_price is None
    assert result.currency is None
    assert result.discount is None
    assert result.stock_quantity is None
    assert result.stock_status == Stock.ON_REQUEST


@responses.activate
def test_get_price_for_products_not_find_all_products():
    # Test if 2 article numbers is searched and endpoint returns only 1
    sales_info = {
        "GrossPrice": 200.0,
        "NettPrice": 100.0,
        "Discount%": 20.0,
        "Currency": "EUR",
        "Unit": "piece",
        "Stock": 2.0,
        "RestockDays": 28,
        "CreatedTsUtc": str(datetime.today()),
        "UpdatedTsUtc": "2024-04-18T08:56:49.7970000",
    }

    res = {"SalesInfo": [{**sales_info, "ArticleNo": "1"}]}

    responses.get(url="http://example.com/products/salesinfo", json=res, status=HTTPStatus.OK)
    client = MyHolmatroPortalApiClient("jwt_token")

    result = client.get_sales_info(["1", "2"], Currency.EUR)  # matching currency
    assert isinstance(result, list)
    assert isinstance(result[0], MYHMProductInfo)

    result_article_1 = result[0]
    assert result_article_1.article_no == "1"
    assert result_article_1.gross_price == 200.0
    assert result_article_1.nett_price == 100.0
    assert result_article_1.currency == Currency.EUR
    assert result_article_1.discount == 20
    assert result_article_1.stock_quantity == 2
    assert result_article_1.stock_status == Stock.IN_STOCK
    assert result_article_1.available_from.isoformat() == (datetime.today().date() + timedelta(days=28)).isoformat()

    result_article_2 = result[1]
    assert result_article_2.article_no == "2"
    assert result_article_2.gross_price is None
    assert result_article_2.nett_price is None
    assert result_article_2.currency is None
    assert result_article_2.discount is None
    assert result_article_2.stock_quantity is None
    assert result_article_2.stock_status == Stock.ON_REQUEST
    assert result_article_2.available_from is None
