import uuid

import jwt
from mock_alchemy.mocking import UnifiedAlchemyMagicMock

from holmatro_customer_portal.database.enums import LanguageEnum
from holmatro_customer_portal.database.models import Category, Language, User
from holmatro_customer_portal.utils.auth.anonymous_auth import create_anonymous_user, generate_token
from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.env import Env


def test_create_anonymous_user():
    db = UnifiedAlchemyMagicMock()
    language = Language(language_code=LanguageEnum.DUTCH.value)
    lifting = Category(category_id=SyncforceCategory.INDUSTRIAL_LIFTING.value)
    cutting = Category(category_id=SyncforceCategory.INDUSTRIAL_CUTTING.value)
    db.add_all([language, lifting, cutting])

    result = create_anonymous_user(db, LanguageEnum.DUTCH)

    assert isinstance(result, User)
    assert len(result.categories) == 2
    assert result.preferred_language == language
    assert result.anonymous


def test_generate_token():
    user = User(id=uuid.uuid4(), email="<EMAIL>")

    token = generate_token(user)

    decoded = jwt.decode(token, Env.JWT_ENCODING_KEY.get(), algorithms=["HS256"])
    assert decoded["sub"] == str(user.id)
    assert decoded["emails"] == [user.email]
