import pytest
from fixtures.database_models_fixture import mocked_user

from holmatro_customer_portal.utils.category_path_validations import validate_category_path


@pytest.mark.parametrize(
    "category_path, expected_result",
    [
        ("1200", None),
        ("80007411-800012", "80007411-800012"),
        ("60007411", "60007411"),
        ("10008604-10007411", "10008604-10007411"),
    ],
)
def test_validate_category_path(category_path: str | None, expected_result: str):
    res = validate_category_path(mocked_user, category_path)

    assert res == expected_result
