from holmatro_customer_portal.schemas.syncforce_product_schema import Category


def test_generate_category_id():
    """Test that Category schema generates correct category_id from category_umid"""
    category_data = {
        "translations": [],
        "sort_index": 0,
        "syncforce_index": 0,
        "category_umid": "a36fd28ef96a42529952a9f7b3d8879e",
        "assets": None,
    }

    category = Category(**category_data)

    assert category.category_id == "4606"
