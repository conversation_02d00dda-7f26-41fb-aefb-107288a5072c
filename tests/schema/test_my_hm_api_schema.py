from datetime import datetime

from holmatro_customer_portal.schemas.my_hm_api_schema import MYHMContact


def test_validation():
    contact = {
        "user": {
            "email_address": "<EMAIL>",
            "contact_id": "12345",
            "relation_id": "67890",
            "relation_name": "<PERSON><PERSON><PERSON>",
            "currency_id": "*",
            "created_ts_utc": datetime.now().isoformat(),
            "updated_ts_utc": datetime.now().isoformat(),
            "language_id": "EN",
            "country_id": "DE",
        },
        "attributes": [{"sf_proposition": "HIE", "sf_category": "HolmatroIndustrial", "sf_sub_category": "Lifting:"}],
    }
    contact = MYHMContact(**contact)
    assert contact.currency_id is None
    assert contact.language_id == "en"
