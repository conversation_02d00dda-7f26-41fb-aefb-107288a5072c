services:
  app:
    container_name: "holmatro-customer-portal-api"
    build:
      context: .
      dockerfile: docker-resources/Dockerfile
      args:
        - PRIVATE_PYPI_USER=${PRIVATE_PYPI_USER}
        - PRIVATE_PYPI_SECRET=${PRIVATE_PYPI_SECRET}
    volumes:
      - .:/app
    depends_on:
      - mysql-service
      - opensearch
    ports:
      - 8000:8000
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 10s
      timeout: 5s
      retries: 3
    stdin_open: true
    tty: true
  rabbitmq:
    image: "rabbitmq:3.12-management"
    container_name: "rabbitmq"
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_DEFAULT_USER}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_DEFAULT_PASS}
    ports:
      - "5672:5672"
      - "15672:15672"
  celery-worker:
    container_name: "celery-worker"
    build:
      context: .
      dockerfile: docker-resources/Dockerfile
      args:
        - PRIVATE_PYPI_USER=${PRIVATE_PYPI_USER}
        - PRIVATE_PYPI_SECRET=${PRIVATE_PYPI_SECRET}
    environment:
      - RABBITMQ_BROKER_URL=amqp://${RABBITMQ_DEFAULT_USER}:${RABBITMQ_DEFAULT_PASS}@rabbitmq:5672
    entrypoint: []
    command: ["poetry", "run", "celery", "-A", "holmatro_customer_portal.celery_config.celery_app", "worker","--concurrency=1", "--loglevel=info"]
    volumes:
      - .:/app
    depends_on:
      - app
      - rabbitmq
    env_file:
      - .env
  mysql-service:
    image: ${MYSQL_ARCH}
    container_name: "holmatro-customer-portal-db"
    volumes:
      - mysql:/var/lib/mysql
      - ./docker-resources/mysql:/docker-entrypoint-initdb.d
    environment:
      MYSQL_ROOT_PASSWORD: ${DATABASE_PASSWORD}
      MYSQL_DATABASE: ${DATABASE_NAME}
      MYSQL_PASSWORD: ${DATABASE_PASSWORD}
    ports:
      - 3306:3306
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: holmatro-customer-portal-phpmyadmin
    links:
      - mysql-service:db
    depends_on:
      - mysql-service
    ports:
      - 8080:80
  opensearch:
    image: opensearchproject/opensearch:2
    container_name: "holmatro-customer-portal-opensearch"
    environment:
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m"
      - "DISABLE_SECURITY_PLUGIN=true"
    volumes:
      - opensearch-data:/usr/share/opensearch/data
    ports:
      - 9200:9200
      - 9600:9600
  opensearch-dashboards:
    image: opensearchproject/opensearch-dashboards:2
    container_name: "holmatro-customer-portal-opensearch-dashboards"
    environment:
      - 'OPENSEARCH_HOSTS=["http://opensearch:9200"]'
      - "DISABLE_SECURITY_DASHBOARDS_PLUGIN=true"
    ports:
      - 5601:5601
    depends_on:
      - opensearch

volumes:
  mysql:
  opensearch-data:

