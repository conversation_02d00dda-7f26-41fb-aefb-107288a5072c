[tool.poetry]
name = "holmatro-customer-portal"
version = "0.1.0"
description = ""
authors = ["Harborn digital"]
readme = "README.md"
packages = [{include = "holmatro_customer_portal"}]

[[tool.poetry.source]]
name = "harborn"
url = "https://pypi.packages.harborn.com/simple/"
priority = "supplemental"

[tool.poetry.dependencies]
python = "~3.12"
pydantic = {extras = ["email"], version = "^2.7.2"}
FastApi = "^0.115.0"
uvicorn = "^0.34.0"
sqlalchemy = "^2.0.30"
pymysql = "^1.1.1"
alembic = "^1.13.1"
cryptography = "^44.0.2"
requests = "^2.32.3"
boto3 = "^1.34.127"
python-jwt-auth = "^0.2.0"
requests-cache = "^1.2.0"
transitions = "^0.9.1"
sentry-sdk = "^2.5.1"
celery = "^5.4.0"
cachetools = "^5.3.3"
pyjwt = "^2.8.0"
opensearch-py = "^2.8.0"
typer = "^0.16.0"

[tool.poetry.group.dev.dependencies]
poethepoet = "^0.29.0"
isort = "^5.12.0"
autoflake = "^2.0.0"
black = "^24.4.2"
pytest = "^8.2.2"
pytest-cov = "^5.0.0"
pytest-timeout = "^2.0.1"
pytest-xdist = "^3.1.0"
pytest-random-order = "^1.1.0"
pytest-mock = "^3.12.0"
coverage = "^7.1.0"
pre-commit = "^3.0.4"
requests-mock = "^1.10.0"
mypy = "^1.0"
flake8 = "^7.1.0"
flake8-bugbear = "^24.4.26"
flake8-deprecated = "^2.0.1"
flake8-executable = "^2.1.3"
flake8-simplify = "^0.21.0"
flake8-debugger = "^4.1.2"
flake8-cognitive-complexity = "^0.1.0"
flake8-annotations-complexity = "^0.0.8"
flake8-functions = "^0.0.8"
bandit = "^1.7.5"
pypdf = "^4.2.0"
Jinja2 = "^3.1.2"
pdfkit = "^1.0.0"
mock-alchemy = "^0.2.6"
moto = "^5.0.9"
python-dotenv = "^1.0.0"
responses = "^0.23.3"
types-requests = "^2.28"
types-cachetools = "^5.3.0.7"
polyfactory = "^2.21.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 120
target-version = ['py310']
include = '\.pyi?$'

[tool.autoflake]
in-place = true
expand-star-imports = true
recursive = true

[tool.isort]
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 120

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
namespace_packages = true
disallow_untyped_defs = true
explicit_package_bases = true
exclude = ["^_dev/*", "^tests/*", "holmatro_customer_portal/services/configurator/transitions/*", "holmatro_customer_portal/services/configurator/states/*"]

[tool.poe.tasks]
fix = [
    { cmd = "autoflake --in-place --expand-star-imports -r holmatro_customer_portal/ tests/"},
    { cmd = "black holmatro_customer_portal/ tests/" },
    { cmd = "isort holmatro_customer_portal/ tests/" },
    { cmd = "mypy holmatro_customer_portal --namespace-packages --explicit-package-bases" },
]
cicd = [
    { cmd = "autoflake --in-place --expand-star-imports -r holmatro_customer_portal/ tests/"},
    { cmd = "black --check holmatro_customer_portal/ tests/" },
    { cmd = "isort --check-only holmatro_customer_portal/ tests/" },
    { cmd = "mypy holmatro_customer_portal --namespace-packages --explicit-package-bases" },
]

test = "pytest --cov=holmatro_customer_portal/ --cov-report=term-missing:skip-covered --cov-report=lcov:coverage.info tests -n auto --dist loadfile  --log-level ERROR --junitxml=test-results.xml"
retest = "pytest --lf"
security = "bandit -s B101,B311 --recursive holmatro_customer_portal -x holmatro_customer_portal/utils/env.py"
