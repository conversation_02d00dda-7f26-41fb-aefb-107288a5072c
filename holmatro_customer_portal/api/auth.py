from fastapi import APIRouter, Depends, status

from holmatro_customer_portal.database.enums import LanguageEnum
from holmatro_customer_portal.schemas.auth import Token, UserClaims
from holmatro_customer_portal.utils.auth.anonymous_auth import create_anonymous_user, generate_token
from holmatro_customer_portal.utils.auth.auth import get_user_claims
from holmatro_customer_portal.utils.database import Session, get_db

router = APIRouter()


@router.get(
    "/claims",
    response_model=UserClaims,
    description="Retrieve the claims for the authenticated user.",
    status_code=status.HTTP_200_OK,
)
def get_claims(claims: UserClaims = Depends(get_user_claims)) -> UserClaims:
    return claims


@router.get("/generate-token", summary="Returns a token for anonymous users")
def generate_token_api(db: Session = Depends(get_db), language_code: LanguageEnum = LanguageEnum.ENGLISH) -> Token:
    user = create_anonymous_user(db, language_code)
    return Token(token=generate_token(user))
