import uuid
from typing import Annotated

from fastapi import APIRouter, Depends, Path, status
from fastapi.params import Query

from holmatro_customer_portal.api.dependencies import (
    get_catalog_repository,
    get_params,
    get_search_params,
    validate_cat_id,
)
from holmatro_customer_portal.database.models import User
from holmatro_customer_portal.domain.repositories.catalog_repository import CatalogRepository
from holmatro_customer_portal.schemas.response_schema import (
    AutocompleteRes,
    ParamsReq,
    ProductDetailsRes,
    ProductPreviewRes,
    ProductsRes,
    ProductsWithFiltersRes,
    SearchFiltersReq,
    SearchParamsReq,
)
from holmatro_customer_portal.services.favorite_product import (
    add_favorite_product_handle,
    get_favorite_products_handle,
    remove_favorite_product_handle,
)
from holmatro_customer_portal.services.get_autocomplete import get_autocomplete_handle
from holmatro_customer_portal.services.get_product_details import get_product_details_handle
from holmatro_customer_portal.services.get_products import get_products_with_filters_handle
from holmatro_customer_portal.services.get_products_by_search import get_products_by_search_handle
from holmatro_customer_portal.services.products.get_category_tree import get_category_tree_handle
from holmatro_customer_portal.utils.auth.auth import get_jwt_token, get_user, get_user_with_favorites
from holmatro_customer_portal.utils.database import Session, get_db
from holmatro_customer_portal.utils.my_hm.my_hm_client_factory import MyHolmatroClientFactory
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import Category

router = APIRouter()


@router.post("", summary="Get a list of products and filters")
def get_products(
    params: Annotated[ParamsReq, Depends(get_params)],
    catalog_repository: Annotated[CatalogRepository, Depends(get_catalog_repository)],
    jwt_token: str = Depends(get_jwt_token),
    filters: SearchFiltersReq | None = None,
    db: Session = Depends(get_db),
    user: User = Depends(get_user),
) -> ProductsWithFiltersRes:
    """
    Returns a json with a list of products, properties and filters.
    \n
    **Query parameters**: 'prod_cat_path' or 'user_category_id' is required. \n
    **Filters**:
    The filters might have different selection types.
    If it is checkbox, the value will be the title.
    If it is slider, the value will be: "smallest value selected-biggest value selected", for example: "100-150".
    If more than one product is selected, the value must be an array of strings.
    """
    my_hm_client = MyHolmatroClientFactory.create_client(jwt_token)

    return get_products_with_filters_handle(catalog_repository, params, filters, user, db, my_hm_client)


@router.post("/search/{search_query}", summary="Get a list of products related to the search query")
def get_products_by_search(
    params: Annotated[SearchParamsReq, Depends(get_search_params)],
    catalog_repository: Annotated[CatalogRepository, Depends(get_catalog_repository)],
    jwt_token: str = Depends(get_jwt_token),
    filters: SearchFiltersReq | None = None,
    db: Session = Depends(get_db),
    user: User = Depends(get_user),
) -> ProductsRes:
    """
    Returns a json with a list of products and a list of properties related to the search.

    The filters might have different selection types.
    If it is checkbox, the value will be the title.
    If it is slider, the value will be: "smallest value selected-biggest value selected", for example: "100-150".
    If more than one product is selected, the value must be an array of strings.
    """
    my_hm_client = MyHolmatroClientFactory.create_client(jwt_token)
    return get_products_by_search_handle(catalog_repository, params, filters, user, db, my_hm_client)


@router.get("/categories", summary="Get a json with the category tree")
def get_categories(
    catalog_repository: Annotated[CatalogRepository, Depends(get_catalog_repository)],
    cat_id: int = Depends(validate_cat_id),
    db: Session = Depends(get_db),
    user: User = Depends(get_user),
) -> list[Category]:
    return get_category_tree_handle(db, catalog_repository, user, cat_id)


@router.get("/autocomplete/{search_query}", summary="Get a list of products and words according to query")
def get_autocomplete(
    search_query: Annotated[str, Path(description="The search query")],
    catalog_repository: Annotated[CatalogRepository, Depends(get_catalog_repository)],
    prod_cat_path: Annotated[
        str | None, Query(description="The product category path in Tweakwise.", deprecated=True)
    ] = None,  # TODO: to deprecate it
    db: Session = Depends(get_db),
    user: User = Depends(get_user),
) -> AutocompleteRes:
    """
    Returns a json with products and suggestions words according to query.
    """
    params = SearchParamsReq(search_query=search_query, category_path=prod_cat_path)

    return get_autocomplete_handle(catalog_repository, params, user, db)


@router.get("/favorite_products", summary="Get all the favorites products from a user")
def get_favorite_products(
    db: Session = Depends(get_db),
    jwt_token: str = Depends(get_jwt_token),
    user: User = Depends(get_user_with_favorites),
) -> list[ProductPreviewRes]:
    my_hm_client = MyHolmatroClientFactory.create_client(jwt_token)

    return get_favorite_products_handle(user, db, my_hm_client)


@router.post(
    "/favorite_product/{product_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Add a product to a user's favorites",
)
def add_favorite_product(
    product_id: Annotated[uuid.UUID, Path(description="The product id")],
    db: Session = Depends(get_db),
    user: User = Depends(get_user_with_favorites),
) -> None:
    return add_favorite_product_handle(product_id, user, db)


@router.delete(
    "/favorite_product/{product_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Remove a product from a user's favorites",
)
def remove_favorite_product(
    product_id: Annotated[uuid.UUID, Path(description="The product id")],
    db: Session = Depends(get_db),
    user: User = Depends(get_user_with_favorites),
) -> None:
    return remove_favorite_product_handle(product_id, user, db)


@router.get("/product_details/{product_id}", summary="Get the details of a single product")
def get_product_details(
    product_id: Annotated[uuid.UUID, Path(description="The product id")],
    jwt_token: str = Depends(get_jwt_token),
    db: Session = Depends(get_db),
    user: User = Depends(get_user),
) -> ProductDetailsRes:
    my_hm_client = MyHolmatroClientFactory.create_client(jwt_token)

    return get_product_details_handle(product_id, user, db, my_hm_client)
