from http import HTTPStatus

from fastapi import APIRouter, Depends, HTTPException
from starlette.status import HTTP_409_CONFLICT

from holmatro_customer_portal.database.enums import LanguageEnum, SystemType
from holmatro_customer_portal.database.models import Language, User
from holmatro_customer_portal.schemas.auth import UserClaims
from holmatro_customer_portal.schemas.response_schema import ProfileRes
from holmatro_customer_portal.services.get_user import collect_anonymous_user_info, parse_user_info, resync_user
from holmatro_customer_portal.utils.auth.auth import get_jwt_token, get_user, get_user_claims
from holmatro_customer_portal.utils.database import Session, get_db
from holmatro_customer_portal.utils.exceptions.user_setting_exception import UserSettingException
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient

router = APIRouter()


@router.get("/profile", summary="Get the profile details of the user")
def get_profile_details(
    jwt_token: str = Depends(get_jwt_token),
    db: Session = Depends(get_db),
    user_claims: UserClaims = Depends(get_user_claims),
) -> ProfileRes:

    if user_claims.is_anonymous_user():
        return collect_anonymous_user_info(db, user_claims)

    my_hm_client = MyHolmatroPortalApiClient(jwt_token)
    user_details = resync_user(user_claims, db, my_hm_client)

    if not user_details:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="User not found")

    return parse_user_info(user_details)


@router.post(
    "/preferred_language/{language_code}",
    description="Update the preferred language. NOTE: setting this to a language other than English is only allowed if the system type is not IMPERIAL.",
)
def update_preferred_language(
    language_code: LanguageEnum,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_user),
) -> dict:
    db_language = db.query(Language).filter_by(language_code=language_code.value).first()
    try:
        current_user.language = db_language  # type: ignore
    except UserSettingException:
        raise HTTPException(
            HTTP_409_CONFLICT, detail=f"Language '{language_code}' not allowed when the system type is IMPERIAL."
        )
    db.commit()

    return {"details": "Preferred language updated successfully", "preferred_language": language_code}


@router.post(
    "/system_type/{system_type}",
    description="Update the preferred system type. NOTE: setting this to IMPERIAL is only allowed in combination with the English language.",
)
def update_preferred_system_type(
    system_type: SystemType,
    db: Session = Depends(get_db),
    user: User = Depends(get_user),
) -> dict:
    try:
        user.system_type = system_type  # type: ignore
    except UserSettingException:
        raise HTTPException(
            HTTP_409_CONFLICT,
            detail=f"System type '{system_type}' not allowed when the preferred language is not English.",
        )
    db.commit()

    return {"details": "Preferred system type updated successfully", "preferred_system_type": system_type}
