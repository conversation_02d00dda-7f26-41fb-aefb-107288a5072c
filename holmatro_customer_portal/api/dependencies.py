from http import HTTPStatus
from typing import Annotated

from fastapi import Depends, HTTPException, Path, Query

from holmatro_customer_portal.data.holmatro_catalog_repository import HolmatroCatalogRepository
from holmatro_customer_portal.data.tweakwise_repository import TweakwiseCatalogRepository
from holmatro_customer_portal.database.enums import SystemType
from holmatro_customer_portal.database.models import User
from holmatro_customer_portal.domain.repositories.catalog_repository import CatalogRepository
from holmatro_customer_portal.schemas.response_schema import ParamsReq, SearchParamsReq
from holmatro_customer_portal.services.configurator.configurator_enums import ImperialFilterTemplateIDs
from holmatro_customer_portal.utils.auth.auth import get_user
from holmatro_customer_portal.utils.category_path_validations import (
    retrieve_category_path_from_category_id,
    validate_category_id,
    validate_category_path,
    validate_user_category_id,
)
from holmatro_customer_portal.utils.database import Session, get_db
from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.open_search.opensearch_client import OpenSearchClient
from holmatro_customer_portal.utils.tweakwise.tweakwise_client import TweakwiseClient

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


def get_catalog_repository(db: Session = Depends(get_db)) -> CatalogRepository:
    return HolmatroCatalogRepository(
        db=db,
        opensearch_client=OpenSearchClient(
            is_fuzzy_search_enabled=Env.OPENSEARCH_FUZZY_SEARCH_ENABLED.get(),
            index_name=Env.OPENSEARCH_INDEX_NAME.get(),
        ),
    )
    if Env.IS_HOLMATRO_CATALOG.get():
        return HolmatroCatalogRepository(
            db=db,
            opensearch_client=OpenSearchClient(
                is_fuzzy_search_enabled=Env.OPENSEARCH_FUZZY_SEARCH_ENABLED.get(),
                index_name=Env.OPENSEARCH_INDEX_NAME.get(),
            ),
        )
    else:
        return TweakwiseCatalogRepository(tweakwise_client=TweakwiseClient())


def get_filter_template_id(user: User, category_id: int | None) -> int | None:
    """For imperial system, the filter template is fixed to an 'EN Imperial' version for the appropriate product group
    For the metric system: leave the filter template undefined to use the default filter template"""
    if category_id is None:
        return None
    if user.system_type == SystemType.IMPERIAL:
        return ImperialFilterTemplateIDs[SyncforceCategory(category_id).name]
    return None


def get_search_params(
    search_query: Annotated[str, Path(description="The search query")],
    pg_no: Annotated[int, Query(description="The page number for pagination.")] = 1,
    no_prod_pg: Annotated[
        int, Query(lt=51, description="The quantity of products to be showed per page. Max: 50")
    ] = 12,
    prod_category_id: Annotated[
        int | None,
        Query(
            description="**integer** - The category/group id linked to the user.\
                   This information is available in the user profile endpoint."
        ),
    ] = None,
    user: User = Depends(get_user),
) -> SearchParamsReq:
    category_id = None
    if prod_category_id:
        category_id = validate_user_category_id(user, prod_category_id)
        if not category_id:
            raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="User is not linked to this category")

    return SearchParamsReq(
        search_query=search_query,
        page_number=pg_no,
        category_path=None,
        products_per_page=no_prod_pg,
        filter_template_id=get_filter_template_id(user, category_id),
        category_id=category_id,
    )


def get_params(
    pg_no: Annotated[int, Query(description="The page number for pagination.")] = 1,
    prod_cat_path: Annotated[
        str | None,
        Query(
            description="**string** - The product category path in Tweakwise.\
                 **Required** if 'user_category_id' is not available"
        ),
    ] = None,
    no_prod_pg: Annotated[
        int, Query(lt=51, description="The quantity of products to be showed per page. Max: 50")
    ] = 12,
    prod_category_id: Annotated[
        int | None,
        Query(
            description="The category/group id linked to the user.\
                This information is available in the user profile endpoint. \
                **Required** if 'prod_cat_path' is not available"
        ),
    ] = None,
    user: User = Depends(get_user),
) -> ParamsReq:
    category_path = _validate_and_get_category_path(user, prod_cat_path, prod_category_id)
    if not prod_category_id:
        prod_category_id = _get_category_id_from_path(category_path)
    return ParamsReq(
        page_number=pg_no,
        category_path=category_path,
        products_per_page=no_prod_pg,
        filter_template_id=get_filter_template_id(user, prod_category_id),
        category_id=prod_category_id,
    )


def validate_cat_id(
    cat_id: Annotated[int | None, Query(description="The category id from Tweakwise")] = None,
) -> int | None:
    """If cat_id, validates it"""
    if cat_id:
        category_id = validate_category_id(cat_id)
        if not category_id:
            raise HTTPException(status_code=HTTPStatus.BAD_REQUEST, detail="No valid category ID provided")
        return category_id

    return None


def _validate_and_get_category_path(user: User, req_category_path: str | None, category_id: int | None) -> str:
    """
    Validate category path if exists. If is not valid, it will check if category_id is available and validate it.
    Else, validate category user id.
    """

    # If not category_path or user_category_id, block access
    if not req_category_path and not category_id:
        raise HTTPException(status_code=HTTPStatus.BAD_REQUEST, detail="Category path or user category is required")

    # Check if category path is available and validate it. If not valid, it returns None
    if req_category_path:
        category_path = validate_category_path(user, req_category_path)
    else:
        category_path = None

    if not category_path:
        if not category_id:
            raise HTTPException(
                status_code=HTTPStatus.FORBIDDEN, detail="User not authorized for the requested category path"
            )

        # Check for category_id and make a path out of it
        category_id = validate_user_category_id(user, category_id)
        if not category_id:
            raise HTTPException(status_code=HTTPStatus.FORBIDDEN, detail="User is not linked to this category")

        category_path = retrieve_category_path_from_category_id(user, category_id)

    return category_path


def _get_category_id_from_path(category_path: str) -> int:
    """
    It gets the category id from the category path.
    If the category path has the Industrial path in the beginning, it considers the second part of the path.
    It removes the language id from the path and returns the last part which represents the category id.
    E.g.: "10008604-10007411". "1000" is the language id and 8604 is the Industrial ID.
          It will return then "7411".
    """
    category_path_chunks = category_path.split("-")
    if not category_path_chunks[0].endswith(str(SyncforceCategory.INDUSTRIAL.value)):
        category_id = category_path_chunks[0][4:]
    elif len(category_path_chunks) >= 2:
        category_id = category_path_chunks[1][4:]
    else:
        _logger.error(f"Category path {category_path} doesn't have enough information to retrieve a category id")
        raise RuntimeError(f"Category path {category_path} doesn't have enough information to retrieve a category id")

    return int(category_id)
