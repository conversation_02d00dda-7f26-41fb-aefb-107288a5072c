import uuid
from datetime import datetime as dt
from http import HTTPStatus
from typing import Annotated
from uuid import UUID

from fastapi import APIR<PERSON>er, Depends, HTTPException
from fastapi.params import Path
from fastapi.responses import Response

from holmatro_customer_portal.api.dependencies import get_catalog_repository
from holmatro_customer_portal.database.enums import AssetSubTypeId, AssetType
from holmatro_customer_portal.database.models import Category, Configuration, User
from holmatro_customer_portal.domain.repositories.catalog_repository import CatalogRepository
from holmatro_customer_portal.mappers.configurator_mapper import ConfiguratorMapper
from holmatro_customer_portal.schemas.configurator import (
    ConfigurationInitReq,
    ConfigurationRes,
    ConfigurationStateReq,
    ConfigurationStateRes,
)
from holmatro_customer_portal.services.configurator.get_documents import get_merged_documents
from holmatro_customer_portal.services.configurator.get_quotation import get_quotation
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState, get_initial_state
from holmatro_customer_portal.services.configurator.workflow import Configurator, ConfiguratorFactory
from holmatro_customer_portal.utils.auth.auth import get_jwt_token, get_user
from holmatro_customer_portal.utils.database import Session, get_db
from holmatro_customer_portal.utils.my_hm.my_hm_client_factory import MyHolmatroClientFactory

router = APIRouter()


@router.post(
    "/initiate",
    summary="Initiate a configuration session.",
    description="Initiate a configuration session.",
)
def configuration_initiate(
    request: ConfigurationInitReq,
    db: Session = Depends(get_db),
    user: User = Depends(get_user),
) -> ConfigurationStateRes:
    category: Category = _validate_and_get_category(request.category, user, db)

    db.add(
        configuration := Configuration(
            user=user,
            reference=request.reference,
            quotation_number=f"HIE-{dt.utcnow().strftime('%Y%m%d%H%M')}",
            last_transition="next",
            category=category,
        )
    )
    db.commit()

    mapper = ConfiguratorMapper(db)
    initial_state = get_initial_state(str(category.category_id))

    return ConfigurationStateRes(
        configuration_id=str(configuration.id),
        category_id=int(configuration.category.category_id) if configuration.category.category_id else 0,
        state_definition=Configurator.load(initial_state),
        configuration_data=mapper.collect_configuration_data(configuration, initial_state),
    )


@router.post(
    "/next",
    summary="Go to next state.",
    description="Go to the next state of the configuration.",
)
def configuration_next(
    request: ConfigurationStateReq,
    catalog_repository: Annotated[CatalogRepository, Depends(get_catalog_repository)],
    db: Session = Depends(get_db),
    user: User = Depends(get_user),
    jwt_token: str = Depends(get_jwt_token),
) -> ConfigurationStateRes:
    my_hm_client = MyHolmatroClientFactory.create_client(jwt_token)
    configuration: Configuration | None = db.query(Configuration).get(UUID(request.configuration_id))
    if not configuration or configuration.user != user:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Configuration not found")

    factory = ConfiguratorFactory()
    configurator: Configurator = factory.get_configurator_for_state(
        db, ConfiguratorState(request.state_id), configuration, my_hm_client, catalog_repository
    )

    last_transition = "next"
    _store_configuration(db, configurator.state_definition, configuration, request, last_transition)

    configurator.next()  # type: ignore

    mapper = ConfiguratorMapper(db)

    return ConfigurationStateRes(
        configuration_id=str(configuration.id),
        category_id=int(configuration.category.category_id) if configuration.category.category_id else 0,
        state_definition=configurator.state_definition,
        configuration_data=mapper.collect_configuration_data(configuration, ConfiguratorState(configurator.state)),
        products=mapper.collect_configuration_product(
            configuration, ConfiguratorState(configurator.state), my_hm_client
        ),
    )


@router.post(
    "/back",
    summary="Go to previous state.",
    description="Go to the previous state of the configuration.",
)
def configuration_back(
    request: ConfigurationStateReq,
    catalog_repository: Annotated[CatalogRepository, Depends(get_catalog_repository)],
    db: Session = Depends(get_db),
    user: User = Depends(get_user),
    jwt_token: str = Depends(get_jwt_token),
) -> ConfigurationStateRes:
    my_hm_client = MyHolmatroClientFactory.create_client(jwt_token)
    configuration: Configuration | None = db.query(Configuration).get(UUID(request.configuration_id))
    if not configuration or configuration.user != user:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Configuration not found")

    """ Enforce that no data is saved when go back from state """
    request.configuration_data = []
    request.products = []

    factory = ConfiguratorFactory()
    configurator: Configurator = factory.get_configurator_for_state(
        db, ConfiguratorState(request.state_id), configuration, my_hm_client, catalog_repository
    )

    last_transition = "back"
    _store_configuration(db, configurator.state_definition, configuration, request, last_transition)

    configurator.back()  # type: ignore

    mapper = ConfiguratorMapper(db)
    return ConfigurationStateRes(
        configuration_id=str(configuration.id),
        category_id=int(configuration.category.category_id) if configuration.category.category_id else 0,
        state_definition=configurator.state_definition,
        configuration_data=mapper.collect_configuration_data(configuration, ConfiguratorState(configurator.state)),
        products=mapper.collect_configuration_product(
            configuration, ConfiguratorState(configurator.state), my_hm_client
        ),
    )


@router.post(
    "/skip",
    summary="Skip this stage.",
    description="Continue at the next stage.",
)
def configuration_skip(
    request: ConfigurationStateReq,
    catalog_repository: Annotated[CatalogRepository, Depends(get_catalog_repository)],
    db: Session = Depends(get_db),
    user: User = Depends(get_user),
    jwt_token: str = Depends(get_jwt_token),
) -> ConfigurationStateRes:
    my_hm_client = MyHolmatroClientFactory.create_client(jwt_token)

    configuration = _clear_state(db, user, request, last_transition="skip")

    factory = ConfiguratorFactory()
    configurator: Configurator = factory.get_configurator_for_state(
        db, ConfiguratorState(request.state_id), configuration, my_hm_client, catalog_repository
    )
    configurator.skip()  # type: ignore

    mapper = ConfiguratorMapper(db)

    return ConfigurationStateRes(
        configuration_id=str(configuration.id),
        category_id=int(configuration.category.category_id) if configuration.category.category_id else 0,
        state_definition=configurator.state_definition,
        configuration_data=mapper.collect_configuration_data(configuration, ConfiguratorState(configurator.state)),
        products=mapper.collect_configuration_product(
            configuration, ConfiguratorState(configurator.state), my_hm_client
        ),
    )


@router.get(
    "/configurations",
    summary="Get all configurations.",
    description="Get all configurations for the logged in user.",
)
def configurations_get(db: Session = Depends(get_db), user: User = Depends(get_user)) -> list[ConfigurationRes]:
    mapper = ConfiguratorMapper(db)
    return mapper.collect_configuration_overview(user)


@router.get(
    "/spec_sheets/{configuration_id}",
    summary="Get all spec sheets of a configuration.",
    description="Get all the spec sheets from the products used in the configuration",
    response_class=Response(media_type="application/pdf"),  # type: ignore
)
def spec_sheets_get(
    configuration_id: Annotated[UUID, Path(description="The configuration id")],
    db: Session = Depends(get_db),
    user: User = Depends(get_user),
) -> Response:
    configuration: Configuration | None = db.query(Configuration).get(configuration_id)
    if not configuration or configuration.user != user:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Configuration not found")

    try:
        pdf_stream = get_merged_documents(
            configuration_id=configuration_id,
            db=db,
            language_id=user.language.id,
            system_type=user.system_type,
            asset_type=AssetType.PRODUCT_TECHNICAL_SPECIFICATION_SHEET,
            asset_sub_type_id=AssetSubTypeId.TECHNICAL_SPECIFICATION_SHEET,
        )
    except HTTPException as e:
        raise e
    if pdf_stream:
        response = Response(pdf_stream, media_type="application/pdf")
        response.headers["Content-Disposition"] = (
            f"attachment; filename=HIE-product-specifications-{dt.now().strftime('%Y-%m-%d')}.pdf"
        )
        return response

    else:
        raise HTTPException(status_code=404, detail="No specsheets found")


@router.get(
    "/service_manuals/{configuration_id}",
    summary="Get all service manuals for a configuration.",
    description="Get the service manuals (maintenance and safety sheets) for products selected in the configuration",
    response_class=Response(media_type="application/pdf"),  # type: ignore
)
def get_service_manuals(
    configuration_id: Annotated[UUID, Path(description="The configuration id")],
    db: Session = Depends(get_db),
    user: User = Depends(get_user),
) -> Response:
    configuration: Configuration | None = db.query(Configuration).get(configuration_id)
    if not configuration or configuration.user != user:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Configuration not found")

    try:
        pdf_stream = get_merged_documents(
            configuration_id=configuration_id,
            db=db,
            language_id=user.language.id,
            asset_type=AssetType.PRODUCT_GUIDE,
            asset_sub_type_id=AssetSubTypeId.SERVICE_MANUAL,
        )
    except HTTPException as e:
        raise e
    if pdf_stream:
        response = Response(pdf_stream, media_type="application/pdf")
        response.headers["Content-Disposition"] = (
            f"attachment; filename=HIE-product-service-manuals-{dt.now().strftime('%Y-%m-%d')}.pdf"
        )
        return response

    else:
        raise HTTPException(status_code=404, detail="No service manuals found")


@router.get(
    "/quotation/{configuration_id}",
    summary="Get the quotation of the configuration. ",
    description="Get the quotation of the configuration.",
    response_class=Response(media_type="application/pdf"),  # type: ignore
)
def quotation_get(
    configuration_id: Annotated[UUID, Path(description="The configuration id")],
    db: Session = Depends(get_db),
    user: User = Depends(get_user),
    jwt_token: str = Depends(get_jwt_token),
) -> Response:
    my_hm_client = MyHolmatroClientFactory.create_client(jwt_token)

    configuration: Configuration | None = db.query(Configuration).get(configuration_id)
    if not configuration or configuration.user != user:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Configuration not found")

    quotation = get_quotation(configuration, my_hm_client, user)

    # Replace this with the actual logic to get your URLs
    response = Response(quotation, media_type="application/pdf")
    response.headers["Content-Disposition"] = f"attachment; filename={configuration.quotation_number}.pdf"
    return response


@router.get(
    "/overview/{configuration_id}",
    summary="Get the configuration overview document.",
    description="Public endpoint meant for retrieving configuration overviews created by anonymous users.",
    response_class=Response(media_type="application/pdf"),  # type: ignore
)
def quotation_get_public(
    configuration_id: Annotated[UUID, Path(description="The configuration id")],
    db: Session = Depends(get_db),
) -> Response:
    my_hm_client = MyHolmatroClientFactory.create_client(token=None)

    configuration: Configuration | None = db.query(Configuration).get(configuration_id)

    if not configuration:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Configuration not found")

    if not configuration.user.anonymous:
        raise HTTPException(
            status_code=HTTPStatus.FORBIDDEN, detail="Not allowed for configuratinos from non-anonymous users"
        )

    quotation = get_quotation(configuration, my_hm_client, configuration.user)

    response = Response(quotation, media_type="application/pdf")
    response.headers["Content-Disposition"] = f"attachment; filename={configuration.quotation_number}.pdf"
    return response


@router.get(
    "/configuration/{configuration_id}",
    summary="Get a specific configuration.",
    description="Get a specific configuration for the logged in user.",
)
def get_configuration(
    configuration_id: Annotated[uuid.UUID, Path(description="The configuration id")],
    catalog_repository: Annotated[CatalogRepository, Depends(get_catalog_repository)],
    db: Session = Depends(get_db),
    user: User = Depends(get_user),
    jwt_token: str = Depends(get_jwt_token),
) -> ConfigurationStateRes:
    my_hm_client = MyHolmatroClientFactory.create_client(jwt_token)
    configuration: Configuration | None = db.query(Configuration).get(configuration_id)
    if not configuration or configuration.user != user:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Configuration not found")

    mapper = ConfiguratorMapper(db)

    if not configuration.last_updated_state:
        initial_state = get_initial_state(configuration.category.category_id)
        return ConfigurationStateRes(
            configuration_id=str(configuration.id),
            category_id=int(configuration.category.category_id) if configuration.category.category_id else 0,
            state_definition=Configurator.load(initial_state),
            configuration_data=mapper.collect_configuration_data(configuration, initial_state),
        )

    factory = ConfiguratorFactory()
    configurator: Configurator = factory.get_configurator_for_state(
        db, ConfiguratorState(configuration.last_updated_state), configuration, my_hm_client, catalog_repository
    )

    match configuration.last_transition:
        case "next":
            configurator.next()  # type: ignore
        case "back":
            configurator.back()  # type: ignore
        case "skip":
            configurator.skip()  # type: ignore

    return ConfigurationStateRes(
        configuration_id=str(configuration.id),
        category_id=int(configuration.category.category_id) if configuration.category.category_id else 0,
        state_definition=configurator.state_definition,
        configuration_data=mapper.collect_configuration_data(configuration, ConfiguratorState(configurator.state)),
        products=mapper.collect_configuration_product(
            configuration, ConfiguratorState(configurator.state), my_hm_client
        ),
    )


@router.delete(
    "/configuration/{configuration_id}",
    summary="Remove a specific configuration.",
    description="Remove a specific configuration for the logged in user.",
    status_code=HTTPStatus.NO_CONTENT,
)
def hide_configuration(
    configuration_id: Annotated[uuid.UUID, Path(description="The configuration id")],
    db: Session = Depends(get_db),
    user: User = Depends(get_user),
) -> None:
    configuration: Configuration | None = db.query(Configuration).get(configuration_id)
    if not configuration or configuration.user != user:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Configuration not found")

    configuration.hidden = True  # type: ignore
    db.merge(configuration)
    db.commit()


def _store_configuration(
    db: Session,
    state_definition: dict,
    configuration: Configuration,
    request: ConfigurationStateReq,
    last_transition: str,
) -> Configuration:
    _validate_max_products_selection(state_definition, request)

    configuration.last_updated_state = request.state_id  # type: ignore
    configuration.last_transition = last_transition  # type: ignore

    mapper = ConfiguratorMapper(db)
    db.add_all(mapper.store_configuration_data(configuration, request))
    if products := mapper.store_configuration_product(configuration, request):
        db.add_all(products)
    db.commit()
    return configuration


def _clear_state(db: Session, user: User, request: ConfigurationStateReq, last_transition: str) -> Configuration:
    configuration: Configuration | None = db.query(Configuration).get(UUID(request.configuration_id))
    if not configuration or configuration.user != user:
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Configuration not found")

    """ Remove, if any, products and configuration data in this state """
    mapper = ConfiguratorMapper(db)
    mapper.clear_configuration_data_for_state(request)
    mapper.clear_configuration_product_for_state(request)

    """ Store only state and transition when state is skipped """
    configuration.last_updated_state = request.state_id  # type: ignore
    configuration.last_transition = last_transition  # type: ignore

    db.commit()
    return configuration


def _validate_max_products_selection(state_definition: dict, request: ConfigurationStateReq) -> None:
    """It validates if state definition accepts more than one product"""
    try:
        multi_select = next(
            content["meta"].get("multi_select")
            for content in state_definition["content"]
            if content.get("meta") and content.get("type") == "products"
        )
    except StopIteration:
        """If not found, default False product will be applied"""
        multi_select = False

    if not multi_select and len(request.products) > 1:
        raise HTTPException(
            status_code=HTTPStatus.BAD_REQUEST,
            detail=f"Not allowed to select more than one product",
        )


def _validate_and_get_category(category_id: int | None, user: User, db: Session) -> Category:
    """Verify if category_id is valid for the user, if so, return the category object."""
    if isinstance(category_id, int) and str(category_id) not in [
        str(category.category_id) for category in user.categories
    ]:
        raise HTTPException(
            status_code=HTTPStatus.BAD_REQUEST,
            detail="Category is not valid for the user",
        )

    if category_id is None and not len(user.categories) == 1:
        raise HTTPException(
            status_code=HTTPStatus.BAD_REQUEST,
            detail="Category is required when user has multiple categories",
        )

    if category_id:
        categories: list[Category] = db.query(Category).filter(Category.category_id == str(category_id)).all()
        if len(categories) == 1:
            return categories[0]
        raise HTTPException(
            status_code=HTTPStatus.BAD_REQUEST,
            detail="Category is not valid or not unique",
        )

    category: Category = user.categories[0]
    return category
