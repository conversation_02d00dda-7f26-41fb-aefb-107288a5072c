from celery import Celery  # type: ignore

from holmatro_customer_portal.utils.env import Env

celery_app = Celery(
    "worker",
    broker=Env.RABBITMQ_BROKER_URL.get(),  # Default RabbitMQ user and password
    backend="rpc://",  # or use your preferred result backend
    include=["holmatro_customer_portal.services.import_product"],  # e.g., 'holmatro_customer_portal.tasks'
)

celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
)
