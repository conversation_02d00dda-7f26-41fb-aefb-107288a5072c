import sentry_sdk
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.responses import JSONResponse

from holmatro_customer_portal.api import auth, configurator, imports, products, user  # Import your API routers
from holmatro_customer_portal.utils.database import construct_db_write_url, session_creator
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.middleware.db_session import DbSessionMiddleware
from holmatro_customer_portal.utils.middleware.request_timeout import RequestTimeoutMiddleware

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())

sentry_url = Env.SENTRY_URL_INCL_TOKEN.get()
if sentry_url:
    sentry_sdk.init(
        dsn=sentry_url,
        traces_sample_rate=Env.SENTRY_TRACING_SAMPLE_RATE.get(),
        profiles_sample_rate=Env.SENTRY_PROFILES_SAMPLE_RATE.get(),
        environment=Env.SENTRY_ENV.get(),
    )

app = FastAPI(
    title="Holmatro Customer Portal",
    description="API for the Holmatro Customer Portal",
    version="1.0.0",
)


# Middleware that provides a db session to the request handlers
app.add_middleware(DbSessionMiddleware, session_creator=session_creator(construct_db_write_url()))

# Middleware that stops any pending request that takes too long, and returns a 504 error response
app.add_middleware(RequestTimeoutMiddleware, timeout_seconds=float(Env.REQUEST_TIMEOUT_SECONDS.get()))

# Include your API routers
app.include_router(imports.router, prefix="/data/imports", tags=["imports"])
app.include_router(products.router, prefix="/data/products", tags=["products"])
app.include_router(auth.router, prefix="/data/auth", tags=["auth"])
app.include_router(configurator.router, prefix="/data/configurator", tags=["configurator"])
app.include_router(user.router, prefix="/data/user", tags=["user"])

if __name__ == "__main__":
    import uvicorn

    if Env.STAGE.get() == "local":
        uvicorn.run(
            "holmatro_customer_portal.main:app", host="0.0.0.0", port=Env.UVICORN_PORT.get(), reload=True  # nosec B104
        )
    else:
        uvicorn.run(app, host=["::", "0.0.0.0"], port=Env.UVICORN_PORT.get())  # type: ignore[arg-type] #nosec B104
