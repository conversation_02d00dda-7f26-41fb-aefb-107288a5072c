from sqlalchemy.orm import Session

from holmatro_customer_portal.database.enums import SystemType
from holmatro_customer_portal.database.models import (
    Category,
    DBCategoryFilterTemplateAssociation,
    DBFilter,
    DBFilterTemplate,
)
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class FilterConfigurationService:

    def __init__(self, session: Session):
        self.session = session

    def get_filters_for_category(self, category_id_str: str, system_type: SystemType) -> list[DBFilter]:
        """
        Get all DBFilter objects for a given category and system type.

        This method replaces both get_allowed_attribute_ids and get_filter_configuration_map
        by returning the complete DBFilter objects, from which both attribute IDs and
        configuration details can be extracted.
        """
        try:
            # First find the category UUID by the category_id string
            category = self.session.query(Category).filter(Category.category_id == str(category_id_str)).one_or_none()
            if not category:
                _logger.debug(f"No category found with category_id {category_id_str}")
                return []

            # Query to get all filters for the category and system type
            query = (
                self.session.query(DBFilter)
                .join(DBFilterTemplate)
                .join(DBCategoryFilterTemplateAssociation)
                .filter(
                    DBCategoryFilterTemplateAssociation.category_id == category.id,
                    DBFilterTemplate.system_type == system_type,
                    DBFilter.is_visible == True,
                )
                .order_by(DBFilter.position)
            )

            return query.all()

        except Exception as e:
            _logger.error(f"Error getting filters for category {category_id_str}: {e}")
            return []
