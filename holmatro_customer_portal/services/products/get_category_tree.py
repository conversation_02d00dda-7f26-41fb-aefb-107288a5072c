from holmatro_customer_portal.database.models import User
from holmatro_customer_portal.domain.repositories.catalog_repository import CatalogRepository
from holmatro_customer_portal.services.get_categories import add_image_url_to_category
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.enums import language_values
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import Category as TweakwiseCategory


def get_category_tree_handle(
    db: Session, catalog_repository: CatalogRepository, user: User, category_id: int | None
) -> list[TweakwiseCategory]:
    category_tree: list[TweakwiseCategory] = []

    # Check if not a specific category is required, fetch only the category trees linked to the user
    if not category_id:
        language_tweakwise_id = language_values[user.language.code.upper()]
        for user_category in user.categories:
            cat_id = f"{language_tweakwise_id}{user_category.category_id}"
            category_child = catalog_repository.get_category_tree(int(cat_id))
            category_tree.append(category_child)

    # Fetch specific category
    else:
        category_child = catalog_repository.get_category_tree(category_id)
        category_tree.append(category_child)

    for tweakwise_category in category_tree:
        _remove_helper_categories(tweakwise_category)
        add_image_url_to_category(tweakwise_category, str(tweakwise_category.category_id), db)
    return category_tree


def _remove_helper_categories(category_tree: TweakwiseCategory) -> None:
    # In the proposition there are "helper" categories.
    # They contain related/associated products which might not be entirely associated to the main category,
    # but to a specific product only.
    # The "helper" categories are filtered out, so they don't appear to the user
    category_tree.children = [category for category in category_tree.children if not category.title.startswith("#")]
