from sqlalchemy.engine import Row

from holmatro_customer_portal.database.models import User
from holmatro_customer_portal.schemas.response_schema import ParamsReq
from holmatro_customer_portal.services.utils.product_queries import (
    get_product_preview_by_article_number,
    get_query_products,
)
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.transforms import validate_and_transform_article_numbers
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import Navigation, ProductsResponse, ResponseProperties

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


def get_db_products(
    product_response: Navigation | ProductsResponse,
    user: User,
    db: Session,
    base_category_id: int | None,
    sub_category_id: int | None = None,
) -> list:
    """It fetches the products and add the redirect products, if any, to the result"""

    redirects_article_numbers = validate_and_transform_article_numbers(
        [redirect.article_number for redirect in product_response.redirects]
    )
    if redirects_article_numbers:
        query_result = get_product_preview_by_article_number(redirects_article_numbers, user, db, base_category_id)
    else:
        query_result = get_query_products(product_response.items, db, user, sub_category_id)

    if not query_result:
        _logger.debug(f"Products not found in the database")

    return query_result


def parse_to_paginated_results(products: list[Row], params: ParamsReq) -> tuple[list, ResponseProperties]:
    page_grouped_products = _split_products_in_pages(products, params.products_per_page)

    properties = ResponseProperties(
        no_of_items=len(products),
        page_size=params.products_per_page,
        no_of_pages=len(page_grouped_products),
        current_page=params.page_number,
        category_id=params.category_id,
    )

    products_page = (
        page_grouped_products[properties.current_page - 1]
        if len(page_grouped_products) >= properties.current_page
        else []
    )

    return products_page, properties


def _split_products_in_pages(products: list[Row], page_size: int) -> list:
    return [products[i : i + page_size] for i in range(0, len(products), page_size)]
