from uuid import UUID

from sqlalchemy import or_

from holmatro_customer_portal.database.enums import LanguageEnum, RemovedItems
from holmatro_customer_portal.database.models import Attribute, AttributeTranslation, Product, User
from holmatro_customer_portal.schemas.response_schema import AttributeRes, GroupedAttributeRes
from holmatro_customer_portal.services.utils.article_number_translations import article_number_translations
from holmatro_customer_portal.utils.database import Session


def get_attributes(session: Session, product_id: UUID, user: User, article_number: str) -> list[GroupedAttributeRes]:
    attributes_with_translations = (
        session.query(Attribute, AttributeTranslation)
        .join(AttributeTranslation, Attribute.translations)
        .filter(Attribute.product_id == product_id)
        .filter(AttributeTranslation.language_id == user.language.id)
        .filter(or_(Attribute.system_type == user.system_type, Attribute.system_type == None))
        .order_by(Attribute.attribute_id.asc())
        .all()
    )
    grouped_results: dict = {}

    for attribute, translation in attributes_with_translations:
        if (
            (not attribute.content and not translation.attribute_value)
            or "(T)" in translation.name
            or attribute.attribute_id in RemovedItems.ATTRIBUTES.value
        ):
            continue
        attribute_response = extract_attribute_response(attribute, translation)
        if not attribute_response:
            continue

        if not grouped_results.get(translation.attribute_group_name):
            grouped_results[translation.attribute_group_name] = {
                "translations": {translation.name: attribute_response},
                "group_id": attribute.attribute_group_id,
            }
        else:
            if found_attribute := grouped_results[translation.attribute_group_name].get(translation.name):
                attribute_response.content = found_attribute.content + " / " + attribute_response.content

            grouped_results[translation.attribute_group_name]["translations"][translation.name] = attribute_response

    grouped_attributes = [
        GroupedAttributeRes(
            group=group,
            group_id=attributes.get("group_id"),
            grouped_attributes=[attribute for name, attribute in attributes.get("translations").items()],
        )
        for group, attributes in grouped_results.items()
    ]
    article_number_translation: dict = article_number_translations[LanguageEnum(user.language.code)]
    article_number_translation["grouped_attributes"][0]["content"] = article_number

    sorted_attributes: list = sorted(grouped_attributes, key=lambda x: x.group_id)
    sorted_attributes.insert(0, (GroupedAttributeRes(**article_number_translation)))

    return sorted_attributes


def extract_attribute_response(attribute: Attribute, translation: AttributeTranslation) -> AttributeRes | None:
    content = (
        translation.attribute_value
        if attribute.multi_lingual_values and translation.attribute_value is not None
        else attribute.content
    )

    content_with_unit_type = (
        (content or "") + " " + translation.attribute_unit if translation.attribute_unit is not None else content
    )
    if not content_with_unit_type:
        return None

    return AttributeRes(content=str(content_with_unit_type), name=str(translation.name))


def get_product_preview_attributes(
    attribute_ids: list[str], user: User, session: Session, article_number: str
) -> list[AttributeRes]:
    query_results = (
        session.query(Attribute, AttributeTranslation)
        .join(Product, Attribute.product_id == Product.id)  # Join with Product table
        .join(AttributeTranslation, Attribute.translations)
        .filter(Product.article_number == article_number)  # Filter by article_number
        .filter(Attribute.attribute_id.in_(attribute_ids))
        .filter(AttributeTranslation.language_id == user.language.id)
        .filter(or_(Attribute.system_type == user.system_type, Attribute.system_type == None))
        .order_by(Attribute.attribute_id)
        .all()
    )
    return [
        result
        for attribute, translation in query_results
        if (result := extract_attribute_response(attribute, translation)) is not None
    ]
