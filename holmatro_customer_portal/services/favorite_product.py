import uuid
from http import HTTPStatus

from fastapi import HTTPException

from holmatro_customer_portal.database.models import Product, User
from holmatro_customer_portal.mappers.product_preview_mapper import ProductPreviewMapper
from holmatro_customer_portal.schemas.response_schema import ProductPreviewRes
from holmatro_customer_portal.services.utils.product_queries import get_product_preview_by_ids
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.my_hm.my_hm_interface import MyHolmatroClientInterface

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


def add_favorite_product_handle(product_id: uuid.UUID, user: User, db: Session) -> None:
    product = db.query(Product).get(product_id)

    if not product:
        _logger.warning(f"Product({product_id}) not found")
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Product not found")
    elif product not in user.favorite_products:
        user.favorite_products.append(product)
        db.commit()


def remove_favorite_product_handle(product_id: uuid.UUID, user: User, db: Session) -> None:
    product = db.query(Product).get(product_id)

    if not product:
        _logger.warning(f"Product({product_id}) not found")
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Product not found")

    elif product in user.favorite_products:
        user.favorite_products.remove(product)
        db.commit()


def get_favorite_products_handle(
    user: User, db: Session, my_hm_client: MyHolmatroClientInterface
) -> list[ProductPreviewRes]:
    product_ids: list = [product.id for product in user.favorite_products if user.favorite_products]
    if not product_ids:
        return []

    products = get_product_preview_by_ids(product_ids, user, db)

    mapper = ProductPreviewMapper(db, my_hm_client)
    return mapper.product_preview_mapper(user, products)
