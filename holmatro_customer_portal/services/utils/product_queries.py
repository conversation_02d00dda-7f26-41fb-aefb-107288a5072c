from uuid import UUID

from sqlalchemy import and_, case, func, literal
from sqlalchemy.engine.row import Row
from sqlalchemy.sql import select

from holmatro_customer_portal.database.enums import AssetType
from holmatro_customer_portal.database.models import (
    Asset,
    AssetResource,
    Category,
    CategoryAssociation,
    Classification,
    Configuration,
    ConfigurationProduct,
    Product,
    ProductNameTranslation,
    ProductText,
    ProductTextTranslation,
    User,
    category_user_association_table,
)
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


def get_product_preview_by_article_number(
    product_article_numbers: list[str] | str,
    user: User,
    db: Session,
    category_id: int | None = None,
) -> list[Row]:
    """
    Queries the products by article number.
    This query is used within the configurator, to fetch a default product which there is the article number available.
    """
    if isinstance(product_article_numbers, str):
        product_article_numbers = [product_article_numbers]

    base_query = db.query(Category).join(
        category_user_association_table,
        and_(
            category_user_association_table.c.category_id == Category.id,
            category_user_association_table.c.user_id == user.id,
        ),
    )

    if category_id:
        category_subquery = base_query.filter(Category.category_id == str(category_id)).subquery()
    else:
        category_subquery = base_query.subquery()

    query_result: list[Row] = (
        db.query(
            Product.id.label("product_id"),
            Product.article_number.label("product_article_number"),
            ProductNameTranslation.value.label("product_name"),
            AssetResource.url.label("asset_url"),
            AssetResource.file_name.label("asset_file_name"),
            category_subquery.c.category_id.label("category_id"),
        )
        .join(
            ProductNameTranslation,
            and_(
                ProductNameTranslation.product_id == Product.id, ProductNameTranslation.language_id == user.language.id
            ),
        )
        .join(
            Asset,
            and_(
                Asset.product_id == Product.id,
                Asset.asset_type == AssetType.PRODUCT_MAIN_IMAGE,
            ),
        )
        .join(AssetResource, and_(Asset.id == AssetResource.asset_id, AssetResource.asset_type == "HIGHRESOLUTION"))
        .join(
            CategoryAssociation,
            Product.id == CategoryAssociation.product_id,
        )
        .join(category_subquery)
        .filter(Product.article_number.in_(product_article_numbers))
    ).all()

    if not query_result:
        _logger.debug(f"Products {product_article_numbers} not found in the database")
        return []

    return query_result


def get_product_preview_by_ids(product_ids: list[str], user: User, db: Session) -> list[Row]:
    """
    Queries the product by database id and returns it mapped.
    This query is used "internally" (favorite_product and related products in 'product_details'),
    when there is a product, therefore we already have its id.
    """
    query_result = (
        db.query(
            Product.id.label("product_id"),
            Product.article_number.label("product_article_number"),
            ProductNameTranslation.value.label("product_name"),
            AssetResource.url.label("asset_url"),
            AssetResource.file_name.label("asset_file_name"),
            Classification.classification_id.label("product_classification_id"),
        )
        .join(
            ProductNameTranslation,
            and_(
                ProductNameTranslation.product_id == Product.id, ProductNameTranslation.language_id == user.language.id
            ),
        )
        .join(Classification, Classification.id == Product.classification_id)
        .join(
            Asset,
            and_(
                Asset.product_id == Product.id,
                Asset.asset_type == AssetType.PRODUCT_MAIN_IMAGE,
            ),
        )
        .join(AssetResource, and_(Asset.id == AssetResource.asset_id, AssetResource.asset_type == "HIGHRESOLUTION"))
        .filter(Product.id.in_(product_ids))
    ).all()

    if not query_result:
        _logger.debug(f"Products {product_ids} not found in the database")
        return []

    return query_result  # type: ignore


def get_query_products(products: list, db: Session, user: User, category_id: int | None = None) -> list[Row]:
    """
    This query is used in products and search endpoints. Queries the product by product_id.
    product_id is an identifier between the products in Syncforce and Tweakwise. It is different from only 'id'.
    It returns the products ordered according to CategoryAssociation.product_sort_order.
    """
    product_item_numbers = [int(product.item_no.split("-")[-1]) for product in products]

    query = (
        db.query(
            Product.id.label("product_id"),
            Product.product_id.label("tweakwise_id"),
            Product.article_number.label("product_article_number"),
            ProductNameTranslation.value.label("product_name"),
            AssetResource.url.label("asset_url"),
            AssetResource.file_name.label("asset_file_name"),
            Category.category_id.label("category_id"),
        )
        .join(
            ProductNameTranslation,
            and_(
                ProductNameTranslation.product_id == Product.id, ProductNameTranslation.language_id == user.language.id
            ),
        )
        .join(
            Asset,
            and_(
                Asset.product_id == Product.id,
                Asset.asset_type == AssetType.PRODUCT_MAIN_IMAGE,
            ),
            isouter=True,
        )
        .join(
            AssetResource,
            and_(Asset.id == AssetResource.asset_id, AssetResource.asset_type == "HIGHRESOLUTION"),
            isouter=True,
        )
        .join(CategoryAssociation)
        .join(Category)
        .filter(Product.product_id.in_(product_item_numbers))
        .filter(CategoryAssociation.product_sort_order.is_not(None))
        .group_by(
            Product.id,
            Product.product_id,
            Product.article_number,
            ProductNameTranslation.value,
            AssetResource.url,
            AssetResource.file_name,
            Category.category_id,
        )
    )

    # Only apply category filter if category_id is provided
    if category_id is not None:
        query = query.filter(Category.category_id == category_id)

    query_result: list[Row] = query.order_by(
        func.min(
            case(
                (CategoryAssociation.product_sort_order.is_(None), 999999), else_=CategoryAssociation.product_sort_order
            )
        ).asc()
    ).all()

    return query_result


def get_configurator_products(product_ids: list[str], configuration: Configuration, db: Session) -> list[Row]:
    """
    This query is used in the configurator overview. It joins the ConfigurationProduct model and its quantity.
    """

    query_result = (
        db.query(
            Product.id.label("product_id"),
            func.sum(ConfigurationProduct.quantity).label("quantity"),
            Product.article_number.label("product_article_number"),
            ProductNameTranslation.value.label("product_name"),
            AssetResource.url.label("asset_url"),
            AssetResource.file_name.label("asset_file_name"),
        )
        .join(
            ConfigurationProduct,
            and_(
                ConfigurationProduct.product_id == Product.id, ConfigurationProduct.configuration_id == configuration.id
            ),
        )
        .join(
            ProductNameTranslation,
            and_(
                ProductNameTranslation.product_id == Product.id,
                ProductNameTranslation.language_id == configuration.user.language.id,
            ),
        )
        .join(
            Asset,
            and_(
                Asset.product_id == Product.id,
                Asset.asset_type == AssetType.PRODUCT_MAIN_IMAGE,
            ),
        )
        .join(AssetResource, and_(Asset.id == AssetResource.asset_id, AssetResource.asset_type == "HIGHRESOLUTION"))
        .group_by(
            ConfigurationProduct.product_id,
            Product.article_number,
            ProductNameTranslation.value,
            AssetResource.url,
            AssetResource.file_name,
        )
        .filter(Product.id.in_(product_ids))
    ).all()

    if not query_result:
        _logger.debug(f"Products {product_ids} not found in the database")
        return []

    return query_result  # type: ignore


def get_autocomplete_products(product_item_numbers: list[int], user: User, db: Session) -> list[Row]:
    query_result: list[Row] = (
        db.query(
            Product.id.label("product_id"),
            Product.product_id.label("product_product_id"),
            ProductNameTranslation.value.label("product_name"),
            Category.category_id.label("category_id"),
        )
        .join(
            ProductNameTranslation,
            and_(
                ProductNameTranslation.product_id == Product.id, ProductNameTranslation.language_id == user.language.id
            ),
        )
        .join(
            CategoryAssociation,
            Product.id == CategoryAssociation.product_id,
        )
        .join(
            category_user_association_table,
            and_(
                category_user_association_table.c.category_id == CategoryAssociation.category_id,
                category_user_association_table.c.user_id == user.id,
            ),
        )
        .join(Category, Category.id == category_user_association_table.c.category_id)
        .filter(Product.product_id.in_(product_item_numbers))
        .all()
    )

    # Return the results in the same order as the provided product_item_numbers list
    ordered_products = sorted(query_result, key=lambda itm: product_item_numbers.index(itm.product_product_id))
    return ordered_products


def get_product_details(db: Session, user: User, product_id: UUID) -> list[Row]:
    # Subquery makes sure to fetch only one category, if more than one exists and avoid duplicate descriptions

    query_result: list[Row] = (
        db.query(
            Product,
            ProductTextTranslation,
            ProductNameTranslation.value.label("product_name"),
        )
        .join(ProductText, ProductText.product_id == Product.id, isouter=True)
        .join(
            ProductTextTranslation,
            and_(
                ProductTextTranslation.product_text_id == ProductText.id,
                ProductTextTranslation.language_id == user.language.id,
                ProductTextTranslation.value != None,
            ),
            isouter=True,
        )
        .join(
            ProductNameTranslation,
            and_(
                ProductNameTranslation.language_id == user.language.id,
                ProductNameTranslation.product_id == Product.id,
            ),
        )
        .filter(Product.id == product_id)
        .order_by(ProductTextTranslation.sort_order)
    ).all()

    return query_result
