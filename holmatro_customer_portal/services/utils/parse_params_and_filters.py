from fastapi.encoders import jsonable_encoder

from holmatro_customer_portal.schemas.response_schema import ParamsReq, SearchFiltersReq
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import RequestParams


def parse_params_and_filters(
    portal_params: ParamsReq, search_filters: SearchFiltersReq | None, default_tweakwise_params: bool = False
) -> dict:
    params = portal_params.model_copy()
    if default_tweakwise_params:
        params = set_default_tweakwise_params(params)

    tweakwise_params = RequestParams(
        tn_q=params.search_query,
        tn_cid=params.category_path,
        tn_p=params.page_number,
        tn_ps=params.products_per_page,
        tn_ft=params.filter_template_id,
    )
    parsed_params: dict = tweakwise_params.model_dump()

    if not search_filters:
        return parsed_params

    decoded_filters = jsonable_encoder(search_filters.filters)
    parse_search_filters = {
        f"tn_fk_{key}": "|".join(map(str, val)) if isinstance(val, list) else str(val)
        for key, val in decoded_filters.items()
    }

    params_result: dict = {**parsed_params, **parse_search_filters}
    return params_result


def set_default_tweakwise_params(params: ParamsReq) -> ParamsReq:
    params.page_number, params.products_per_page = 1, 1000
    return params
