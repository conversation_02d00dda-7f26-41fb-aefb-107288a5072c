from typing import Any
from uuid import UUID

from sqlalchemy import or_

from holmatro_customer_portal.database.enums import AssetType
from holmatro_customer_portal.database.models import (
    Asset,
    AssetMetadataTranslations,
    AssetResource,
    RelatedProduct,
    RelationshipTranslation,
    User,
    asset_resource_language_association_table,
)
from holmatro_customer_portal.schemas.response_schema import AssetGroupRes, AssetRes, ImageRes
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


def get_product_images(db: Session, product_id: UUID) -> list[ImageRes]:
    asset_results = (
        db.query(AssetResource.url)
        .join(Asset, Asset.id == AssetResource.asset_id)
        .filter(
            Asset.product_id == product_id,
            or_(Asset.asset_type == AssetType.PRODUCT_SHOTS, Asset.asset_type == AssetType.PRODUCT_MAIN_IMAGE),
            AssetResource.asset_type == "HIGHRESOLUTION",  # Filters out duplicated images with different types
        )
        .distinct()  # Filters out duplicated images with same url and different types
        .all()
    )

    return [ImageRes(url=asset.url, type=None) for asset in asset_results]


def get_product_assets(db: Session, product_id: UUID, language_id: UUID) -> list[AssetGroupRes]:
    asset_results = (
        db.query(
            AssetResource.url.label("url"),
            AssetResource.asset_type.label("file_type"),
            AssetResource.size.label("asset_size"),
            Asset.asset_type.label("type"),
            AssetMetadataTranslations.asset_label.label("file_name"),
            Asset.id,
        )
        .join(Asset, Asset.id == AssetResource.asset_id)
        .outerjoin(
            asset_resource_language_association_table,
            AssetResource.id == asset_resource_language_association_table.c.asset_resource_id,
        )
        .join(AssetMetadataTranslations, AssetMetadataTranslations.asset_id == Asset.id)
        .filter(
            Asset.product_id == product_id,
            or_(
                asset_resource_language_association_table.c.language_id == language_id,
                asset_resource_language_association_table.c.language_id.is_(None),
            ),
            AssetMetadataTranslations.language_id == language_id,
            Asset.asset_type.in_(
                [
                    AssetType.PRODUCT_GUIDE,
                    AssetType.PRODUCT_TECHNICAL_DRAWING,
                    AssetType.PRODUCT_TECHNICAL_SPECIFICATION_SHEET,
                    AssetType.PRODUCT_DIAGRAM,
                ]
            ),
        )
        .order_by(Asset.role_id.asc())
        .all()
    )

    return group_assets(asset_results)


def fetch_asset_type(assets: list[AssetRes]) -> str | None:
    asset_type = None
    for asset in assets:
        if asset.file_type == "OTHER" or asset.file_type == "HIGHRESOLUTION":
            asset_type = str(asset.url).split(".")[-1]

    return asset_type


def group_assets(asset_results: list) -> list[AssetGroupRes]:
    assets_dict: dict[str, list[AssetRes]] = {}

    for asset in asset_results:
        asset_model = AssetRes(
            id=asset.id, url=asset.url, file_type=asset.file_type, type=asset.type, size=asset.asset_size
        )
        if asset.file_name not in assets_dict:
            assets_dict[asset.file_name] = [asset_model]
        else:
            assets_dict[asset.file_name].append(asset_model)
    asset_groups = [
        AssetGroupRes(file_name=file_name, asset_files=assets, asset_type=fetch_asset_type(assets))
        for file_name, assets in assets_dict.items()
    ]
    return asset_groups


def fetch_related_products(db: Session, user: User, product_id: UUID, relationship_ids: list[int]) -> Any:
    return (
        db.query(
            RelatedProduct.related_product_id.label("related_product_id"),
            RelationshipTranslation.translated_name.label("relationship_name"),
            RelatedProduct.relationship_id.label("relationship_id"),
        )
        .join(RelationshipTranslation, RelatedProduct.id == RelationshipTranslation.related_product_id)
        .filter(
            RelatedProduct.main_product_id == product_id,
            RelationshipTranslation.language_id == user.language.id,
            RelatedProduct.relationship_id.in_(relationship_ids),
        )
        .all()
    )
