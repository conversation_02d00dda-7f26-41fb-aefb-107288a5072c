from holmatro_customer_portal.database.enums import LanguageEnum

article_number_translations: dict = {
    LanguageEnum.DUTCH: {"group_id": 1, "group": "Details", "grouped_attributes": [{"name": "Artikelnummer"}]},
    LanguageEnum.ENGLISH: {"group_id": 1, "group": "Details", "grouped_attributes": [{"name": "Articlenumber"}]},
    LanguageEnum.GERMAN: {"group_id": 1, "group": "Details", "grouped_attributes": [{"name": "Artikelnummer"}]},
    LanguageEnum.FRENCH: {"group_id": 1, "group": "Informations", "grouped_attributes": [{"name": "Numéro d'article"}]},
    LanguageEnum.FRENCH: {"group_id": 1, "group": "Informations", "grouped_attributes": [{"name": "Numéro d'article"}]},
    LanguageEnum.POLISH: {"group_id": 1, "group": "Szczegół<PERSON>", "grouped_attributes": [{"name": "Numer produktu"}]},
    LanguageEnum.SPANISH: {"group_id": 1, "group": "Detalles", "grouped_attributes": [{"name": "Número de artículo	"}]},
    LanguageEnum.PORTUGUESE: {"group_id": 1, "group": "Detalhes", "grouped_attributes": [{"name": "Número do artigo"}]},
    LanguageEnum.CHINESE: {"group_id": 1, "group": "详情", "grouped_attributes": [{"name": "品号"}]},
}
