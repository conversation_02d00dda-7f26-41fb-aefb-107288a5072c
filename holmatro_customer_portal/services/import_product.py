import uuid
from datetime import datetime, timezone

import sentry_sdk
from celery import chain  # type: ignore
from sqlalchemy.orm import Session

from holmatro_customer_portal.celery_config import celery_app
from holmatro_customer_portal.database.models import Product as DBProduct
from holmatro_customer_portal.mappers.product_model_mapper import ProductMapper
from holmatro_customer_portal.schemas.syncforce_product_schema import Product as SFProduct
from holmatro_customer_portal.utils.database import construct_db_write_url, session_creator
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.storage_utils import fetch_json_contents_by_ids, get_api_json_contents
from holmatro_customer_portal.utils.transforms import convert_dict_keys_to_snake

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


def import_product_to_db(
    db: Session, imported_product: dict, categories_data: dict, process_assets: bool = True
) -> str | None:
    # A few products from SyncForce have no segmentation defined, which is currently required for import.
    # TODO: This doesn't seem to be used anywhere in the application, so we may opt to remove this field in the future.
    if not imported_product["Product"]["Segmentation"]:
        _logger.warning(f"Product {imported_product['Product']['ArticleNumber']} has no segmentation, skipping import")
        return None

    converted_product = convert_dict_keys_to_snake(imported_product["Product"])

    try:
        sf_product = SFProduct(**converted_product)
    except Exception as e:
        # Catch any errors during Pydantic model validation (but not restricted to only ValidationErrors),
        # and log them to avoid breaking and halting the entire import process.
        article_number = converted_product["article_number"]
        _logger.error(
            f"Skipping import of product {article_number} due to error during model validation",
            exc_info=e,
        )
        sentry_sdk.capture_exception(e, extras={"product_article_number": article_number})
        return None

    product_mapper = ProductMapper(db, categories_data=categories_data, process_assets=process_assets)

    product_id = db.query(DBProduct.id).filter_by(umid=uuid.UUID(sf_product.umid)).one_or_none()
    _logger.debug(f"Retrieving product from database with umid {sf_product.umid}")
    if product_id:
        product_id = product_id[0]
    db_product = product_mapper.map_domain_to_db_model(sf_product, product_id)
    db_product.deleted_at = None  # Ensure product is not soft-deleted when imported/updated

    db.merge(db_product)

    _logger.info(f"Finished importing product {sf_product.article_number} with umid {sf_product.umid}")
    return sf_product.umid


@celery_app.task
def import_products_from_storage(ids: list[str], process_assets: bool = True) -> list[str]:
    """
    Also called from Holmatro products sync for each product.
    Imports products in small batches with commits to balance reliability and performance.
    """
    db = session_creator(construct_db_write_url())()
    try:
        # Download categories data once for all products
        proposition_id = Env.SYNC_FORCE_PROPOSITION_ID.get()
        categories_data = get_api_json_contents(f"categories-{proposition_id}.json")

        products_data = fetch_json_contents_by_ids(ids)
        imported_umids = []
        failed_imports = []

        # Batch size for commits - balance between reliability and performance
        batch_size = Env.IMPORT_BATCH_SIZE.get()
        current_batch_count = 0

        for key, data in products_data.items():
            try:
                umid = import_product_to_db(db, data, categories_data, process_assets=process_assets)
                if umid is not None:
                    imported_umids.append(umid)
                    current_batch_count += 1
                    _logger.debug(f"Successfully processed product {umid}")

                    # Commit in batches to reduce lock contention while preserving most work
                    if current_batch_count >= batch_size:
                        db.commit()
                        _logger.debug(f"Committed batch of {current_batch_count} products")
                        current_batch_count = 0
                else:
                    # Product was skipped (e.g., no segmentation), but this is not an error
                    _logger.debug(f"Product from {key} was skipped during import")
            except Exception as e:
                # Log the error but continue with other products
                product_info = data.get("Product", {}).get("ArticleNumber", key) if data else key
                _logger.error(f"Failed to import product {product_info}: {e}", exc_info=e)
                sentry_sdk.capture_exception(e, extras={"product_key": key, "product_info": product_info})
                failed_imports.append(key)

                # Rollback only the current transaction to clean up any partial changes
                db.rollback()
                current_batch_count = 0  # Reset batch count after rollback

        # Commit any remaining products in the final batch
        if current_batch_count > 0:
            db.commit()
            _logger.debug(f"Committed final batch of {current_batch_count} products")

        _logger.info(f"Import completed: {len(imported_umids)} successful, {len(failed_imports)} failed")
        if failed_imports:
            _logger.warning(f"Failed imports: {failed_imports}")

        return imported_umids
    except Exception as e:
        # This catches errors in setup (categories download, products_data fetch, etc.)
        _logger.error(f"Critical error during import setup: {e}", exc_info=e)
        db.rollback()
        raise e
    finally:
        db.close()


# dont change name parameter, used in Holmatro products sync
@celery_app.task(name="holmatro_customer_portal.soft_delete_products_not_in_list")
def soft_delete_products_not_in_list(imported_umids: list[str]) -> None:
    """
    Also called from Holmatro products sync as the final step
    """
    # Safeguard: Prevent accidental deletion of all products
    min_products_threshold = Env.MIN_PRODUCTS_PROPOSITION_THRESHOLD.get()

    if not imported_umids:
        raise ValueError("Cannot soft-delete products with empty imported_umids list")

    if len(imported_umids) < min_products_threshold:
        raise ValueError(
            f"Imported products count ({len(imported_umids)}) is below minimum threshold ({min_products_threshold}). "
            f"This could indicate a data issue. Set MIN_PRODUCTS_THRESHOLD environment variable to override."
        )

    _logger.info(f"Soft-deleting products not in list of {len(imported_umids)} imported products")
    db = session_creator(construct_db_write_url())()
    try:
        # Convert string UMIDs to UUID objects
        uuid_list = [uuid.UUID(umid) for umid in imported_umids]

        soft_deleted_rows = (
            db.query(DBProduct)
            .filter(DBProduct.umid.notin_(uuid_list), DBProduct.deleted_at.is_(None))
            .update(
                {DBProduct.deleted_at: datetime.now(timezone.utc), DBProduct.dirty: True}, synchronize_session=False
            )
        )

        db.commit()
        _logger.info(f"Successfully soft-deleted {soft_deleted_rows} products")
    except Exception as e:
        _logger.error(f"An error occurred during soft-deletion: {e}")
        db.rollback()
        raise e
    finally:
        db.close()


@celery_app.task
def schedule_full_resync_with_cleanup(product_ids: list[str], process_assets: bool = True) -> None:
    """Schedule a full resync using Celery chain to ensure cleanup happens after import."""
    workflow = chain(import_products_from_storage.s(product_ids, process_assets), soft_delete_products_not_in_list.s())
    workflow.apply_async()
