import sentry_sdk
from sqlalchemy import and_, or_
from sqlalchemy.orm import aliased

from holmatro_customer_portal.database.models import Category as DBCategory
from holmatro_customer_portal.database.models import CategoryTranslation, Language, User
from holmatro_customer_portal.schemas.auth import UserClaims
from holmatro_customer_portal.schemas.my_hm_api_schema import MYHMContact
from holmatro_customer_portal.schemas.response_schema import ProfileRes
from holmatro_customer_portal.utils.database import DatabaseOperations, Session
from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient


def resync_user(user_claims: UserClaims, session: Session, my_hm_client: MyHolmatroPortalApiClient) -> User | None:
    db_ops = DatabaseOperations(session)
    email = user_claims.emails[0]

    # Important: get_user_info() will return de user details of <PERSON> from Holmatro when using a token that is
    # connected to a Harborn email address! This expected behavior, and implemented as such to allow all Harborn users
    # access to the required categories, without having to create a matching user in their system. All other users will
    # receive their own details from this call.
    user_info: MYHMContact = my_hm_client.get_user_info()
    categories: list[DBCategory] = fetch_user_categories(user_info, session)

    # Because of a previous incident where no matching categories were found after the proposition in SyncForce was
    # renamed, we now just return the current user instead of setting the categories to None, as this would cause
    # a 500 error page in the frontend.
    if len(categories) == 0:
        sentry_sdk.capture_exception(RuntimeError(f"No matching categories found for user {email}!"))
        return session.query(User).filter_by(email=email).one_or_none()
    else:
        user = db_ops.upsert(
            User,
            filter_by={"email": email},
            email=email,
            last_name=user_claims.family_name,
            name=user_claims.name,  # Unknown standard B2C Claims output
            country=user_info.country_id,
            currency=user_info.currency_id,
            relation_name=user_info.relation_name,
            main_language=db_ops.upsert(
                Language, filter_by={"language_code": user_info.language_id}, language_code=user_info.language_id
            ),
            categories=categories,
        )
        session.add(user)
        session.commit()

        return user


def fetch_user_categories(user_info: MYHMContact, session: Session) -> list[DBCategory]:
    category_pairs = [(attribute.sf_category, attribute.sf_sub_category) for attribute in user_info.attributes]

    ParentCategory = aliased(DBCategory)

    conditions = [
        and_(
            ParentCategory.translations.any(CategoryTranslation.name == parent),
            DBCategory.translations.any(CategoryTranslation.name == child),
        )
        for parent, child in category_pairs
    ]

    results = (
        session.query(DBCategory)
        .join(ParentCategory, DBCategory.parent_id == ParentCategory.id)
        .join(DBCategory.translations)
        .filter(or_(*conditions))
        .all()
    )

    return results


def parse_user_info(user_details: User) -> ProfileRes:
    user_categories = [cat.category_id for cat in user_details.categories]
    portal_categories = [cat for cat in SyncforceCategory if str(cat.value) in user_categories]

    # User.language is a hybrid property and User.categories is a relationship
    # It is necessary a dict to be able to store this information
    user = user_details.to_dict()
    user["language"] = user_details.language.model_dump()
    user["categories"] = portal_categories
    user["system_type"] = user_details.system_type

    return ProfileRes.model_validate(user)


def collect_anonymous_user_info(session: Session, user_claims: UserClaims) -> ProfileRes:
    user = session.query(User).filter(User.email == user_claims.emails[0]).one()
    categories = [SyncforceCategory(int(cat.category_id)) for cat in user.categories]

    return ProfileRes(
        name=None,
        id=user.id,
        email=None,
        last_name=None,
        language=user.language.model_dump(),
        currency=None,
        categories=categories,
        system_type=user.system_type,
    )
