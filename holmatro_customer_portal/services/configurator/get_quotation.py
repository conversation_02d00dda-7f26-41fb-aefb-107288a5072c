from datetime import datetime as dt
from functools import cache

import pdfkit  # type:ignore
from jinja2 import Environment, FileSystemLoader, Template
from sqlalchemy import Column

from holmatro_customer_portal.database.models import (
    Configuration,
    ConfigurationProduct,
    Product,
    ProductNameTranslation,
    User,
)
from holmatro_customer_portal.schemas.my_hm_api_schema import MYH<PERSON>rice, MYHMStock
from holmatro_customer_portal.schemas.quotation import <PERSON><PERSON><PERSON><PERSON><PERSON>, Quote, QuoteItem
from holmatro_customer_portal.schemas.response_schema import Stock
from holmatro_customer_portal.utils.my_hm.my_hm_interface import MyHolmatroClientInterface


def get_quotation(configuration: Configuration, my_hm_client: MyHolmatroClientInterface, user: User) -> bytes:
    """
    1. Get product prices, product stock and other quotation details
    2. Check if configuration have more than 2 products
    3. If so, we separate the subtotal from quotation details because it will be use only in the last page
    4. Generate first page with quotation details
    5. Generate the other pages making sure that only the last one contains the subtotal
    Obs: In case there are changes in the template, it might be necessary to change the quantity of items per page
    """
    product_prices: dict[str, MYH<PERSON>rice] = {
        conf_product.product.article_number: my_hm_client.get_product_sales_prices(
            conf_product.product.article_number, user.currency
        )
        for conf_product in configuration.products
    }
    product_stock: dict[str, MYHMStock] = {
        conf_product.product.article_number: my_hm_client.get_stock(conf_product.product.article_number)
        for conf_product in configuration.products
    }

    quotation_items: list[QuoteItem] = get_quotation_items(
        group_by_quantity(configuration.products), product_prices, product_stock
    )
    quotation_sub_total: str | None = calculate_quotation_amount(quotation_items)
    quotation: Quote = get_quote_details(quotation_sub_total, configuration)

    content = get_pdf_content(quotation, quotation_items, bool(user.anonymous))
    options = {
        "margin-top": "25px",
        "margin-right": "47.5px",
        "margin-bottom": "50px",
        "margin-left": "47.5px",
    }

    quotation_pdf: bytes = pdfkit.from_string(content, options=options)
    return quotation_pdf


def get_quote_details(quotation_sub_total: str | None, configuration: Configuration) -> Quote:
    title = "Configuration Overview" if configuration.user.anonymous else "Quotation"
    return Quote(
        title=title,
        details_email=configuration.user.email,  # type: ignore
        details_name=str(configuration.user.last_name),
        date_value=dt.now().strftime("%Y-%m-%d"),
        quotation_number=str(configuration.quotation_number),
        currency_label=configuration.user.currency.value if configuration.user.currency else None,
        reference_value=str(configuration.reference),
        subtotal_value=quotation_sub_total,
        details_company=str(configuration.user.relation_name),
    )


def get_quotation_items(
    configuration_products: list[ConfigurationProduct],
    product_prices: dict[str, MYHMPrice],
    product_stock: dict[str, MYHMStock],
    language_code: str = "en",
) -> list[QuoteItem]:
    quote_items = []
    for configuration_product in configuration_products:
        product: Product = configuration_product.product
        price_res: MYHMPrice = product_prices[str(product.article_number)]
        stock_res: MYHMStock = product_stock[str(product.article_number)]

        # If the current stock status is IN_STOCK, but the article quantity from the quote item
        # exceeds the quantity in stock, then the status should be NO_STOCK instead.
        stock_status = stock_res.stock_status
        if stock_status == Stock.IN_STOCK and configuration_product.quantity > (stock_res.stock_quantity or 0):
            stock_status = Stock.NO_STOCK

        quote_items.append(
            QuoteItem(
                quantity=str(configuration_product.quantity),
                product_name=name_translation(product.product_names, language_code),
                availability=stock_status.value.replace("_", " "),
                product_nr=str(product.article_number),
                unit_price=str(price_res.gross_price) if price_res.gross_price else None,
                discount=str(price_res.discount) if price_res.discount else None,
                net_price=str(price_res.nett_price) if price_res.nett_price else None,
                line_total=str(price_res.nett_price * configuration_product.quantity) if price_res.nett_price else None,
            )
        )
    return quote_items


def name_translation(translations: list[ProductNameTranslation], language_code: str) -> str:
    return next(
        (translation.value for translation in translations if translation.language.language_code == language_code),
    )


def calculate_quotation_amount(quotation_items: list[QuoteItem]) -> str | None:
    if any(item.line_total is None for item in quotation_items):
        # Cannot calculate a total if any of the line totals are None
        return None

    return "{:.2f}".format(sum([float(item.line_total) for item in quotation_items]))  # type: ignore


def get_pdf_content(quotation: Quote, quotation_items: list[QuoteItem], anonymous: bool) -> str:
    template = "anonymous_quote.html" if anonymous else "quote.html"

    content = _load_pdf_template(template).render(
        quote=quotation.model_dump(),
        item_header=ItemHeader().model_dump(),
        quote_items=[item.model_dump() for item in quotation_items],
    )

    return content


def group_by_quantity(products: list[ConfigurationProduct]) -> list[ConfigurationProduct]:
    grouped = {}
    for product in products:
        if product.product_id not in grouped:
            grouped[product.product_id] = product
        else:
            grouped[product.product_id].quantity += product.quantity  # type: ignore

    return [v for v in grouped.values()]


@cache
def _load_pdf_template(template: str) -> Template:
    abs_template_path = "holmatro_customer_portal/services/configurator/quotation_data/"
    environment = Environment(autoescape=True, loader=FileSystemLoader(abs_template_path))
    return environment.get_template(template)  # nosec
