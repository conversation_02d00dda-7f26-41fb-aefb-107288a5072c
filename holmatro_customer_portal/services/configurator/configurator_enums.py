from enum import Enum, IntEnum


class SyncForceAttributeId(Enum):
    CYLINDER_REQUIRED_OIL_CONTENT = 357
    CYLINDER_ACTING_TYPE = 359
    CYLINDER_RETURN_TYPE = 361
    CYLINDER_TONNAGE = 98
    CYLINDER_TYPE = 363
    MODEL = 2
    PUMP_OPERATOR_TYPE = 358
    PUMP_SECOND_STAGE_OUTPUT = 183
    PUMP_CAPACITY_OIL_TANK = 384


class FilterName(Enum):
    # The filter names come from Tweakwise.
    # They are used as Enum because Tweakwise doesn't provide an ID

    """Lifting filters"""
    PUMP_OPERATOR_TYPE = "hie-pump-operator-type-multilanguage"
    CAPACITY_OIL_TANK = "capacity-oil"
    CYLINDER_TYPE_MULTILANGUAGE = "hie-cylinder-types-multilanguage"
    CYLINDER_TYPE = "cylinder-type"
    TONNAGE = "tonnage-t"
    HOSE_TYPE = "hose-type"
    ACTING_TYPE = "hie-acting-type-multilanguage"
    NUMBER_OF_OUTPUTS = "number-of-outputs"
    FIRST_STAGE_OUTPUT_CC = "first-stage-output-min-cc"
    FIRST_STAGE_OUTPUT_OZ = "first-stage-output-min-oz"

    """ Cutting filters """
    CUTTER_APPLICATION = "hie-cutting-applications-multi-language"
    CUTTER_DETAILS = "hie-short-description-multilanguage"
    CUTTER_TYPE = "hie-for-cutter-type-multilanguage-en-nl"  # used for hoses and pumps
    CUTTING_FREQUENCY = "number-of-cuts-per-day"
    CAR_RECYCLING_CATALYTIC_CONVERTER = "catalytic-converter"
    CABLE_CUTTING_POWER_CABLE_DIAMETER = "max-cutting-diameter-power-cable-mm"
    METAL_RECYCLING_ROUND_BAR_DIAMETER = "max-cutting-diameter-round-bar-s235-acc-to-en-10025-2-mm"
    METAL_RECYCLING_THIN_PIPE_DIAMETER = (
        "max-cutting-diameter-round-tube-e235-acc-to-en-10305-1-based-on-wall-thickness-2mm-0-08in-mm"
    )
    METAL_RECYCLING_THICK_PIPE_DIAMETER = (
        "max-cutting-diameter-round-tube-e235-acc-to-en-10305-1-based-on-wall-thickness-5mm-0-2in-mm"
    )
    METAL_RECYCLING_FLAT_BAR_THICKNESS = (
        "max-cutting-thickness-flat-bar-s235-acc-to-en-10025-based-on-strip-width-10mm-0-4in-mm"
    )


class CutterFilterValues(Enum):
    # The filter values come from Tweakwise.
    # They are used as Enum because Tweakwise doesn't provide an ID
    CABLE_CUTTING = "cable recycling"
    CAR_RECYCLING = "car recycling"
    FRIDGE_RECYCLING = "recycling of domestic appliances"
    METAL_RECYCLING = "metal/scrap recycling"
    STATIONARY_TWO_HANDS = "Stationary Cutter - 2 hand controlled"
    MOBILE = "Mobile Cutter"
    BATTERY = "Battery Cutter"
    STATIONARY = "Stationary Cutter - 1 hand controlled"


class CuttingFrequencyFilterValues(Enum):
    # The filter values come from Tweakwise.
    # They are used as Enum because Tweakwise doesn't provide an ID
    LOW = 10
    MEDIUM = 75
    HIGH = 200


class HoseTypeFilter(Enum):
    # The filter values come from Tweakwise.
    # They are used as Enum because Tweakwise doesn't provide an ID
    EXTENSION = "extension hose"
    STANDARD = "standard hose"


class ImperialFilterTemplateIDs(IntEnum):
    # The filter ids are found in Tweakwise
    # To find the id of a filter use the endpoint 'gateway.tweakwisenavigator.com/catalog/templates/{{tweakwise_key}}'
    # and search for the template by name as it is defined in Tweakwise
    INDUSTRIAL_LIFTING = 383  # Template used for users with imperial unit
    INDUSTRIAL_CUTTING = 385  # Template used for users with imperial unit


class PumpType(Enum):
    HAND = "hand"
    COMPACT = "compact"
    COMPACT_ELECTRIC = "electric"
    COMPACT_AIR = "air"
    VARI = "vari"
    VARI_ELECTRIC_MOTOR = "electric motor"
    VARI_PETROL_ENGINE = "petrol engine"
    EXISTING = "existing"


class CylinderType(Enum):
    LIFTING_CYLINDERS = "cylinder"
    LIFTING_WEDGES = "wedges"
    LIFTING_TOE_JACKS = "toe_jacks"
    PULLING_CYLINDERS = "pulling"
    PULLING_HOLLOW = "hollow plunger"


class CylinderActingType(Enum):
    SINGLE = "single"
    DOUBLE = "double"


class CylinderReturnType(Enum):
    GRAVITY = "gravity"
    SPRING = "spring"
    HYDRAULIC = "hydraulic"


class FlowPanelType(Enum):
    SEPARATE = "separate"
    ON_TOP = "on_top"
    EXISTING = "existing"


class ConfiguratorSelectionChoices(Enum):
    YES = "yes"
    NO = "no"
    NO_GAUGE = "nogauge"
    GAUGE_AND_NEEDLE = "gaugeandneedle"
    GAUGE_ONLY = "gaugeonly"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class HoseTypeSelection(Enum):
    MOUNTED = "mounted"
    EXTENSION = "extension"
    EXISTING = "existing"


class CutterApplication(Enum):
    CABLE_CUTTING = "cable_cutting"
    CAR_RECYCLING = "car_recycling"
    FRIDGE_RECYCLING = "fridge_recycling"
    METAL_RECYCLING = "metal_recycling"


class CutterType(Enum):
    MOBILE = "mobile"
    STATIONARY = "stationary"
    BATTERY = "battery"


class CutterRequirements(Enum):
    FRIDGE_RECYCLING_TWIST_GRIP = "twist_grip"
    FRIDGE_RECYCLING_STATIONARY_ONE_HAND = "one_hand"
    FRIDGE_RECYCLING_STATIONARY_TWO_HANDS = "two_hands"
    LOW = "low"
    HIGH = "high"
    SMALL = "small"
    LARGE = "large"
    MEDIUM = "medium"
    METAL_RECYCLING_ROUND_BAR = "round_bar"
    METAL_RECYCLING_THIN_PIPE = "thin_pipe"
    METAL_RECYCLING_THICK_PIPE = "thick_pipe"
    METAL_RECYCLING_FLAT_BAR = "flat_bar"
