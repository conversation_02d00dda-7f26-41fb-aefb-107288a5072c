import json
import re
import types
from typing import Any

from transitions import EventData, Machine

from holmatro_customer_portal import get_project_root
from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.domain.repositories.catalog_repository import CatalogRepository
from holmatro_customer_portal.mappers.product_preview_mapper import ProductPreviewMapper
from holmatro_customer_portal.services.configurator.states.base import BaseState, ConfiguratorState
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition
from holmatro_customer_portal.services.utils.product_queries import get_product_preview_by_article_number
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.my_hm.my_hm_interface import MyHolmatroClientInterface

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class Configurator:
    state: ConfiguratorState
    s3_pattern = re.compile(r"(https://)([^.]+)(.s3.[^.]+.amazonaws.com/)")

    def __init__(
        self,
        session: Session,
        states: list[dict],
        initial_state: ConfiguratorState,
        configuration: Configuration,
        my_hm_client: MyHolmatroClientInterface,
        catalog_repository: CatalogRepository,
    ) -> None:
        self._session = session
        self._configuration = configuration
        self._my_hm_client = my_hm_client
        self._catalog_repository = catalog_repository

        self.machine = Machine(
            model=self,
            states=states,
            initial=initial_state,
            auto_transitions=False,
            send_event=True,
        )

        self.load_state_definition()  # load initial state definition

    def load_state_definition(self, event: EventData | None = None) -> None:
        _logger.debug(f"Loading state definition for {self.state}")
        self.state_definition = self.load(self.state)

    @staticmethod
    def load(state: ConfiguratorState) -> Any:
        read: str = open(
            f"{get_project_root()}/holmatro_customer_portal/services/configurator/states/definitions/{state.value}.json"
        ).read()
        parsed: str = re.sub(Configurator.s3_pattern, rf"\1{Env.HOLMATRO_ASSETS_BUCKET_NAME.get()}\3", read)
        return json.loads(parsed)

    def set_default_product(self, product_article_number: str) -> None:
        """
        Insert product object in state definition if there is a default product
        """
        default_product = get_product_preview_by_article_number(
            product_article_number, self._configuration.user, self._session
        )
        mapped_product = ProductPreviewMapper(self._session, self._my_hm_client).product_preview_mapper(
            self._configuration.user, default_product
        )

        for c in self.state_definition["content"]:
            if default_product and c["type"] == "products":
                c["meta"]["default_product"] = mapped_product[0]


class ConfiguratorFactory:
    def get_configurator_for_state(
        self,
        session: Session,
        state: ConfiguratorState,
        configuration: Configuration,
        my_hm_client: MyHolmatroClientInterface,
        catalog_repository: CatalogRepository,
    ) -> Configurator:
        _logger.debug(f"Creating configurator for state {state}")

        states = [s().definition for s in BaseState.__subclasses__()]
        configurator = Configurator(session, states, state, configuration, my_hm_client, catalog_repository)

        for s in BaseState.__subclasses__():
            enter_method_name = s().on_enter_method_name
            setattr(configurator, enter_method_name, types.MethodType(s.on_enter, configurator))

            exit_method_name = s().on_exit_method_name
            setattr(configurator, exit_method_name, types.MethodType(s.on_exit, configurator))

        for t in BaseTransition.__subclasses__():
            if t.source == state:
                method_name = t().condition_method_name
                setattr(configurator, method_name, types.MethodType(t.condition, configurator))
                configurator.machine.add_transition(**t().definition)

        return configurator
