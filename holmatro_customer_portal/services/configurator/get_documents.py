import io
from typing import Optional
from uuid import UUID

import requests
from pypdf import Pdf<PERSON><PERSON><PERSON>
from sqlalchemy import and_
from sqlalchemy.engine import Row

from holmatro_customer_portal.database.enums import AssetSubTypeId, AssetTypeId, SystemType
from holmatro_customer_portal.database.models import (
    Asset,
    AssetMetadataTranslations,
    AssetResource,
    AssetType,
    ConfigurationProduct,
    Language,
    Product,
    asset_resource_language_association_table,
)
from holmatro_customer_portal.utils.database import Session


def get_merged_documents(
    configuration_id: UUID,
    db: Session,
    language_id: UUID,
    asset_type: AssetType,
    asset_sub_type_id: AssetSubTypeId,
    system_type: SystemType | None = None,
) -> Optional[bytes]:
    document_urls = get_document_urls(configuration_id, db, language_id, asset_type, asset_sub_type_id, system_type)
    if document_urls:
        return download_and_merge_pdfs(document_urls)
    else:
        return None


def download_and_merge_pdfs(urls: set[str] | list[str]) -> bytes:
    merger = PdfMerger()
    for pdf_download_url in urls:
        response = requests.get(pdf_download_url, timeout=5)
        response.raise_for_status()
        pdf = io.BytesIO(response.content)
        merger.append(pdf)

    output = io.BytesIO()
    merger.write(output)
    merger.close()
    return output.getvalue()


def get_document_urls(
    configuration_id: UUID,
    session: Session,
    language_id: UUID,
    asset_type: AssetType,
    asset_sub_type_id: AssetSubTypeId,
    system_type: SystemType | None = None,
) -> set[str]:
    english = session.query(Language).filter(Language.language_code == "en").one()
    # Query to fetch asset resources that match the criteria
    filterset = [
        and_(
            ConfigurationProduct.configuration_id == configuration_id,
            Asset.asset_type == asset_type,
            Asset.asset_type_id == AssetTypeId.DOCUMENT.value,
            Asset.asset_sub_type_id == asset_sub_type_id.value,
            asset_resource_language_association_table.c.language_id == language_id,
            AssetMetadataTranslations.language_id == english.id,
        )
    ]
    if system_type:
        filterset.append(and_(AssetMetadataTranslations.asset_label.ilike(f"%{system_type.value}%")))

    urls: list[Row] = (
        session.query(AssetResource.url)
        .join(Asset, Asset.id == AssetResource.asset_id)
        .join(Product, Product.id == Asset.product_id)
        .join(ConfigurationProduct, ConfigurationProduct.product_id == Product.id)
        .join(AssetMetadataTranslations, AssetMetadataTranslations.asset_id == Asset.id)
        .join(
            asset_resource_language_association_table,
            AssetResource.id == asset_resource_language_association_table.c.asset_resource_id,
        )
        .filter(*filterset)
        .all()
    )
    # Extract URLs from the query result
    return set([url[0] for url in urls])
