from __future__ import annotations

from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition, TransitionTrigger


class CuttingChargerBatteriesBackTransition(BaseTransition):
    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_CUTTING_CHARGER_BATTERIES_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST


class CuttingChargerBatteriesSkipTransition(BaseTransition):
    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_CUTTING_CHARGER_BATTERIES_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST


class CuttingChargerBatteriesNextTransition(BaseTransition):
    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_CUTTING_CHARGER_BATTERIES_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST
