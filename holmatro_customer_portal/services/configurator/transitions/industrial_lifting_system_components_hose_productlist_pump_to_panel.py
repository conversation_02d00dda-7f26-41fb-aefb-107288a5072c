from __future__ import annotations

from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.services.configurator.configurator_enums import ConfiguratorSelectionChoices, PumpType
from holmatro_customer_portal.services.configurator.states.base import (
    ConfiguratorFlowPanelStageDataIDs,
    ConfiguratorState,
)
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition, TransitionTrigger
from holmatro_customer_portal.services.configurator.utils import (
    get_or_query_products,
    get_or_query_pump_type,
    get_or_query_value,
)

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator


class SystemComponentsHoseProductListPumpToPanelBackToFlowPanelCoverBackTransition(BaseTransition):
    """
    Conditions:
    - If a protection frame was selected and selected to choose a cover
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_COVER

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        pump_type = get_or_query_pump_type(event, self._session, self._configuration)
        if pump_type != PumpType.VARI.value:
            return False

        protection_frame = get_or_query_products(
            event,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME,
            self._session,
            self._configuration,
        )
        if not protection_frame:
            return False

        return (
            get_or_query_value(
                event,
                ConfiguratorFlowPanelStageDataIDs.FLOW_PANEL_SETTINGS_COVER_SELECTION.value,
                self._session,
                self._configuration,
            )
            != ConfiguratorSelectionChoices.NO.value
        )


class SystemComponentsHoseProductListPumpToPanelBackToFlowPanelProtectionFrameBackTransition(BaseTransition):
    """
    Conditions:
    - If selected to choose a protection frame
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        pump_type = get_or_query_pump_type(event, self._session, self._configuration)
        if pump_type != PumpType.VARI.value:
            return False

        return (
            get_or_query_value(
                event,
                ConfiguratorFlowPanelStageDataIDs.FLOW_PANEL_SETTINGS_FRAME_SELECTION.value,
                self._session,
                self._configuration,
            )
            != ConfiguratorSelectionChoices.NO.value
        )


class SystemComponentsHoseProductListPumpToPanelBackToHoseProductListMainBackTransition(BaseTransition):
    """
    Conditions:
    - If vari pump separate
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        pump_type = get_or_query_pump_type(event, self._session, self._configuration)

        if not pump_type:
            return True

        return pump_type == PumpType.VARI.value


class SystemComponentsHoseProductListPumpToPanelBackToHoseSettingsTransition(BaseTransition):
    """
    Is Default
    Conditions:
    - If hand pump or compact pump
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS

    def unless(self) -> list:
        return [
            SystemComponentsHoseProductListPumpToPanelBackToFlowPanelCoverBackTransition().condition_method_name,
            SystemComponentsHoseProductListPumpToPanelBackToFlowPanelProtectionFrameBackTransition().condition_method_name,
            SystemComponentsHoseProductListPumpToPanelBackToHoseProductListMainBackTransition().condition_method_name,
        ]


class SystemComponentsHoseProductListPumpToPanelNextTransition(BaseTransition):
    """Is Default"""

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL
    dest = ConfiguratorState.INDUSTRIAL_OVERVIEW


class SystemComponentsHoseProductListPumpToPanelSkipTransition(BaseTransition):
    """Is Default"""

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL
    dest = ConfiguratorState.INDUSTRIAL_OVERVIEW
