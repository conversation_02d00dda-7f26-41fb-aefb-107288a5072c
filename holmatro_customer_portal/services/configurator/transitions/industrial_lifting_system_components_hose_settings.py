from __future__ import annotations

from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.services.configurator.configurator_enums import (
    ConfiguratorSelectionChoices,
    HoseTypeSelection,
)
from holmatro_customer_portal.services.configurator.states.base import (
    ConfiguratorHoseStageDataIDs,
    ConfiguratorPumpStageDataIDs,
    ConfiguratorState,
)
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition, TransitionTrigger
from holmatro_customer_portal.services.configurator.utils import (
    get_or_query_cylinder_quantity,
    get_or_query_products,
    get_or_query_value,
)

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator


class SystemComponentsHoseSettingsBackToPumpProductListTransition(BaseTransition):
    """
    Conditions:
    - If hand pump, only ONE cylinder was selected, selected to NOT choose a gaugeset neither a valve
    - If compact pump electric, only ONE cylinder was selected and selected to NOT choose valve
    - If compact pump air, only ONE cylinder was selected and selected to NOT choose gaugeset
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        cylinder_quantity = get_or_query_cylinder_quantity(event, self._session, self._configuration)

        if isinstance(cylinder_quantity, int) and cylinder_quantity > 1:
            return False

        hand_pump_gaugeset_selection = get_or_query_value(
            event,
            ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_GAUGESET_SELECTION.value,
            self._session,
            self._configuration,
        )

        if hand_pump_gaugeset_selection == ConfiguratorSelectionChoices.NO.value:
            hand_pump_valve_selection: str | None = get_or_query_value(
                event,
                ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_VALVE_SELECTION.value,
                self._session,
                self._configuration,
            )
            return hand_pump_valve_selection != ConfiguratorSelectionChoices.YES.value

        compact_electric_pump_gaugeset_selection = get_or_query_value(
            event,
            ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_COMPACT_ELECTRIC_GAUGE_SELECTION.value,
            self._session,
            self._configuration,
        )

        if compact_electric_pump_gaugeset_selection == ConfiguratorSelectionChoices.NO_GAUGE.value:
            return True

        compact_pump_air_gaugeset_selection = get_or_query_value(
            event,
            ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_COMPACT_AIR_GAUGE_SELECTION.value,
            self._session,
            self._configuration,
        )
        return compact_pump_air_gaugeset_selection == ConfiguratorSelectionChoices.NO.value


class SystemComponentsHoseSettingsBackToValveTransition(BaseTransition):
    """
    Conditions:
    - If hand pump double acting, only ONE cylinder was selected, selected to NOT choose a gaugeset and to choose a valve
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        cylinder_quantity = get_or_query_cylinder_quantity(event, self._session, self._configuration)

        if isinstance(cylinder_quantity, int) and cylinder_quantity > 1:
            return False

        hand_pump_gaugeset_selection = get_or_query_value(
            event,
            ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_GAUGESET_SELECTION.value,
            self._session,
            self._configuration,
        )

        if hand_pump_gaugeset_selection != ConfiguratorSelectionChoices.NO.value:
            return False

        hand_pump_valve_selection: str | None = get_or_query_value(
            event,
            ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_VALVE_SELECTION.value,
            self._session,
            self._configuration,
        )
        return hand_pump_valve_selection == ConfiguratorSelectionChoices.YES.value


class SystemComponentsHoseSettingsBackToGaugesetTransition(BaseTransition):
    """
    Conditions:
    - If hand pump, only ONE cylinder was selected and selected to choose a gaugeset
    - If compact pump air, only ONE cylinder was selected and selected to choose a gaugeset
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        cylinder_quantity = get_or_query_cylinder_quantity(event, self._session, self._configuration)
        if isinstance(cylinder_quantity, int) and cylinder_quantity > 1:
            return False

        hand_pump_gaugeset_selection = get_or_query_value(
            event,
            ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_GAUGESET_SELECTION.value,
            self._session,
            self._configuration,
        )

        if hand_pump_gaugeset_selection == ConfiguratorSelectionChoices.YES.value:
            return True

        compact_pump_air_gaugeset_selection = get_or_query_value(
            event,
            ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_COMPACT_AIR_GAUGE_SELECTION.value,
            self._session,
            self._configuration,
        )
        if compact_pump_air_gaugeset_selection == ConfiguratorSelectionChoices.YES.value:
            return True

        compact_pump_electric_gaugeset_selection = get_or_query_value(
            event,
            ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_COMPACT_ELECTRIC_GAUGE_SELECTION.value,
            self._session,
            self._configuration,
        )
        return compact_pump_electric_gaugeset_selection in [
            ConfiguratorSelectionChoices.GAUGE_AND_NEEDLE.value,
            ConfiguratorSelectionChoices.GAUGE_ONLY.value,
        ]


class SystemComponentsHoseSettingsBackToFlowPanelProductListTransition(BaseTransition):
    """
    Is Default
    Conditions:
    - If flow panel has been already selected
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        flow_panel = get_or_query_products(
            event,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST,
            self._session,
            self._configuration,
        )
        return not flow_panel


class SystemComponentsHoseSettingsBackToHoseProductListMainTransition(BaseTransition):
    """
    Is Default
    Conditions:
    - If more than ONE cylinder was selected
    - If no data
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN

    def unless(self) -> list:
        return [
            SystemComponentsHoseSettingsBackToGaugesetTransition().condition_method_name,
            SystemComponentsHoseSettingsBackToValveTransition().condition_method_name,
            SystemComponentsHoseSettingsBackToPumpProductListTransition().condition_method_name,
            SystemComponentsHoseSettingsBackToFlowPanelProductListTransition().condition_method_name,
        ]


class SystemComponentsHoseSettingsNextToOverviewTransition(BaseTransition):
    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_OVERVIEW

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        hose_type = get_or_query_value(
            event, ConfiguratorHoseStageDataIDs.HOSE_SETTINGS_TYPE.value, self._session, self._configuration
        )
        return hose_type == HoseTypeSelection.EXISTING.value


class SystemComponentsHoseSettingsNextTransition(BaseTransition):
    """Is Default"""

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL

    def unless(self) -> list:
        return [SystemComponentsHoseSettingsNextToOverviewTransition().condition_method_name]


class SystemComponentsHoseSettingsSkipTransition(BaseTransition):
    """Is Default"""

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL
