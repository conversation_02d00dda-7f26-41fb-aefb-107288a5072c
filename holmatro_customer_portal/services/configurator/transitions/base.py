from __future__ import annotations

import enum
from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator


class TransitionTrigger(enum.Enum):
    NEXT = "next"
    BACK = "back"
    SKIP = "skip"


class BaseTransition:
    trigger: TransitionTrigger
    source: ConfiguratorState
    dest: ConfiguratorState

    @property
    def condition_method_name(self) -> str:
        return f"condition_{self.source.value}_{self.trigger.value}_{self.dest.value}".replace(".", "_")

    @property
    def definition(self) -> dict:
        return {
            "trigger": self.trigger.value,
            "source": self.source,
            "dest": self.dest,
            "conditions": self.condition_method_name,
            "unless": self.unless(),
        }

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:  # type: ignore
        return True

    def unless(self) -> list:
        return []
