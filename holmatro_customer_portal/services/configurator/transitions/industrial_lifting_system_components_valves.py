from __future__ import annotations

from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.services.configurator.configurator_enums import ConfiguratorSelectionChoices, PumpType
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorPumpStageDataIDs, ConfiguratorState
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition, TransitionTrigger
from holmatro_customer_portal.services.configurator.utils import (
    get_configuration_pump_type,
    get_or_query_cylinder_quantity,
    get_or_query_value,
)
from holmatro_customer_portal.utils.database import Session

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator


class SystemComponentsValveBackTransition(BaseTransition):
    """Is Default"""

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST


class SystemComponentsValveNextToFlowPanelProductListTransition(BaseTransition):
    """
    Conditions:
    - If hand pump, selected to NOT choose a gauge and more than ONE cylinder was chosen
    """

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        return transition_to_flow_panel_list(event, self._session, self._configuration)


class SystemComponentsValveNextToFlowPanelSettingsTransition(BaseTransition):
    """Conditions: If vari pump"""

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        pump_type = get_configuration_pump_type(self._session, self._configuration)
        return pump_type == PumpType.VARI.value or pump_type == PumpType.VARI_ELECTRIC_MOTOR.value


class SystemComponentsValveNextToHoseSettingsTransition(BaseTransition):
    """
    Conditions:
    # - If compact pump electric and ONE cylinder chosen
    - If hand pump, only one cylinder chosen and selected to NOT choose a gauge
    """

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        return transition_to_hose_settings(event, self._session, self._configuration)


class SystemComponentsValveNextToGaugeTransition(BaseTransition):
    """
    Is Default
    Conditions:
    - If hand pump double acting and selected to choose gauge
    """

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET

    def unless(self) -> list:
        return [
            SystemComponentsValveNextToHoseSettingsTransition().condition_method_name,
            SystemComponentsValveNextToFlowPanelSettingsTransition().condition_method_name,
            SystemComponentsValveNextToFlowPanelProductListTransition().condition_method_name,
        ]


class SystemComponentsValveSkipToFlowPanelListTransition(BaseTransition):
    """
    Conditions:
    - If hand pump selected to NOT choose a gauge and more than ONE cylinder was chosen
    """

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        return transition_to_flow_panel_list(event, self._session, self._configuration)


class SystemComponentsValveSkipToHoseSettingsTransition(BaseTransition):
    """
    Conditions:
    - If hand pump, only one cylinder chosen and selected to NOT choose a gauge
    # - If compact pump, only one cylinder chosen
    """

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        return transition_to_hose_settings(event, self._session, self._configuration)


class SystemComponentsValveSkipToFlowPanelSettingsTransition(BaseTransition):
    """Conditions: If vari pump"""

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        pump_type = get_configuration_pump_type(self._session, self._configuration)
        return pump_type == PumpType.VARI.value or pump_type == PumpType.VARI_ELECTRIC_MOTOR.value


class SystemComponentsValveSkipToGaugeTransition(BaseTransition):
    """
    Is Default
    Conditions:
    - If hand pump double acting, selected to choose gauge and only one cylinder
    """

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET

    def unless(self) -> list:
        return [
            SystemComponentsValveSkipToFlowPanelSettingsTransition().condition_method_name,
            SystemComponentsValveSkipToFlowPanelListTransition().condition_method_name,
        ]


def transition_to_hose_settings(event: EventData, db: Session, configuration: Configuration):
    cylinder_quantity: int | None = get_or_query_cylinder_quantity(event, db, configuration)
    if isinstance(cylinder_quantity, int) and cylinder_quantity > 1:
        return False

    gaugeset_selection: str | None = get_or_query_value(
        event, ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_GAUGESET_SELECTION.value, db, configuration
    )
    return gaugeset_selection == ConfiguratorSelectionChoices.NO.value


def transition_to_flow_panel_list(event: EventData, db: Session, configuration: Configuration):
    cylinder_quantity: int | None = get_or_query_cylinder_quantity(event, db, configuration)
    if isinstance(cylinder_quantity, int) and cylinder_quantity <= 1:
        return False

    gaugeset_selection: str | None = get_or_query_value(
        event, ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_GAUGESET_SELECTION.value, db, configuration
    )
    return gaugeset_selection == ConfiguratorSelectionChoices.NO.value
