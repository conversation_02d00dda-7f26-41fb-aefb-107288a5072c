from __future__ import annotations

from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.services.configurator.configurator_enums import CutterType
from holmatro_customer_portal.services.configurator.queries import get_configuration_data_value
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorCutterStageDataIDs, ConfiguratorState
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition, TransitionTrigger

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator


class CuttingAccessoriesBackToChargerBatteriesTransition(BaseTransition):
    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_CHARGER_BATTERIES_PRODUCTLIST

    def condition(self: BaseTransition | Configurator, _: EventData) -> bool:
        """
        Conditions:
        - Way of cutting is 'battery'
        """
        way_of_cutting = get_configuration_data_value(
            self._session, self._configuration, ConfiguratorCutterStageDataIDs.CUTTER_WAY_OF_CUTTING.value
        )
        return way_of_cutting == CutterType.BATTERY.value


class CuttingAccessoriesBackToPumpTransition(BaseTransition):
    """Is Default"""

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_PUMP_PRODUCTLIST

    def unless(self) -> list:
        return [CuttingAccessoriesBackToChargerBatteriesTransition().condition_method_name]


class CuttingAccessoriesSkipTransition(BaseTransition):
    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_OVERVIEW


class CuttingAccessoriesNextTransition(BaseTransition):
    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_OVERVIEW
