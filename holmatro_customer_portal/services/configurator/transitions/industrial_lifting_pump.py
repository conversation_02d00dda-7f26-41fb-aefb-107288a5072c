from __future__ import annotations

from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.services.configurator.configurator_enums import (
    ConfiguratorSelectionChoices,
    PumpType,
    SyncForceAttributeId,
)
from holmatro_customer_portal.services.configurator.queries import get_configuration_product_attribute
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorPumpStageDataIDs, ConfiguratorState
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition, TransitionTrigger
from holmatro_customer_portal.services.configurator.utils import (
    get_configuration_cylinder_acting_type,
    get_or_query_cylinder_quantity,
    get_or_query_pump_type,
    get_or_query_value,
)

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator


"""PUMP SETTINGS ACTING TRANSITIONS"""


class PumpSettingsActingNextTransition(BaseTransition):
    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS


class PumpSettingsActingBackToPullingClevisEyesTransition(BaseTransition):
    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        """Conditions: if cylinder pulling"""
        return (
            get_configuration_product_attribute(
                self._session,
                self._configuration,
                ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST,
                SyncForceAttributeId.CYLINDER_TYPE,
            )
            == "pulling"
        )


class PumpSettingsActingBackTransition(BaseTransition):
    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST

    def unless(self) -> list:
        return [PumpSettingsActingBackToPullingClevisEyesTransition().condition_method_name]


class PumpSettingsActingSkipTransition(BaseTransition):
    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST


"""PUMP SETTINGS TRANSITIONS"""


class PumpSettingsNextTransition(BaseTransition):
    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST


class PumpSettingsBackToActingTransition(BaseTransition):
    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        """
        Conditions:
        - If we should ask for single/double acting
        - If more than one cylinder was selected
        """
        acting_type = get_configuration_cylinder_acting_type(self._session, self._configuration)
        if acting_type is None:
            return True

        cylinder_quantity: int | None = get_or_query_cylinder_quantity(event, self._session, self._configuration)
        return isinstance(cylinder_quantity, int) and cylinder_quantity > 1


class PumpSettingsBackToPullingClevisEyesTransition(BaseTransition):
    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        """Conditions: if cylinder pulling and only one cylinder was selected"""
        cylinder_quantity = get_or_query_cylinder_quantity(event, self._session, self._configuration)

        if not isinstance(cylinder_quantity, int) or (isinstance(cylinder_quantity, int) and cylinder_quantity > 1):
            return False

        return (
            get_configuration_product_attribute(
                self._session,
                self._configuration,
                ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST,
                SyncForceAttributeId.CYLINDER_TYPE,
            )
            == "pulling"
        )


class PumpSettingsBackToCylinderTransition(BaseTransition):
    """Is Default"""

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST

    def unless(self) -> list:
        return [
            PumpSettingsBackToActingTransition().condition_method_name,
            PumpSettingsBackToPullingClevisEyesTransition().condition_method_name,
        ]


class PumpSettingsSkipTransition(BaseTransition):
    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST


"""PUMP PRODUCT LIST TRANSITIONS"""


class PumpListNextToGaugeTransition(BaseTransition):
    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        """
        Conditions:
        If hand pump with cylinder single acting type and selected to choose a gauge
        If hand pump with cylinder double acting type and selected to choose a gauge and not choose a valve
        If compact pump and selected to choose a gauge
        """
        pump_type: str | None = get_or_query_pump_type(event, self._session, self._configuration)
        if pump_type == PumpType.COMPACT.value:
            compact_pump_type: str | None = get_or_query_value(
                event,
                ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_COMPACT_POWER.value,
                self._session,
                self._configuration,
            )
            if compact_pump_type == PumpType.COMPACT_AIR.value:
                air_pump_gauge_selection: str | None = get_or_query_value(
                    event,
                    ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_COMPACT_AIR_GAUGE_SELECTION.value,
                    self._session,
                    self._configuration,
                )
                return air_pump_gauge_selection == ConfiguratorSelectionChoices.YES.value

            elif compact_pump_type == PumpType.COMPACT_ELECTRIC.value:
                electric_pump_gauge_selection: str | None = get_or_query_value(
                    event,
                    ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_COMPACT_ELECTRIC_GAUGE_SELECTION.value,
                    self._session,
                    self._configuration,
                )
                return (
                    electric_pump_gauge_selection == ConfiguratorSelectionChoices.GAUGE_AND_NEEDLE.value
                    or electric_pump_gauge_selection == ConfiguratorSelectionChoices.GAUGE_ONLY.value
                )

        elif pump_type == PumpType.HAND.value:
            hand_pump_gauge_selection: str | None = get_or_query_value(
                event,
                ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_GAUGESET_SELECTION.value,
                self._session,
                self._configuration,
            )
            if hand_pump_gauge_selection == ConfiguratorSelectionChoices.YES.value:
                hand_pump_valve_selection: str | None = get_or_query_value(
                    event,
                    ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_VALVE_SELECTION.value,
                    self._session,
                    self._configuration,
                )
                return (
                    not hand_pump_valve_selection or hand_pump_valve_selection == ConfiguratorSelectionChoices.NO.value
                )

        return False


class PumpListNextToValveTransition(BaseTransition):
    """
    If hand pump with cylinder double acting type and selected to choose a valve
    If vari pump and others options
    """

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        pump_type: str | None = get_or_query_pump_type(event, self._session, self._configuration)
        if pump_type == PumpType.HAND.value:
            hand_pump_valve_selection: str | None = get_or_query_value(
                event,
                ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_VALVE_SELECTION.value,
                self._session,
                self._configuration,
            )
            return hand_pump_valve_selection == ConfiguratorSelectionChoices.YES.value

        return pump_type == PumpType.VARI.value


class PumpListNextToHoseSettingsTransition(BaseTransition):
    """
    If hand pump, selected to NOT choose a valve neither a gauge and only ONE cylinder was chosen
    If compact pump, selected to NOT choose a gauge and only ONE cylinder was chosen
    """

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        cylinder_quantity: int | None = get_or_query_cylinder_quantity(event, self._session, self._configuration)
        if isinstance(cylinder_quantity, int) and cylinder_quantity > 1:
            return False

        pump_type: str | None = get_or_query_pump_type(event, self._session, self._configuration)
        if pump_type == PumpType.HAND.value:
            hand_pump_gaugeset_selection: str | None = get_or_query_value(
                event,
                ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_GAUGESET_SELECTION.value,
                self._session,
                self._configuration,
            )
            if hand_pump_gaugeset_selection == ConfiguratorSelectionChoices.NO.value:
                hand_pump_valve_selection: str | None = get_or_query_value(
                    event,
                    ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_VALVE_SELECTION.value,
                    self._session,
                    self._configuration,
                )
                return hand_pump_valve_selection in [ConfiguratorSelectionChoices.NO.value, None]

        elif pump_type == PumpType.COMPACT.value:
            compact_pump_type: str | None = get_or_query_value(
                event,
                ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_COMPACT_POWER.value,
                self._session,
                self._configuration,
            )
            if compact_pump_type == PumpType.COMPACT_ELECTRIC.value:
                compact_pump_electric_gaugeset_selection: str | None = get_or_query_value(
                    event,
                    ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_COMPACT_ELECTRIC_GAUGE_SELECTION.value,
                    self._session,
                    self._configuration,
                )
                return compact_pump_electric_gaugeset_selection == ConfiguratorSelectionChoices.NO_GAUGE.value
            elif compact_pump_type == PumpType.COMPACT_AIR.value:
                compact_pump_air_gaugeset_selection: str | None = get_or_query_value(
                    event,
                    ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_COMPACT_AIR_GAUGE_SELECTION.value,
                    self._session,
                    self._configuration,
                )
                return compact_pump_air_gaugeset_selection == ConfiguratorSelectionChoices.NO.value

        return False


class PumpListNextToFlowPanelTransition(BaseTransition):
    """
    Is Default
    Conditions:
    - If hand pump with cylinder double acting type and selected to NOT choose a valve neither a gauge
    - If hand pump with cylinder single acting type and selected to NOT choose a gauge
    - If electric compact pump and selected to NOT choose a valve neither a gauge
    - If air compact pump and selected to NOT choose a gauge
    """

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST

    def unless(self) -> list:
        return [
            PumpListNextToHoseSettingsTransition().condition_method_name,
            PumpListNextToValveTransition().condition_method_name,
            PumpListNextToGaugeTransition().condition_method_name,
        ]


class PumpListBackTransition(BaseTransition):
    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS


class PumpListSkipTransition(BaseTransition):
    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST
