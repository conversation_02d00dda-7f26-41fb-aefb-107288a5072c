from __future__ import annotations

from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.services.configurator.configurator_enums import (
    ConfiguratorSelectionChoices,
    FlowPanelType,
    PumpType,
)
from holmatro_customer_portal.services.configurator.states.base import (
    ConfiguratorFlowPanelStageDataIDs,
    ConfiguratorState,
)
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition, TransitionTrigger
from holmatro_customer_portal.services.configurator.utils import get_or_query_pump_type, get_or_query_value

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator


class SystemComponentsHoseProductListMainBackToFlowPanelSettingsTransition(BaseTransition):
    """
    Conditions:
    - If vari pump 'existing' type
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        pump_type = get_or_query_pump_type(event, self._session, self._configuration)

        if pump_type != PumpType.VARI.value:
            return False

        flowpanel_setup = get_or_query_value(
            event,
            ConfiguratorFlowPanelStageDataIDs.FLOW_PANEL_SETTINGS_TYPE.value,
            self._session,
            self._configuration,
        )
        return flowpanel_setup == FlowPanelType.EXISTING.value


class SystemComponentsHoseProductListMainBackToFlowPanelProductListTransition(BaseTransition):
    """
    Conditions:
    - If vari pump 'on top' type
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST

    def unless(self) -> list:
        return [SystemComponentsHoseProductListMainBackToFlowPanelSettingsTransition().condition_method_name]


class SystemComponentHoseProductListMainNextToOverview(BaseTransition):
    """
    Conditions:
    - If flow panel 'on top'
    """

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN
    dest = ConfiguratorState.INDUSTRIAL_OVERVIEW

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        pump_type = get_or_query_pump_type(event, self._session, self._configuration)

        if pump_type != PumpType.VARI.value:
            return False

        flowpanel_setup = get_or_query_value(
            event,
            ConfiguratorFlowPanelStageDataIDs.FLOW_PANEL_SETTINGS_TYPE.value,
            self._session,
            self._configuration,
        )
        return flowpanel_setup == FlowPanelType.ON_TOP.value


class SystemComponentsHoseProductListMainNextToProtectionFrameCoverTransition(BaseTransition):
    """
    Conditions:
    - If is vari pump, chosen flowpanel separate and selected to chose protection-frame cover
    """

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        pump_type = get_or_query_pump_type(event, self._session, self._configuration)

        if pump_type != PumpType.VARI.value:
            return False

        protection_frame_selection = get_or_query_value(
            event,
            ConfiguratorFlowPanelStageDataIDs.FLOW_PANEL_SETTINGS_FRAME_SELECTION.value,
            self._session,
            self._configuration,
        )

        return protection_frame_selection == ConfiguratorSelectionChoices.YES.value


class SystemComponentsHoseProductListMainNextToHoseProductListPumpToPanelTransition(BaseTransition):
    """
    Conditions:
    - If vari pump separate or existing
    """

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        pump_type = get_or_query_pump_type(event, self._session, self._configuration)

        if not pump_type:
            return True

        if pump_type != PumpType.VARI.value:
            return False

        protection_frame_selection = get_or_query_value(
            event,
            ConfiguratorFlowPanelStageDataIDs.FLOW_PANEL_SETTINGS_FRAME_SELECTION.value,
            self._session,
            self._configuration,
        )
        return protection_frame_selection != ConfiguratorSelectionChoices.YES.value


class SystemComponentsHoseProductListMainNextToHoseSettingsTransition(BaseTransition):
    """
    Is Default
    Conditions:
    - If hand pump
    - If compact pump
    """

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS

    def unless(self) -> list:
        return [
            SystemComponentsHoseProductListMainNextToProtectionFrameCoverTransition().condition_method_name,
            SystemComponentsHoseProductListMainNextToHoseProductListPumpToPanelTransition().condition_method_name,
            SystemComponentHoseProductListMainNextToOverview().condition_method_name,
        ]


class SystemComponentsHoseProductListMainSkipToOverviewTransition(BaseTransition):
    """
    Conditions:
    - If vari pump on top
    """

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN
    dest = ConfiguratorState.INDUSTRIAL_OVERVIEW

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        pump_type = get_or_query_pump_type(event, self._session, self._configuration)

        if pump_type != PumpType.VARI.value:
            return False

        flowpanel_setup = get_or_query_value(
            event,
            ConfiguratorFlowPanelStageDataIDs.FLOW_PANEL_SETTINGS_TYPE.value,
            self._session,
            self._configuration,
        )
        return flowpanel_setup == FlowPanelType.ON_TOP.value


class SystemComponentsHoseProductListMainSkipToHoseProductListPumpToPanelTransition(BaseTransition):
    """
    Conditions:
    - If vari pump separate or existing
    """

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        pump_type = get_or_query_pump_type(event, self._session, self._configuration)

        if not pump_type:
            return True

        if pump_type != PumpType.VARI.value:
            return False

        flowpanel_setup = get_or_query_value(
            event,
            ConfiguratorFlowPanelStageDataIDs.FLOW_PANEL_SETTINGS_TYPE.value,
            self._session,
            self._configuration,
        )
        return flowpanel_setup != FlowPanelType.ON_TOP.value


class SystemComponentsHoseProductListMainSkipToHoseSettingsTransition(BaseTransition):
    """
    Is Default
    Conditions:
    - If hand pump
    - If compact pump
    """

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS

    def unless(self) -> list:
        return [
            SystemComponentsHoseProductListMainNextToHoseProductListPumpToPanelTransition().condition_method_name,
        ]
