from __future__ import annotations

from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.services.configurator.configurator_enums import (
    ConfiguratorSelectionChoices,
    FlowPanelType,
)
from holmatro_customer_portal.services.configurator.queries import get_configuration_data_value
from holmatro_customer_portal.services.configurator.states.base import (
    ConfiguratorFlowPanelStageDataIDs,
    ConfiguratorState,
)
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition, TransitionTrigger
from holmatro_customer_portal.services.configurator.utils import get_or_query_value

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator


class SystemComponentsFlowPanelSettingsBackTransition(BaseTransition):
    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE


class SystemComponentsFlowPanelSettingsNextToGaugesetTransition(BaseTransition):
    """Conditions: If is vari pump, choosen flowpanel 'separate' and selected to choose gaugeset"""

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        gaugeset_selection = get_or_query_value(
            event,
            ConfiguratorFlowPanelStageDataIDs.FLOW_PANEL_SETTINGS_GAUGE_SELECTION.value,
            self._session,
            self._configuration,
        )
        return gaugeset_selection == ConfiguratorSelectionChoices.YES.value


class SystemComponentsFlowPanelSettingsNextToHosesProductListTransition(BaseTransition):
    """Conditions: If is vari pump, choosen flowpanel 'existing'"""

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        flow_panel_type = get_or_query_value(
            event,
            ConfiguratorFlowPanelStageDataIDs.FLOW_PANEL_SETTINGS_TYPE.value,
            self._session,
            self._configuration,
        )
        return flow_panel_type == FlowPanelType.EXISTING.value


class SystemComponentsFlowPanelSettingsNextToFlowPanelProductListTransition(BaseTransition):
    """Is Default"""

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST

    def unless(self) -> list:
        return [
            SystemComponentsFlowPanelSettingsNextToGaugesetTransition().condition_method_name,
            SystemComponentsFlowPanelSettingsNextToHosesProductListTransition().condition_method_name,
        ]


class SystemComponentsFlowPanelSettingsSkipToGaugesetTransition(BaseTransition):
    """Conditions: If is vari pump, choosen flowpanel 'separate' and selected to choose gaugeset"""

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        gaugeset_selection = get_configuration_data_value(
            self._session,
            self._configuration,
            ConfiguratorFlowPanelStageDataIDs.FLOW_PANEL_SETTINGS_GAUGE_SELECTION.value,
        )
        return gaugeset_selection == ConfiguratorSelectionChoices.YES.value


class SystemComponentsFlowPanelSettingsSkipToFlowPanelProductListTransition(BaseTransition):
    """Is Default"""

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST

    def unless(self) -> list:
        return [SystemComponentsFlowPanelSettingsSkipToGaugesetTransition().condition_method_name]
