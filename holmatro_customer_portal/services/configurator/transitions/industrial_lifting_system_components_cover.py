from __future__ import annotations

from typing import TYPE_CHECKING

from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition, TransitionTrigger

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator


class SystemComponentsCoverBackTransition(BaseTransition):
    """Is Default"""

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_COVER
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME


class SystemComponentsCoverNextTransition(BaseTransition):
    """Is Default"""

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_COVER
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL


class SystemComponentsCoverSkipTransition(BaseTransition):
    """Is Default"""

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_COVER
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL
