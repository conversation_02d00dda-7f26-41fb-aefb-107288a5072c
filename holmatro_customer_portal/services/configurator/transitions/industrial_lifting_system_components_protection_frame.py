from __future__ import annotations

from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.services.configurator.states.base import (
    ConfiguratorFlowPanelStageDataIDs,
    ConfiguratorState,
)
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition, TransitionTrigger
from holmatro_customer_portal.services.configurator.utils import get_or_query_products, get_or_query_value

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator


class SystemComponentsProtectionFrameBackTransition(BaseTransition):
    """Is Default"""

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN


class SystemComponentsProtectionFrameNextToCoverTransition(BaseTransition):
    """Is Default"""

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_COVER

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        """Conditions: if selected to choose a cover and a frame was selected"""
        protection_frame = get_or_query_products(
            event,
            ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME,
            self._session,
            self._configuration,
        )
        if not protection_frame:
            return False

        cover_selection = get_or_query_value(
            event,
            ConfiguratorFlowPanelStageDataIDs.FLOW_PANEL_SETTINGS_COVER_SELECTION.value,
            self._session,
            self._configuration,
        )
        return cover_selection != "no"


class SystemComponentsProtectionFrameNextTransition(BaseTransition):
    """Is Default"""

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL

    def unless(self) -> list:
        return [SystemComponentsProtectionFrameNextToCoverTransition().condition_method_name]


class SystemComponentsProtectionFrameSkipTransition(BaseTransition):
    """Is Default"""

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL
