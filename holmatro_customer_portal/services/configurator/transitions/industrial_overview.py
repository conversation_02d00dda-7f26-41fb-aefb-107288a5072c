from __future__ import annotations

from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.services.configurator.configurator_enums import FlowPanelType, HoseTypeSelection, PumpType
from holmatro_customer_portal.services.configurator.states.base import (
    ConfiguratorFlowPanelStageDataIDs,
    ConfiguratorHoseStageDataIDs,
    ConfiguratorState,
)
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition, TransitionTrigger
from holmatro_customer_portal.services.configurator.utils import get_or_query_pump_type, get_or_query_value
from holmatro_customer_portal.utils.enums import SyncforceCategory

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator


class OverviewNextTransition(BaseTransition):
    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_OVERVIEW
    dest = ConfiguratorState.INDUSTRIAL_OVERVIEW


class OverviewBackToHoseSettingsTransition(BaseTransition):
    """Conditions: if lifting configuration and hose selected as existing"""

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_OVERVIEW
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        if not self._configuration.category.category_id == str(SyncforceCategory.INDUSTRIAL_LIFTING.value):
            return False

        hose_type = get_or_query_value(
            event,
            ConfiguratorHoseStageDataIDs.HOSE_SETTINGS_TYPE.value,
            self._session,
            self._configuration,
        )
        return hose_type == HoseTypeSelection.EXISTING.value


class OverviewBackToHoseProductListMainTransition(BaseTransition):
    """Conditions: if lifting configuration and vari pump on top"""

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_OVERVIEW
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        if not self._configuration.category.category_id == str(SyncforceCategory.INDUSTRIAL_LIFTING.value):
            return False

        pump_type = get_or_query_pump_type(event, self._session, self._configuration)

        if pump_type != PumpType.VARI.value:
            return False

        flowpanel_setup = get_or_query_value(
            event,
            ConfiguratorFlowPanelStageDataIDs.FLOW_PANEL_SETTINGS_TYPE.value,
            self._session,
            self._configuration,
        )
        return flowpanel_setup == FlowPanelType.ON_TOP.value


class OverviewBackToHosePumpToPanelTransition(BaseTransition):
    """Conditions: if lifting configuration"""

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_OVERVIEW
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        if not self._configuration.category.category_id == str(SyncforceCategory.INDUSTRIAL_LIFTING.value):
            return False

        return True


class OverviewBackTransition(BaseTransition):
    """Conditions: if cutting configuration"""

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_OVERVIEW
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        if not self._configuration.category.category_id == str(SyncforceCategory.INDUSTRIAL_CUTTING.value):
            return False

        return True
