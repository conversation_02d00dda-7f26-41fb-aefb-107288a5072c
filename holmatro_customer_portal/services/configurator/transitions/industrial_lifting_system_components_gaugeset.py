from __future__ import annotations

from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.services.configurator.configurator_enums import ConfiguratorSelectionChoices, PumpType
from holmatro_customer_portal.services.configurator.queries import get_configuration_data_value, get_product_quantities
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorPumpStageDataIDs, ConfiguratorState
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition, TransitionTrigger
from holmatro_customer_portal.services.configurator.utils import get_configuration_pump_type

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator


class SystemComponentsGaugesetSkipToFlowPanelProductListTransition(BaseTransition):
    """
    Conditions:
    - If hand pump and more than ONE cylinder is selected
    - If compact pump and more than ONE cylinder is selected
    - If vari pump
    """

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        pump_type = get_configuration_pump_type(self._session, self._configuration)

        if pump_type == PumpType.VARI.value:
            return True

        cylinder_quantity: dict | None = get_product_quantities(
            self._session, self._configuration, ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST
        )
        cylinder_total_quantity = cylinder_quantity.get("total_quantity") if cylinder_quantity else None
        if isinstance(cylinder_total_quantity, int) and cylinder_total_quantity > 1:
            return pump_type == PumpType.HAND.value or pump_type == PumpType.COMPACT.value
        else:
            return False


class SystemComponentsGaugesetSkipToHoseSettingsTransition(BaseTransition):
    """
    Is Default
    Conditions:
    - If hand pump single acting and ONE cylinder is selected
    - If compact pump and ONE cylinder is selected
    """

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS

    def unless(self) -> list:
        return [SystemComponentsGaugesetSkipToFlowPanelProductListTransition().condition_method_name]


class SystemComponentsGaugesetBackToValveTransition(BaseTransition):
    """
    Conditions:
    - If hand pump double acting and selected to choose a valve
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        pump_type = get_configuration_pump_type(self._session, self._configuration)
        if not pump_type or pump_type != PumpType.HAND.value:
            return False

        hand_pump_valve_selection: str | None = get_configuration_data_value(
            self._session, self._configuration, ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_VALVE_SELECTION.value
        )
        return hand_pump_valve_selection == ConfiguratorSelectionChoices.YES.value


class SystemComponentsGaugesetBackToFlowPanelSettingsTransition(BaseTransition):
    """
    Conditions:
    - If vari pump 'separate' type
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        return get_configuration_pump_type(self._session, self._configuration) == PumpType.VARI.value


class SystemComponentsGaugesetBackToPumpProductListTransition(BaseTransition):
    """
    Is Default
    Conditions:
    - If hand pump single acting
    - If hand pump double acting and selected to NOT choose a valve
    - If compact pump
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST

    def unless(self) -> list:
        return [
            SystemComponentsGaugesetBackToFlowPanelSettingsTransition().condition_method_name,
            SystemComponentsGaugesetBackToValveTransition().condition_method_name,
        ]


class SystemComponentsGaugesetNextToHoseSettingsTransition(BaseTransition):
    """
    Conditions:
    - If hand pump single acting and ONE cylinder is selected
    - If compact pump and ONE cylinder is selected
    """

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        cylinder_quantity: dict | None = get_product_quantities(
            self._session, self._configuration, ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST
        )
        cylinder_total_quantity = cylinder_quantity.get("total_quantity") if cylinder_quantity else None
        if not isinstance(cylinder_total_quantity, int) or cylinder_total_quantity > 1:
            return False

        pump_type = get_configuration_pump_type(self._session, self._configuration)
        return pump_type == PumpType.HAND.value or pump_type == PumpType.COMPACT.value


class SystemComponentsGaugesetNextToFlowPanelProductlistTransition(BaseTransition):
    """
    Is Default
    Conditions:
     - If hand pump and more than ONE cylinder is selected
     - If compact pump and more than ONE cylinder is selected
     - If vari pump
    """

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST

    def unless(self) -> list:
        return [SystemComponentsGaugesetNextToHoseSettingsTransition().condition_method_name]
