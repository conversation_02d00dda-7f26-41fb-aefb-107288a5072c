from __future__ import annotations

from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition, TransitionTrigger


class CutterSettingsBackTransition(BaseTransition):
    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_CUTTING_PUMP_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_HOSE_PRODUCTLIST


class CutterSettingsSkipTransition(BaseTransition):
    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_CUTTING_PUMP_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST


class CutterSettingsNextTransition(BaseTransition):
    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_CUTTING_PUMP_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST
