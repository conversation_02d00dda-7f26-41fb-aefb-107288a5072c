from __future__ import annotations

from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.services.configurator.configurator_enums import (
    ConfiguratorSelectionChoices,
    FlowPanelType,
    PumpType,
)
from holmatro_customer_portal.services.configurator.states.base import (
    ConfiguratorFlowPanelStageDataIDs,
    ConfiguratorPumpStageDataIDs,
    ConfiguratorState,
)
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition, TransitionTrigger
from holmatro_customer_portal.services.configurator.utils import (
    get_or_query_cylinder_acting_type,
    get_or_query_pump_type,
    get_or_query_value,
)

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator


class SystemComponentsFlowPanelProductListBackToGaugeTransition(BaseTransition):
    """
    Conditions:
    - If hand or compact pump/ if selected to choose a gauge
    - If compact pump and selected to choose a gauge
    - If vari pump, flowpanel is separate and selected to choose a gauge
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        hand_pump_gaugeset_selection = get_or_query_value(
            event,
            ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_GAUGESET_SELECTION.value,
            self._session,
            self._configuration,
        )
        if hand_pump_gaugeset_selection == ConfiguratorSelectionChoices.YES.value:
            return True

        compact_pump_air_gaugeset_selection = get_or_query_value(
            event,
            ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_COMPACT_AIR_GAUGE_SELECTION.value,
            self._session,
            self._configuration,
        )
        if compact_pump_air_gaugeset_selection == ConfiguratorSelectionChoices.YES.value:
            return True

        compact_pump_electric_gaugeset_selection = get_or_query_value(
            event,
            ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_COMPACT_ELECTRIC_GAUGE_SELECTION.value,
            self._session,
            self._configuration,
        )
        if compact_pump_electric_gaugeset_selection in [
            ConfiguratorSelectionChoices.GAUGE_ONLY.value,
            ConfiguratorSelectionChoices.GAUGE_AND_NEEDLE.value,
        ]:
            return True

        vari_pump_gaugeset_selection = get_or_query_value(
            event,
            ConfiguratorFlowPanelStageDataIDs.FLOW_PANEL_SETTINGS_GAUGE_SELECTION.value,
            self._session,
            self._configuration,
        )
        return vari_pump_gaugeset_selection == ConfiguratorSelectionChoices.YES.value


class SystemComponentsFlowPanelProductListBackToValveTransition(BaseTransition):
    """
    Conditions:
    - If hand pump double acting, selected to NOT choose a gauge and selected to choose a valve
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        hand_pump_gaugeset_selection = get_or_query_value(
            event,
            ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_GAUGESET_SELECTION.value,
            self._session,
            self._configuration,
        )

        if hand_pump_gaugeset_selection == ConfiguratorSelectionChoices.NO.value:
            hand_pump_valve_selection: str | None = get_or_query_value(
                event,
                ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_VALVE_SELECTION.value,
                self._session,
                self._configuration,
            )

            return hand_pump_valve_selection == ConfiguratorSelectionChoices.YES.value

        return False


class SystemComponentsFlowPanelProductListBackToPumpSettingsTransition(BaseTransition):
    """
    Conditions:
    - If pump type is unknown
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        pump_type = get_or_query_pump_type(event, self._session, self._configuration)
        if pump_type is None:
            cylinder_acting_type = get_or_query_cylinder_acting_type(event, self._session, self._configuration)
            return cylinder_acting_type is not None
        else:
            return False


class SystemComponentsFlowPanelProductListBackToPumpActingTransition(BaseTransition):
    """
    Conditions:
    - If pump type unknown and NO cylinder is selected
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        pump_type = get_or_query_pump_type(event, self._session, self._configuration)
        if pump_type is None:
            cylinder_acting_type = get_or_query_cylinder_acting_type(event, self._session, self._configuration)
            return cylinder_acting_type is None
        else:
            return False


class SystemComponentsFlowPanelProductListBackToFlowPanelSettingsTransition(BaseTransition):
    """
    Conditions:
    - If vari pump 'separate' and selected to NOT choose a gauge
    - If vari pump 'on top'
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        pump_type = get_or_query_pump_type(event, self._session, self._configuration)
        if pump_type != PumpType.VARI.value:
            return False

        flow_panel_type = get_or_query_value(
            event, ConfiguratorFlowPanelStageDataIDs.FLOW_PANEL_SETTINGS_TYPE.value, self._session, self._configuration
        )
        if flow_panel_type == FlowPanelType.SEPARATE.value:
            vari_pump_gaugeset_selection = get_or_query_value(
                event,
                ConfiguratorFlowPanelStageDataIDs.FLOW_PANEL_SETTINGS_GAUGE_SELECTION.value,
                self._session,
                self._configuration,
            )
            return vari_pump_gaugeset_selection == ConfiguratorSelectionChoices.NO.value

        return flow_panel_type == FlowPanelType.ON_TOP.value


class SystemComponentsFlowPanelProductListBackToPumpProductListTransition(BaseTransition):
    """
    Is Default
    Conditions:
    - If hand pump double acting, selected to NOT choose a gauge and selected to NOT choose a valve
    - If hand pump single acting and selected to NOT choose a gauge
    - If compact pump and selected to NOT choose a gauge
    """

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST

    def unless(self) -> list:
        return [
            SystemComponentsFlowPanelProductListBackToGaugeTransition().condition_method_name,
            SystemComponentsFlowPanelProductListBackToValveTransition().condition_method_name,
            SystemComponentsFlowPanelProductListBackToPumpSettingsTransition().condition_method_name,
            SystemComponentsFlowPanelProductListBackToPumpActingTransition().condition_method_name,
            SystemComponentsFlowPanelProductListBackToFlowPanelSettingsTransition().condition_method_name,
        ]


class SystemComponentsFlowPanelProductListNextTransition(BaseTransition):
    """
    Is Default
    """

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN


class SystemComponentsFlowPanelProductListSkipToHoseSettingsTransition(BaseTransition):
    """
    Conditions:
    - If hand pump
    - If compact pump
    """

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        pump_type = get_or_query_pump_type(event, self._session, self._configuration)
        return pump_type in [PumpType.HAND.value, PumpType.COMPACT.value]


class SystemComponentsFlowPanelProductListSkipToHoseProductListPumptoPanelTransition(BaseTransition):
    """
    Is Default
    Conditions:
    - If vari pump 'on top' type
    - If no choice is made
    """

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL

    def unless(self) -> list:
        return [
            SystemComponentsFlowPanelProductListSkipToHoseSettingsTransition().condition_method_name,
        ]
