from __future__ import annotations

from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.services.configurator.configurator_enums import CutterApplication, CutterType
from holmatro_customer_portal.services.configurator.queries import get_configuration_data_value
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorCutterStageDataIDs, ConfiguratorState
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition, TransitionTrigger

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator

"""CUTTER SETTINGS TRANSITIONS"""


class CutterSettingsBackTransition(BaseTransition):
    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_SETTINGS


class CutterSettingsSkipToChargerTransition(BaseTransition):
    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_CHARGER_BATTERIES_PRODUCTLIST

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        return (
            get_configuration_data_value(
                self._session, self._configuration, ConfiguratorCutterStageDataIDs.CUTTER_WAY_OF_CUTTING.value
            )
            == CutterType.BATTERY.value
        )


class CutterSettingsSkipToHoseTransition(BaseTransition):
    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_HOSE_PRODUCTLIST

    def unless(self) -> list:
        return [CutterSettingsSkipToChargerTransition().condition_method_name]


class CutterSettingsNextToProductListTransition(BaseTransition):
    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        """
        Conditions:
        - If application is Fridge Recycling
        """
        return (
            get_configuration_data_value(
                self._session, self._configuration, ConfiguratorCutterStageDataIDs.CUTTER_SETTINGS_APPLICATION.value
            )
            == CutterApplication.FRIDGE_RECYCLING.value
        )


class CutterSettingsNextToWayOfCuttingTransition(BaseTransition):
    """Is default"""

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_WAY_OF_CUTTING

    def unless(self) -> list:
        return [CutterSettingsNextToProductListTransition().condition_method_name]


"""CUTTER WAY OF CUTTING TRANSITIONS"""


class CutterWayOfCuttingBackTransition(BaseTransition):
    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_WAY_OF_CUTTING
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_SETTINGS


class CutterWayOfCuttingSkipToChargerTransition(BaseTransition):
    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_WAY_OF_CUTTING
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_CHARGER_BATTERIES_PRODUCTLIST

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        return (
            get_configuration_data_value(
                self._session, self._configuration, ConfiguratorCutterStageDataIDs.CUTTER_WAY_OF_CUTTING.value
            )
            == CutterType.BATTERY.value
        )


class CutterWayOfCuttingSkipToHoseTransition(BaseTransition):
    """Is default"""

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_WAY_OF_CUTTING
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_HOSE_PRODUCTLIST

    def unless(self) -> list:
        return [CutterWayOfCuttingSkipToChargerTransition().condition_method_name]


class CutterWayOfCuttingNextTransition(BaseTransition):
    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_WAY_OF_CUTTING
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST


"""CUTTER PRODUCT LIST TRANSITIONS"""


class CutterProductListBackToCutterSettingsTransition(BaseTransition):
    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_SETTINGS

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        """
        Conditions:
        - If application is Fridge Recycling
        """
        return (
            get_configuration_data_value(
                self._session, self._configuration, ConfiguratorCutterStageDataIDs.CUTTER_SETTINGS_APPLICATION.value
            )
            == CutterApplication.FRIDGE_RECYCLING.value
        )


class CutterProductListBackToWayOfCuttingTransition(BaseTransition):
    """Is default"""

    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_WAY_OF_CUTTING

    def unless(self) -> list:
        return [CutterProductListBackToCutterSettingsTransition().condition_method_name]


class CutterProductListSkipToChargerTransition(BaseTransition):
    """
    Conditions:
    - When way of cutting is battery
    """

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_CHARGER_BATTERIES_PRODUCTLIST

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        return (
            get_configuration_data_value(
                self._session, self._configuration, ConfiguratorCutterStageDataIDs.CUTTER_WAY_OF_CUTTING.value
            )
            == CutterType.BATTERY.value
        )


class CutterProductListSkipToHoseTransition(BaseTransition):
    """Is default"""

    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_HOSE_PRODUCTLIST

    def unless(self) -> list:
        return [CutterProductListSkipToChargerTransition().condition_method_name]


class CutterProductListNextToChargerTransition(BaseTransition):
    """Is default"""

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_CHARGER_BATTERIES_PRODUCTLIST

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        return (
            get_configuration_data_value(
                self._session, self._configuration, ConfiguratorCutterStageDataIDs.CUTTER_WAY_OF_CUTTING.value
            )
            == CutterType.BATTERY.value
        )


class CutterProductListNextToHoseTransition(BaseTransition):
    """Is default"""

    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_CUTTING_HOSE_PRODUCTLIST

    def unless(self) -> list:
        return [CutterProductListNextToChargerTransition().condition_method_name]
