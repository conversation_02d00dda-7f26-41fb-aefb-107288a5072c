from __future__ import annotations

from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.services.configurator.configurator_enums import SyncForceAttributeId
from holmatro_customer_portal.services.configurator.queries import get_configuration_product_attribute
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.services.configurator.transitions.base import BaseTransition, TransitionTrigger
from holmatro_customer_portal.services.configurator.utils import (
    get_configuration_cylinder_acting_type,
    get_or_query_cylinder_quantity,
)

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator


"""CYLINDER SETTINGS TRANSITION"""


class CylinderSettingsNextTransition(BaseTransition):
    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST


class CylinderSettingsBackTransition(BaseTransition):
    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS


class CylinderSettingsSkipToPumpSettingsActingTransition(BaseTransition):
    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING


"""CYLINDER PRODUCT LIST TRANSITIONS"""


class CylinderListNextToPullingClevisEyesTransition(BaseTransition):
    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_CLEVIS_EYES

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        """
        Conditions:
        - If chosen cylinder type pulling
        """
        return (
            get_configuration_product_attribute(
                self._session,
                self._configuration,
                ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST,
                SyncForceAttributeId.CYLINDER_TYPE,
            )
            == "pulling"
        )


class CylinderListNextToPumpSettingsActingTransition(BaseTransition):
    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        """
        Conditions:
        - If we should ask for single/double acting
        - If more than one cylinder is selected
        """
        if (
            get_configuration_product_attribute(
                self._session,
                self._configuration,
                ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST,
                SyncForceAttributeId.CYLINDER_TYPE,
            )
            == "pulling"
        ):
            return False

        cylinder_quantity = get_or_query_cylinder_quantity(event, self._session, self._configuration)
        if isinstance(cylinder_quantity, int):
            return cylinder_quantity > 1

        return get_configuration_cylinder_acting_type(self._session, self._configuration) is None


class CylinderListNextToPumpSettingsTransition(BaseTransition):
    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS

    def unless(self) -> list:
        return [
            CylinderListNextToPullingClevisEyesTransition().condition_method_name,
            CylinderListNextToPumpSettingsActingTransition().condition_method_name,
        ]


class CylinderListBackTransition(BaseTransition):
    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS


class CylinderListSkipToPumpSettingsActingTransition(BaseTransition):
    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING


"""CYLINDER PULLING CLEVIS EYES TRANSITION"""


class PullingClevisEyesNextTransition(BaseTransition):
    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_CLEVIS_EYES
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING


class PullingClevisEyesBackTransition(BaseTransition):
    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_CLEVIS_EYES
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST


class PullingClevisEyesSkipToPumpActingTransition(BaseTransition):
    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_CLEVIS_EYES
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING


"""CYLINDER PULLING PROTECTION SPRING TRANSITIONS"""


class PullingProtectionSpringNextToPumpSettingActingTransition(BaseTransition):
    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        """
        Conditions:
        - If cylinder 60T and more than one cylinder was chosen
        """
        cylinder_quantity = get_or_query_cylinder_quantity(event, self._session, self._configuration)
        return isinstance(cylinder_quantity, int) and cylinder_quantity > 1


class PullingProtectionSpringNextToPumpActingTransition(BaseTransition):
    trigger = TransitionTrigger.NEXT
    source = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS

    def unless(self) -> list:
        return [PullingProtectionSpringNextToPumpSettingActingTransition().condition_method_name]


class PullingProtectionSpringBackTransition(BaseTransition):
    trigger = TransitionTrigger.BACK
    source = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_CLEVIS_EYES


class PullingProtectionSpringSkipToPumpActingTransition(BaseTransition):
    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING

    def condition(self: BaseTransition | Configurator, event: EventData) -> bool:
        """
        Conditions:
        - If cylinder 60T and more than one cylinder was chosen
        """
        cylinder_quantity = get_or_query_cylinder_quantity(event, self._session, self._configuration)
        return isinstance(cylinder_quantity, int) and cylinder_quantity > 1


class PullingProtectionSpringSkipToPumpSettingsTransition(BaseTransition):
    trigger = TransitionTrigger.SKIP
    source = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING
    dest = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS

    def unless(self) -> list:
        return [
            PullingProtectionSpringSkipToPumpActingTransition().condition_method_name,
        ]
