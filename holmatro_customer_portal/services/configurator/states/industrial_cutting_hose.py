from __future__ import annotations

from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.services.configurator.configurator_enums import (
    CutterApplication,
    CutterRequirements,
    CutterType,
    FilterName,
)
from holmatro_customer_portal.services.configurator.queries import get_configuration_data_value, get_product_quantities
from holmatro_customer_portal.services.configurator.states.base import (
    BaseState,
    ConfiguratorCutterStageDataIDs,
    ConfiguratorState,
)
from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.tweakwise.tweakwise_client import TweakwiseClient

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class HoseProductList(BaseState):
    state = ConfiguratorState.INDUSTRIAL_CUTTING_HOSE_PRODUCTLIST

    def on_enter(self: BaseState | Configurator, event: EventData) -> bool:
        _logger.debug(f"Fetching on enter information for {self.state}")

        category_path: str | None = self._catalog_repository.get_category_path(
            self._configuration.user.language.code, SyncforceCategory.CUTTER_HOSES, SyncforceCategory.INDUSTRIAL_CUTTING
        )
        if category_path:
            for c in self.state_definition["content"]:
                if c["type"] == "filters":
                    c["meta"]["category"] = category_path

        filters = {}

        application = get_configuration_data_value(
            self._session,
            self._configuration,
            ConfiguratorCutterStageDataIDs.CUTTER_SETTINGS_APPLICATION.value,
        )

        if application == CutterApplication.FRIDGE_RECYCLING.value:
            fridge_recycling_user_requirements = get_configuration_data_value(
                self._session,
                self._configuration,
                ConfiguratorCutterStageDataIDs.CUTTER_SETTINGS_REQUIREMENTS.value,
            )
            if fridge_recycling_user_requirements == CutterRequirements.FRIDGE_RECYCLING_TWIST_GRIP.value:
                filters.update({FilterName.CUTTER_TYPE.value: CutterType.MOBILE.value})

            # Make sure to compare user requirements with Fridge recycling requirements
            elif fridge_recycling_user_requirements:
                filters.update({FilterName.CUTTER_TYPE.value: CutterType.STATIONARY.value})

        else:
            way_of_cutting = get_configuration_data_value(
                self._session, self._configuration, ConfiguratorCutterStageDataIDs.CUTTER_WAY_OF_CUTTING.value
            )
            if way_of_cutting == CutterType.MOBILE.value:
                filters.update({FilterName.CUTTER_TYPE.value: CutterType.MOBILE.value})
            elif way_of_cutting == CutterType.STATIONARY.value:
                filters.update({FilterName.CUTTER_TYPE.value: CutterType.STATIONARY.value})

        if filters:
            for c in self.state_definition["content"]:
                if c["type"] == "filters":
                    c["meta"]["filters"] = filters

        quantity_cutter = get_product_quantities(
            self._session, self._configuration, ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST
        )
        if quantity_cutter:
            for c in self.state_definition["content"]:
                if c["type"] == "products":
                    c["meta"]["initial"] = quantity_cutter.get("total_quantity") or 1
