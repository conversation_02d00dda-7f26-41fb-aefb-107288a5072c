from __future__ import annotations

from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.mappers.product_preview_mapper import ProductPreviewMapper
from holmatro_customer_portal.schemas.response_schema import ProductPreviewRes
from holmatro_customer_portal.services.configurator.configurator_enums import (
    CylinderActingType,
    FilterName,
    HoseTypeFilter,
)
from holmatro_customer_portal.services.configurator.default_products import FlowPanel, Hoses
from holmatro_customer_portal.services.configurator.queries import get_product_quantities
from holmatro_customer_portal.services.configurator.states import BaseState, ConfiguratorState
from holmatro_customer_portal.services.configurator.states.system_components_helpers.flowpanel_productlist import (
    get_flowpanel_assemblyset_and_couplers,
    get_product_flowpanel,
)
from holmatro_customer_portal.services.configurator.states.system_components_helpers.gaugeset import (
    add_additional_products_to_gauge,
    get_product_gaugeset,
)
from holmatro_customer_portal.services.configurator.states.system_components_helpers.hose_couplers import (
    add_couplers_to_configuration,
)
from holmatro_customer_portal.services.configurator.states.system_components_helpers.hose_productlist import (
    set_hose_state_content,
)
from holmatro_customer_portal.services.configurator.states.system_components_helpers.valve import get_product_valves
from holmatro_customer_portal.services.configurator.utils import (
    get_or_query_cylinder_acting_type,
    get_or_query_cylinder_quantity,
)
from holmatro_customer_portal.services.utils.product_queries import get_product_preview_by_article_number
from holmatro_customer_portal.utils.enums import SyncforceCategory

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator


class SystemComponentsValve(BaseState):
    state = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE

    def on_enter(self: BaseState | Configurator, event: EventData) -> None:
        """Add and prefill the valve product list."""
        products = get_product_valves(self._session, self._configuration, self._my_hm_client, event)
        pump_quantity: dict | None = get_product_quantities(
            self._session, self._configuration, ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST
        )
        for c in self.state_definition["content"]:
            if c["type"] == "products":
                c["meta"]["products"] = products
                c["meta"]["initial"] = pump_quantity.get("total_quantity") if pump_quantity else 1


class SystemComponentsGaugeset(BaseState):
    state = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET

    def on_enter(self: BaseState | Configurator, event: EventData) -> None:
        """Add and prefill the gaugeset product list."""
        products = get_product_gaugeset(self._session, self._configuration, self._my_hm_client, event)
        pump_quantity: dict | None = get_product_quantities(
            self._session, self._configuration, ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST
        )
        for c in self.state_definition["content"]:
            if c["type"] == "products":
                c["meta"]["products"] = products
                c["meta"]["initial"] = pump_quantity.get("total_quantity") if pump_quantity else 1

    def on_exit(self: BaseState | Configurator, event: EventData) -> None:
        add_additional_products_to_gauge(event, self._session, self._configuration, self.state)


class SystemComponentsFlowPanelSettings(BaseState):
    state = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS


class SystemComponentsFlowPanelProductList(BaseState):
    state = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST

    def on_enter(self: BaseState | Configurator, event: EventData) -> None:
        """Add and prefill the flowpanel filters according to the cylinder acting type."""
        get_product_flowpanel(
            event, self._session, self._configuration, self.state_definition, self._catalog_repository
        )

    def on_exit(self: BaseState | Configurator, event: EventData) -> None:
        """
        Determine the quantity and type of couplers and assembly set depending on the quantity and type of flowpanel
        """
        get_flowpanel_assemblyset_and_couplers(event, self._session, self._configuration, self.state)


class SystemComponentsHoseSetting(BaseState):
    state = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS


class SystemComponentsHoseProductListMain(BaseState):
    state = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN

    def on_enter(self: BaseState | Configurator, event: EventData) -> None:
        """
        Add and prefill the hoses product list.
        1: determine the quantity and type of cylinder to define the minimum amount of hoses
        2: set default hose, hose filter and hose category
        """
        cylinder_quantity = get_or_query_cylinder_quantity(event, self._session, self._configuration)
        cylinder_acting_type = get_or_query_cylinder_acting_type(event, self._session, self._configuration)

        for c in self.state_definition["content"]:
            if c["type"] == "products" and isinstance(cylinder_quantity, int):
                if cylinder_acting_type == CylinderActingType.DOUBLE.value:
                    c["meta"]["initial"] = 2 * cylinder_quantity
                else:
                    c["meta"]["initial"] = cylinder_quantity

        for c in self.state_definition["content"]:
            if c["type"] == "filters":
                c["meta"]["filters"] = {
                    f"{self._configuration.user.language.code}-{FilterName.HOSE_TYPE.value}": f"{HoseTypeFilter.EXTENSION.value}"
                }
                c["meta"]["category"] = self._catalog_repository.get_category_path(
                    self._configuration.user.language.code,
                    SyncforceCategory.LIFTING_HOSES,
                    SyncforceCategory.INDUSTRIAL_LIFTING,
                )

        self.set_default_product(Hoses.EXTENSION_HOSE_2M.value)


class SystemComponentsHoseProductListPumpToPanel(BaseState):
    state = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL

    def on_enter(self: BaseState | Configurator, event: EventData) -> None:
        """Add and prefill the hoses product list."""
        set_hose_state_content(self, event, self._session, self._configuration, self._catalog_repository)

    def on_exit(self: BaseState | Configurator, event: EventData) -> None:
        """Determine the quantity and type of couplers depending on the quantity and type of cylinder"""
        add_couplers_to_configuration(event, self._session, self._configuration, self.state)


class SystemComponentsProtectionFrame(BaseState):
    state = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME

    def on_enter(self: BaseState | Configurator, event: EventData) -> None:
        """Add and prefill the frame and cover product list."""
        product_article_numbers = FlowPanel.FRAME.value

        def get_products(product_article_number: list[str] | str) -> list[ProductPreviewRes]:
            products = get_product_preview_by_article_number(
                product_article_number, self._configuration.user, self._session
            )
            return ProductPreviewMapper(self._session, self._my_hm_client).product_preview_mapper(
                self._configuration.user, products
            )

        for c in self.state_definition["content"]:
            if c["type"] == "products":
                c["meta"]["products"] = get_products(product_article_numbers)


class SystemComponentsCover(BaseState):
    state = ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_COVER

    def on_enter(self: BaseState | Configurator, event: EventData) -> None:
        """Add and prefill the frame and cover product list."""
        product_article_numbers = FlowPanel.COVER.value

        def get_products(product_article_number: list[str] | str) -> list[ProductPreviewRes]:
            products = get_product_preview_by_article_number(
                product_article_number, self._configuration.user, self._session
            )
            return ProductPreviewMapper(self._session, self._my_hm_client).product_preview_mapper(
                self._configuration.user, products
            )

        for c in self.state_definition["content"]:
            if c["type"] == "products":
                c["meta"]["products"] = get_products(product_article_numbers)
