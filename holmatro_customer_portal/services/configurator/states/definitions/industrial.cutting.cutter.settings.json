{"stage": "industrial.cutting.cutter", "type": "settings", "id": "industrial.cutting.cutter.settings", "title": "industrial.cutting.cutter.settings.title", "subtitle": "industrial.cutting.cutter.settings.subtitle", "icon": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/icons/cutting/Cutter+(not+animated).svg", "content": [{"id": "industrial.cutting.cutter.settings.application", "title": "industrial.cutting.cutter.settings.application.title", "subtitle": null, "type": "choice", "condition": null, "meta": {"type": "button", "choices": [{"title": "industrial.cutting.cutter.settings.application.cable_cutting.title", "img": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/configurator/cutting/Cable+recycling.png", "value": "cable_cutting"}, {"title": "industrial.cutting.cutter.settings.application.car_recycling.title", "img": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/configurator/cutting/Car+recycling.png", "value": "car_recycling"}, {"title": "industrial.cutting.cutter.settings.application.fridge_recycling.title", "img": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/configurator/cutting/Domestic+appliance.png", "value": "fridge_recycling"}, {"title": "industrial.cutting.cutter.settings.application.metal_recycling.title", "img": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/configurator/cutting/Metal+-+Scrap.png", "value": "metal_recycling"}]}}, {"id": "industrial.cutting.cutter.settings.frequency", "title": "industrial.cutting.cutter.settings.cable_cutting.frequency.title", "subtitle": null, "type": "choice", "condition": {"type": "equals", "field": "industrial.cutting.cutter.settings.application", "value": "cable_cutting"}, "meta": {"type": "radio", "choices": [{"title": "industrial.cutting.cutter.settings.cable_cutting.frequency.low.title", "value": "low"}, {"title": "industrial.cutting.cutter.settings.cable_cutting.frequency.medium.title", "value": "medium"}, {"title": "industrial.cutting.cutter.settings.cable_cutting.frequency.high.title", "value": "high"}]}}, {"id": "industrial.cutting.cutter.settings.requirements", "title": "industrial.cutting.cutter.settings.cable_cutting.requirements.title", "subtitle": null, "type": "choice", "condition": {"type": "equals", "field": "industrial.cutting.cutter.settings.application", "value": "cable_cutting"}, "meta": {"type": "radio", "choices": [{"title": "industrial.cutting.cutter.settings.cable_cutting.requirements.small.title", "value": "small"}, {"title": "industrial.cutting.cutter.settings.cable_cutting.requirements.medium.title", "value": "medium"}, {"title": "industrial.cutting.cutter.settings.cable_cutting.requirements.large.title", "value": "large"}]}}, {"id": "industrial.cutting.cutter.settings.frequency", "title": "industrial.cutting.cutter.settings.car_recycling.frequency.title", "subtitle": null, "type": "choice", "condition": {"type": "equals", "field": "industrial.cutting.cutter.settings.application", "value": "car_recycling"}, "meta": {"type": "radio", "choices": [{"title": "industrial.cutting.cutter.settings.car_recycling.frequency.low.title", "value": "low"}, {"title": "industrial.cutting.cutter.settings.car_recycling.frequency.medium.title", "value": "medium"}]}}, {"id": "industrial.cutting.cutter.settings.requirements", "title": "industrial.cutting.cutter.settings.car_recycling.requirements.title", "subtitle": null, "type": "choice", "condition": {"type": "equals", "field": "industrial.cutting.cutter.settings.application", "value": "car_recycling"}, "meta": {"type": "radio", "choices": [{"title": "industrial.cutting.cutter.settings.car_recycling.requirements.low.title", "value": "low"}, {"title": "industrial.cutting.cutter.settings.car_recycling.requirements.medium.title", "value": "medium"}, {"title": "industrial.cutting.cutter.settings.car_recycling.requirements.high.title", "value": "high"}]}}, {"id": "industrial.cutting.cutter.settings.requirements", "title": "industrial.cutting.cutter.settings.fridge_recycling.requirements.title", "subtitle": null, "type": "choice", "condition": {"type": "equals", "field": "industrial.cutting.cutter.settings.application", "value": "fridge_recycling"}, "meta": {"type": "radio", "choices": [{"title": "industrial.cutting.cutter.settings.fridge_recycling.requirements.twist_grip.title", "value": "twist_grip"}, {"title": "industrial.cutting.cutter.settings.fridge_recycling.requirements.one_hand.title", "value": "one_hand"}, {"title": "industrial.cutting.cutter.settings.fridge_recycling.requirements.two_hands.title", "value": "two_hands"}]}}, {"id": "industrial.cutting.cutter.settings.frequency", "title": "industrial.cutting.cutter.settings.metal_recycling.frequency.title", "subtitle": null, "type": "choice", "condition": {"type": "equals", "field": "industrial.cutting.cutter.settings.application", "value": "metal_recycling"}, "meta": {"type": "radio", "choices": [{"title": "industrial.cutting.cutter.settings.metal_recycling.frequency.low.title", "value": "low"}, {"title": "industrial.cutting.cutter.settings.metal_recycling.frequency.medium.title", "value": "medium"}, {"title": "industrial.cutting.cutter.settings.metal_recycling.frequency.high.title", "value": "high"}]}}, {"id": "industrial.cutting.cutter.settings.metal_recycling.type_of_metal", "title": "industrial.cutting.cutter.settings.metal_recycling.type_of_metal.title", "subtitle": null, "type": "choice", "condition": {"type": "equals", "field": "industrial.cutting.cutter.settings.application", "value": "metal_recycling"}, "meta": {"type": "radio", "choices": [{"title": "industrial.cutting.cutter.settings.metal_recycling.type_of_metal.round_bar.title", "value": "round_bar"}, {"title": "industrial.cutting.cutter.settings.metal_recycling.type_of_metal.thin_pipe.title", "value": "thin_pipe"}, {"title": "industrial.cutting.cutter.settings.metal_recycling.type_of_metal.thick_pipe.title", "value": "thick_pipe"}, {"title": "industrial.cutting.cutter.settings.metal_recycling.type_of_metal.flat_bar.title", "value": "flat_bar"}]}}, {"id": "industrial.cutting.cutter.settings.metal_recycling.size", "title": "industrial.cutting.cutter.settings.metal_recycling.round_bar.size.title", "subtitle": "industrial.cutting.cutter.settings.metal_recycling.round_bar.size.subtitle", "type": "input", "condition": {"type": "and", "conditions": [{"type": "equals", "field": "industrial.cutting.cutter.settings.application", "value": "metal_recycling"}, {"type": "equals", "field": "industrial.cutting.cutter.settings.metal_recycling.type_of_metal", "value": "round_bar"}]}, "meta": {"type": "integer", "placeholder": "industrial.cutting.cutter.settings.metal_recycling.round_bar.size.placeholder", "label": "industrial.cutting.cutter.settings.metal_recycling.round_bar.size.label", "range": {"min": 1, "max": 24}, "unit": "mm"}}, {"id": "industrial.cutting.cutter.settings.metal_recycling.size", "title": "industrial.cutting.cutter.settings.metal_recycling.thin_pipe.size.title", "subtitle": "industrial.cutting.cutter.settings.metal_recycling.thin_pipe.size.subtitle", "type": "input", "condition": {"type": "and", "conditions": [{"type": "equals", "field": "industrial.cutting.cutter.settings.application", "value": "metal_recycling"}, {"type": "equals", "field": "industrial.cutting.cutter.settings.metal_recycling.type_of_metal", "value": "thin_pipe"}]}, "meta": {"type": "integer", "placeholder": "industrial.cutting.cutter.settings.metal_recycling.thin_pipe.size.placeholder", "label": "industrial.cutting.cutter.settings.metal_recycling.thin_pipe.size.label", "range": {"min": 1, "max": 100}, "unit": "mm"}}, {"id": "industrial.cutting.cutter.settings.metal_recycling.size", "title": "industrial.cutting.cutter.settings.metal_recycling.thick_pipe.size.title", "subtitle": "industrial.cutting.cutter.settings.metal_recycling.thick_pipe.size.subtitle", "type": "input", "condition": {"type": "and", "conditions": [{"type": "equals", "field": "industrial.cutting.cutter.settings.application", "value": "metal_recycling"}, {"type": "equals", "field": "industrial.cutting.cutter.settings.metal_recycling.type_of_metal", "value": "thick_pipe"}]}, "meta": {"type": "integer", "placeholder": "industrial.cutting.cutter.settings.metal_recycling.thick_pipe.size.placeholder", "label": "industrial.cutting.cutter.settings.metal_recycling.thick_pipe.size.label", "range": {"min": 1, "max": 60}, "unit": "mm"}}, {"id": "industrial.cutting.cutter.settings.metal_recycling.size", "title": "industrial.cutting.cutter.settings.metal_recycling.flat_bar.size.title", "subtitle": "industrial.cutting.cutter.settings.metal_recycling.flat_bar.size.subtitle", "type": "input", "condition": {"type": "and", "conditions": [{"type": "equals", "field": "industrial.cutting.cutter.settings.application", "value": "metal_recycling"}, {"type": "equals", "field": "industrial.cutting.cutter.settings.metal_recycling.type_of_metal", "value": "flat_bar"}]}, "meta": {"type": "integer", "placeholder": "industrial.cutting.cutter.settings.metal_recycling.flat_bar.size.placeholder", "label": "industrial.cutting.cutter.settings.metal_recycling.flat_bar.size.label", "range": {"min": 1, "max": 11}, "unit": "mm"}}]}