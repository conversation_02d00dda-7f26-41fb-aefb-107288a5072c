{"stage": "industrial.lifting.system_components", "type": "settings", "id": "industrial.lifting.system_components.flowpanel.settings", "title": "industrial.lifting.system_components.flowpanel.settings.title", "subtitle": "industrial.lifting.system_components.flowpanel.settings.subtitle", "icon": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/icons/flowpanel.svg", "content": [{"id": "industrial.lifting.system_components.flowpanel.settings.type", "title": null, "subtitle": null, "type": "choice", "condition": null, "meta": {"type": "button", "choices": [{"title": "industrial.lifting.system_components.flowpanel.settings.type.on_top.title", "img": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/configurator/lifting/FlowPanel-on-top.png", "value": "on_top"}, {"title": "industrial.lifting.system_components.flowpanel.settings.type.separate.title", "img": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/configurator/lifting/FlowPanel-separate.png", "value": "separate"}, {"title": "industrial.lifting.system_components.flowpanel.settings.type.existing.title", "img": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/icons/flowpanel.svg", "value": "existing"}]}}, {"id": "industrial.lifting.system_components.flowpanel.settings.separate.gaugeset", "title": "industrial.lifting.system_components.flowpanel.settings.separate.gaugeset.title", "subtitle": null, "type": "choice", "condition": {"type": "equals", "field": "industrial.lifting.system_components.flowpanel.settings.type", "value": "separate"}, "meta": {"type": "radio", "choices": [{"title": "industrial.lifting.system_components.flowpanel.settings.separate.gaugeset.yes.title", "value": "yes"}, {"title": "industrial.lifting.system_components.flowpanel.settings.separate.gaugeset.no.title", "value": "no"}]}}, {"id": "industrial.lifting.system_components.flowpanel.settings.separate.frame", "title": "industrial.lifting.system_components.flowpanel.settings.separate.frame.title", "subtitle": null, "type": "choice", "condition": {"type": "equals", "field": "industrial.lifting.system_components.flowpanel.settings.type", "value": "separate"}, "meta": {"type": "radio", "choices": [{"title": "industrial.lifting.system_components.flowpanel.settings.separate.frame.yes.title", "value": "yes"}, {"title": "industrial.lifting.system_components.flowpanel.settings.separate.frame.no.title", "value": "no"}]}}, {"id": "industrial.lifting.system_components.flowpanel.settings.separate.cover", "title": "industrial.lifting.system_components.flowpanel.settings.separate.cover.title", "subtitle": null, "type": "choice", "condition": {"type": "and", "conditions": [{"type": "equals", "field": "industrial.lifting.system_components.flowpanel.settings.type", "value": "separate"}, {"type": "equals", "field": "industrial.lifting.system_components.flowpanel.settings.separate.frame", "value": "yes"}]}, "meta": {"type": "radio", "choices": [{"title": "industrial.lifting.system_components.flowpanel.settings.separate.cover.yes.title", "value": "yes"}, {"title": "industrial.lifting.system_components.flowpanel.settings.separate.cover.no.title", "value": "no"}]}}]}