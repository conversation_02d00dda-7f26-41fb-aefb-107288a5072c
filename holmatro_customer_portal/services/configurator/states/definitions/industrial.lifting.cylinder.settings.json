{"stage": "industrial.lifting.cylinder", "type": "settings", "id": "industrial.lifting.cylinder.settings", "title": "industrial.lifting.cylinder.settings.title", "subtitle": "industrial.lifting.cylinder.settings.subtitle", "icon": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/icons/cylinder.svg", "content": [{"id": "industrial.lifting.cylinder.settings.main", "title": "industrial.lifting.cylinder.settings.main.title", "subtitle": null, "type": "choice", "condition": null, "meta": {"type": "button", "choices": [{"title": "industrial.lifting.cylinder.settings.main.application.title", "img": null, "value": "application"}, {"title": "industrial.lifting.cylinder.settings.main.calculation.title", "img": null, "value": "calculation"}, {"title": "industrial.lifting.cylinder.settings.main.existing.title", "img": null, "value": "existing"}]}}, {"id": "industrial.lifting.cylinder.settings.application", "title": "industrial.lifting.cylinder.settings.application.title", "subtitle": null, "type": "choice", "condition": {"type": "equals", "field": "industrial.lifting.cylinder.settings.main", "value": "application"}, "meta": {"type": "button", "choices": [{"title": "industrial.lifting.cylinder.settings.application.lifting.title", "img": null, "value": "lifting"}, {"title": "industrial.lifting.cylinder.settings.application.pulling.title", "img": null, "value": "pulling"}]}}, {"id": "industrial.lifting.cylinder.settings.application.lifting", "title": null, "subtitle": null, "type": "choice", "condition": {"type": "and", "conditions": [{"type": "equals", "field": "industrial.lifting.cylinder.settings.main", "value": "application"}, {"type": "equals", "field": "industrial.lifting.cylinder.settings.application", "value": "lifting"}]}, "meta": {"type": "button", "choices": [{"title": "industrial.lifting.cylinder.settings.application.lifting.cylinder.title", "img": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/configurator/lifting/Cylinder.png", "value": "cylinder"}, {"title": "industrial.lifting.cylinder.settings.application.lifting.wedges.title", "img": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/configurator/lifting/Wedge.png", "value": "wedges"}, {"title": "industrial.lifting.cylinder.settings.application.lifting.toe_jacks.title", "img": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/configurator/lifting/Toe+jack.png", "value": "toe_jacks"}]}}, {"id": "industrial.lifting.cylinder.settings.application.pulling", "title": null, "subtitle": null, "type": "choice", "condition": {"type": "and", "conditions": [{"type": "equals", "field": "industrial.lifting.cylinder.settings.main", "value": "application"}, {"type": "equals", "field": "industrial.lifting.cylinder.settings.application", "value": "pulling"}]}, "meta": {"type": "button", "choices": [{"title": "industrial.lifting.cylinder.settings.application.pulling.pulling_jack.title", "img": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/configurator/lifting/Pulling+Jack.png", "value": "pulling"}, {"title": "industrial.lifting.cylinder.settings.application.pulling.hollow_cylinder.title", "img": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/configurator/lifting/Hollow.png", "value": "hollow plunger"}]}}, {"id": "industrial.lifting.cylinder.settings.calculation.weight", "title": "industrial.lifting.cylinder.settings.calculation.weight.title", "subtitle": "industrial.lifting.cylinder.settings.calculation.weight.subtitle", "type": "input", "condition": {"type": "equals", "field": "industrial.lifting.cylinder.settings.main", "value": "calculation"}, "meta": {"type": "integer", "placeholder": "industrial.lifting.cylinder.settings.calculation.weight.placeholder", "label": "industrial.lifting.cylinder.settings.calculation.weight.label", "range": {"min": 1, "max": 1000}, "unit": "tonne"}}, {"id": "industrial.lifting.cylinder.settings.calculation.symmetry", "title": "industrial.lifting.cylinder.settings.calculation.symmetry.title", "subtitle": null, "type": "choice", "condition": {"type": "equals", "field": "industrial.lifting.cylinder.settings.main", "value": "calculation"}, "meta": {"type": "button", "choices": [{"title": "industrial.lifting.cylinder.settings.calculation.symmetry.symmetrical.title", "img": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/configurator/lifting/Symmetric.png", "value": "symmetrical"}, {"title": "industrial.lifting.cylinder.settings.calculation.symmetry.asymmetrical.title", "img": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/configurator/lifting/A-Symmetric.png", "value": "asymmetrical"}]}}, {"id": "industrial.lifting.cylinder.settings.calculation.liftingpoints", "title": null, "subtitle": null, "type": "input", "condition": {"type": "equals", "field": "industrial.lifting.cylinder.settings.main", "value": "calculation"}, "meta": {"type": "integer", "placeholder": "industrial.lifting.cylinder.settings.calculation.liftingpoints.placeholder", "label": "industrial.lifting.cylinder.settings.calculation.liftingpoints.label", "range": {"min": 1, "max": null}}}, {"id": "industrial.lifting.cylinder.settings.existing", "title": null, "subtitle": "industrial.lifting.cylinder.settings.existing.subtitle", "type": null, "condition": {"type": "equals", "field": "industrial.lifting.cylinder.settings.main", "value": "existing"}, "meta": {}}]}