{"stage": "industrial.lifting.pump", "type": "settings", "id": "industrial.lifting.pump.settings", "title": "industrial.lifting.pump.settings.title", "subtitle": "industrial.lifting.pump.settings.subtitle", "icon": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/icons/pump.svg", "content": [{"id": "industrial.lifting.pump.settings.type", "title": null, "subtitle": null, "type": "choice", "condition": null, "meta": {"type": "button", "choices": [{"title": "industrial.lifting.pump.settings.type.hand.title", "img": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/configurator/lifting/HandPump.png", "value": "hand"}, {"title": "industrial.lifting.pump.settings.type.compact.title", "img": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/configurator/lifting/CompactPumps.png", "value": "compact"}, {"title": "industrial.lifting.pump.settings.type.vari.title", "img": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/configurator/lifting/Vari+Pump.png", "value": "vari"}, {"title": "industrial.lifting.pump.settings.type.existing.title", "img": "https://harborn-acc-holmatro-product-import-bucket.s3.eu-west-1.amazonaws.com/assets/portal/icons/pump.svg", "value": "existing"}]}}, {"id": "industrial.lifting.pump.settings.hand.valve", "title": "industrial.lifting.pump.settings.hand.valve.title", "subtitle": "industrial.lifting.pump.settings.hand.valve.subtitle", "type": "choice", "condition": {"type": "equals", "field": "industrial.lifting.pump.settings.type", "value": "hand"}, "meta": {"type": "radio", "choices": [{"title": "industrial.lifting.pump.settings.hand.valve.yes.title", "value": "yes"}, {"title": "industrial.lifting.pump.settings.hand.valve.no.title", "value": "no"}], "initial": "yes"}}, {"id": "industrial.lifting.pump.settings.hand.gauge", "title": "industrial.lifting.pump.settings.hand.gauge.title", "subtitle": "industrial.lifting.pump.settings.hand.gauge.subtitle", "type": "choice", "condition": {"type": "equals", "field": "industrial.lifting.pump.settings.type", "value": "hand"}, "meta": {"type": "radio", "choices": [{"title": "industrial.lifting.pump.settings.hand.gauge.yes.title", "value": "yes"}, {"title": "industrial.lifting.pump.settings.hand.gauge.no.title", "value": "no"}], "initial": "yes"}}, {"id": "industrial.lifting.pump.settings.compact.power", "title": null, "subtitle": null, "type": "choice", "condition": {"type": "equals", "field": "industrial.lifting.pump.settings.type", "value": "compact"}, "meta": {"type": "radio", "choices": [{"title": "industrial.lifting.pump.settings.compact.power.electric.title", "img": null, "value": "electric"}, {"title": "industrial.lifting.pump.settings.compact.power.air.title", "img": null, "value": "air"}]}}, {"id": "industrial.lifting.pump.settings.compact.electric.gauge", "title": "industrial.lifting.pump.settings.compact.electric.gauge.title", "subtitle": "industrial.lifting.pump.settings.compact.electric.gauge.subtitle", "type": "choice", "condition": {"type": "and", "conditions": [{"type": "equals", "field": "industrial.lifting.pump.settings.type", "value": "compact"}, {"type": "equals", "field": "industrial.lifting.pump.settings.compact.power", "value": "electric"}]}, "meta": {"type": "radio", "choices": [{"title": "industrial.lifting.pump.settings.compact.electric.gauge.gaugeonly.title", "value": "<PERSON><PERSON>ly"}, {"title": "industrial.lifting.pump.settings.compact.electric.gauge.gaugeandneedle.title", "value": "gaugeandneedle"}, {"title": "industrial.lifting.pump.settings.compact.electric.gauge.nogauge.title", "value": "nogauge"}], "initial": "gaugeandneedle"}}, {"id": "industrial.lifting.pump.settings.compact.air.gauge", "title": "industrial.lifting.pump.settings.compact.air.gauge.title", "subtitle": "industrial.lifting.pump.settings.compact.air.gauge.subtitle", "type": "choice", "condition": {"type": "and", "conditions": [{"type": "equals", "field": "industrial.lifting.pump.settings.type", "value": "compact"}, {"type": "equals", "field": "industrial.lifting.pump.settings.compact.power", "value": "air"}]}, "meta": {"type": "radio", "choices": [{"title": "industrial.lifting.pump.settings.compact.air.gauge.yes.title", "value": "yes"}, {"title": "industrial.lifting.pump.settings.compact.air.gauge.no.title", "value": "no"}], "initial": "yes"}}]}