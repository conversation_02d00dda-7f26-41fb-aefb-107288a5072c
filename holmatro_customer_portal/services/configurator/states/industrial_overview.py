from __future__ import annotations

from datetime import datetime
from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.mappers.product_preview_mapper import ProductPreviewMapper
from holmatro_customer_portal.schemas.configurator import OverviewDetails, OverviewPrice
from holmatro_customer_portal.services.configurator.states.base import (
    BaseState,
    ConfiguratorOverviewDataIDs,
    ConfiguratorState,
)
from holmatro_customer_portal.services.utils.product_queries import get_configurator_products
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class Overview(BaseState):
    state = ConfiguratorState.INDUSTRIAL_OVERVIEW

    def on_enter(self: BaseState | Configurator, event: EventData) -> None:
        """
        1. Update the db with the current state and timestamp for finished_at
        """
        if not self._configuration.finished_at:
            self._configuration.finished_at = datetime.now()
            self._configuration.last_updated_state = self.state.value
            self._session.merge(self._configuration)
            self._session.commit()

        indexed_product_ids = [product.product_id for product in self._configuration.products]
        category_id = self._configuration.category.category_id if self._configuration.category else None

        if indexed_product_ids:
            products = get_configurator_products(indexed_product_ids, self._configuration, self._session)
            sorted_products = sorted(products, key=lambda x: indexed_product_ids.index(x.product_id))
            mapped_products = ProductPreviewMapper(
                self._session, self._my_hm_client
            ).configurator_product_preview_mapper(self._configuration.user, sorted_products)
            if mapped_products:
                for c in self.state_definition["content"]:
                    if c["id"] == ConfiguratorOverviewDataIDs.OVERVIEW_PRODUCTLIST.value:
                        c["meta"]["products"] = mapped_products

            configuration_total_price_net = None
            configuration_total_price_gross = None

            # Check if there is any product without price
            product_without_price = [
                product for product in mapped_products if not (product.total_price_net and product.total_price_gross)
            ]
            if not product_without_price:
                configuration_total_price_net = sum(
                    product.total_price_net
                    for product in mapped_products
                    if product.total_price_net and product.total_price_gross
                )
                configuration_total_price_gross = sum(
                    product.total_price_gross
                    for product in mapped_products
                    if product.total_price_net and product.total_price_gross
                )

            for c in self.state_definition["content"]:
                if c["id"] == ConfiguratorOverviewDataIDs.OVERVIEW_DETAILS.value:
                    c["meta"]["overview"] = OverviewDetails(
                        configuration_id=self._configuration.id,
                        category_id=category_id,
                        started_at=self._configuration.started_at,
                        quotation_number=self._configuration.quotation_number,
                        reference=self._configuration.reference,
                        progress=100,
                        tags=[],
                        price_net=OverviewPrice(configuration=configuration_total_price_net),
                        price_gross=OverviewPrice(configuration=configuration_total_price_gross),
                    )
        else:
            for c in self.state_definition["content"]:
                if c["id"] == ConfiguratorOverviewDataIDs.OVERVIEW_DETAILS.value:
                    c["meta"]["overview"] = OverviewDetails(
                        configuration_id=self._configuration.id,
                        category_id=category_id,
                        started_at=self._configuration.started_at,
                        quotation_number=self._configuration.quotation_number,
                        reference=self._configuration.reference,
                        progress=100,
                        tags=[],
                        price_net=OverviewPrice(),
                        price_gross=OverviewPrice(),
                    )
