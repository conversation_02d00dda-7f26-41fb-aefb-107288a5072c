from __future__ import annotations

from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.services.configurator.configurator_enums import (
    ConfiguratorSelectionChoices,
    CutterApplication,
    CutterFilterValues,
    CutterRequirements,
    CutterType,
    CuttingFrequencyFilterValues,
    FilterName,
)
from holmatro_customer_portal.services.configurator.queries import get_configuration_data_value
from holmatro_customer_portal.services.configurator.states.base import (
    BaseState,
    ConfiguratorCutterStageDataIDs,
    ConfiguratorState,
)
from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.tweakwise.tweakwise_client import TweakwiseClient

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class CutterSettingsMain(BaseState):
    state = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_SETTINGS


class CutterSettingsType(BaseState):
    state = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_WAY_OF_CUTTING

    def on_enter(self: BaseState | Configurator, event: EventData) -> None:
        for c in self.state_definition["content"]:
            if c["type"] == "choice":
                choice_block = c
                choices = {choice["value"]: choice for choice in choice_block["meta"]["choices"]}
            else:
                none_block = c

        application = get_configuration_data_value(
            self._session, self._configuration, ConfiguratorCutterStageDataIDs.CUTTER_SETTINGS_APPLICATION.value
        )
        frequency = get_configuration_data_value(
            self._session, self._configuration, ConfiguratorCutterStageDataIDs.CUTTER_SETTINGS_FREQUENCY.value
        )

        match application:
            case CutterApplication.CABLE_CUTTING.value:
                requirements = get_configuration_data_value(
                    self._session,
                    self._configuration,
                    ConfiguratorCutterStageDataIDs.CUTTER_SETTINGS_REQUIREMENTS.value,
                )

                if frequency == ConfiguratorSelectionChoices.MEDIUM.value:
                    del choices[CutterType.BATTERY.value]
                elif frequency == ConfiguratorSelectionChoices.HIGH.value:
                    del choices[CutterType.MOBILE.value]
                    del choices[CutterType.BATTERY.value]
                if requirements == "large":
                    del choices[CutterType.STATIONARY.value]
                if len(choices) >= 1:
                    choice_block["meta"]["choices"] = list(choices.values())
                    self.state_definition["content"] = [choice_block]
                else:
                    self.state_definition["content"] = [none_block]
            case CutterApplication.CAR_RECYCLING.value:
                del choices[CutterType.STATIONARY.value]
                if frequency == ConfiguratorSelectionChoices.MEDIUM.value:
                    del choices[CutterType.BATTERY.value]
                choice_block["meta"]["choices"] = list(choices.values())
                self.state_definition["content"] = [choice_block]
            case CutterApplication.METAL_RECYCLING.value:
                if frequency == ConfiguratorSelectionChoices.MEDIUM.value:
                    del choices[CutterType.BATTERY.value]
                elif frequency == ConfiguratorSelectionChoices.HIGH.value:
                    del choices[CutterType.MOBILE.value]
                    del choices[CutterType.BATTERY.value]
                choice_block["meta"]["choices"] = list(choices.values())
                self.state_definition["content"] = [choice_block]


class CutterProductList(BaseState):
    state = ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST

    def on_enter(self: BaseState | Configurator, event: EventData) -> bool:
        _logger.debug(f"Fetching on enter information for {self.state}")
        filters = {}

        category_path: str | None = self._catalog_repository.get_category_path(
            self._configuration.user.language.code, SyncforceCategory.CUTTERS, SyncforceCategory.INDUSTRIAL_CUTTING
        )
        if category_path:
            for c in self.state_definition["content"]:
                if c["type"] == "filters":
                    c["meta"]["category"] = category_path

        def get_data(attribute_id: str) -> str:
            return get_configuration_data_value(
                self._session,
                self._configuration,
                attribute_id,
            )

        application = get_data(ConfiguratorCutterStageDataIDs.CUTTER_SETTINGS_APPLICATION.value)
        if application:
            filters.update(
                {FilterName.CUTTER_APPLICATION.value: CutterFilterValues[CutterApplication(application).name].value}
            )

            # Retrieve control type (only for fridge recycling)
            if application == CutterApplication.FRIDGE_RECYCLING.value:
                # Add value input by user to the filters
                control_type = get_data(ConfiguratorCutterStageDataIDs.CUTTER_SETTINGS_REQUIREMENTS.value)
                if control_type:
                    filter_name, filter_value, _ = update_user_requirements_filter(application, control_type)
                    filters.update({filter_name: filter_value})

            else:
                # Retrieve value for filters that are not used for fridge recycling
                cutting_frequency = get_data(ConfiguratorCutterStageDataIDs.CUTTER_SETTINGS_FREQUENCY.value)
                way_of_cutting = get_data(ConfiguratorCutterStageDataIDs.CUTTER_WAY_OF_CUTTING.value)

                # Add one of the 4 filters defined for metal recycling
                if application == CutterApplication.METAL_RECYCLING.value:
                    type_of_metal = get_data(
                        ConfiguratorCutterStageDataIDs.CUTTER_SETTINGS_METAL_RECYCLING_REQUIREMENTS.value
                    )
                    metal_thickness = get_data(
                        ConfiguratorCutterStageDataIDs.CUTTER_SETTINGS_METAL_RECYCLING_THICKNESS.value
                    )
                    if type_of_metal and metal_thickness:
                        # The filter has a type with min and max, e.g. "1-10"
                        # It retrieves the value input by the user (min) and the max value possible
                        filter_name, _, max_value = update_user_requirements_filter(type_of_metal, metal_thickness)
                        filters.update({filter_name: f"{metal_thickness}-{max_value}"})

                # Retrieve max diameter cable value input by user (only for cable_recycling)
                else:
                    user_requirements = get_data(ConfiguratorCutterStageDataIDs.CUTTER_SETTINGS_REQUIREMENTS.value)
                    if user_requirements:
                        # The filter has a type with min and max, e.g. "1-10"
                        # It retrieves the value input by the user (min) and the max value possible
                        filter_name, user_value, max_value = update_user_requirements_filter(
                            application, user_requirements
                        )
                        filters.update({filter_name: f"{user_value}-{max_value}"})

                if cutting_frequency:
                    # The filter has a type with min and max, e.g. "1-10"
                    # It retrieves the value input by the user (min) and the max value possible
                    filters.update(
                        {
                            FilterName.CUTTING_FREQUENCY.value: f"{CuttingFrequencyFilterValues[CutterRequirements(cutting_frequency).name].value}-{CuttingFrequencyFilterValues.HIGH.value}",
                        }
                    )
                if way_of_cutting:
                    filters.update(
                        {FilterName.CUTTER_DETAILS.value: CutterFilterValues[CutterType(way_of_cutting).name].value}
                    )

        for c in self.state_definition["content"]:
            if c["type"] == "filters":
                c["meta"]["filters"] = filters


def update_user_requirements_filter(application: str, user_requirements: str) -> tuple | None:
    # Maps the Tweakwise filter to the application and its values
    user_requirements_values_mapping = {
        CutterApplication.CABLE_CUTTING.value: {  # Refers to the max diameter of a cable
            "filter_name": FilterName.CABLE_CUTTING_POWER_CABLE_DIAMETER.value,
            "max_value": 160,
            CutterRequirements.SMALL.value: 70,
            CutterRequirements.MEDIUM.value: 110,
            CutterRequirements.LARGE.value: 160,
        },
        CutterApplication.CAR_RECYCLING.value: {  # Refers to the catalytic converter
            "filter_name": FilterName.CAR_RECYCLING_CATALYTIC_CONVERTER.value,
            "max_value": 99,
            CutterRequirements.LOW.value: 80,
            CutterRequirements.MEDIUM.value: 95,
            CutterRequirements.HIGH.value: 99,
        },
        CutterApplication.FRIDGE_RECYCLING.value: {
            "filter_name": FilterName.CUTTER_DETAILS.value,
            CutterRequirements.FRIDGE_RECYCLING_TWIST_GRIP.value: CutterFilterValues.MOBILE.value,
            CutterRequirements.FRIDGE_RECYCLING_STATIONARY_ONE_HAND.value: CutterFilterValues.STATIONARY.value,
            CutterRequirements.FRIDGE_RECYCLING_STATIONARY_TWO_HANDS.value: CutterFilterValues.STATIONARY_TWO_HANDS.value,
        },
        # Refers to the type and thickness of metal
        CutterRequirements.METAL_RECYCLING_ROUND_BAR.value: {
            "filter_name": FilterName.METAL_RECYCLING_ROUND_BAR_DIAMETER.value,
            "max_value": 24,
        },
        CutterRequirements.METAL_RECYCLING_THIN_PIPE.value: {
            "filter_name": FilterName.METAL_RECYCLING_THIN_PIPE_DIAMETER.value,
            "max_value": 100,
        },
        CutterRequirements.METAL_RECYCLING_THICK_PIPE.value: {
            "filter_name": FilterName.METAL_RECYCLING_THICK_PIPE_DIAMETER.value,
            "max_value": 70,
        },
        CutterRequirements.METAL_RECYCLING_FLAT_BAR.value: {
            "filter_name": FilterName.METAL_RECYCLING_FLAT_BAR_THICKNESS.value,
            "max_value": 100,
        },
    }

    mapping = user_requirements_values_mapping.get(application)
    if not mapping:
        return

    filter_name: str = mapping.get("filter_name")
    filter_value: int = mapping.get("max_value")
    user_value: int | str = mapping.get(user_requirements)
    if not filter_name:
        return

    return filter_name, user_value, filter_value
