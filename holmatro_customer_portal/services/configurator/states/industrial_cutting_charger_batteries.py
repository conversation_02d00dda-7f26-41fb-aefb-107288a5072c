from __future__ import annotations

from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.mappers.product_preview_mapper import ProductPreviewMapper
from holmatro_customer_portal.schemas.response_schema import ProductPreviewRes
from holmatro_customer_portal.services.configurator.default_products import Batteries, Chargers
from holmatro_customer_portal.services.configurator.queries import get_product_quantities
from holmatro_customer_portal.services.configurator.states.base import BaseState, ConfiguratorState
from holmatro_customer_portal.services.utils.product_queries import get_product_preview_by_article_number
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class ChargerBatteriesProductList(BaseState):
    state = ConfiguratorState.INDUSTRIAL_CUTTING_CHARGER_BATTERIES_PRODUCTLIST

    def on_enter(self: BaseState | Configurator, event: EventData) -> None:
        product_article_numbers = [
            Batteries.BATTERY_28V_6AH.value,
            Chargers.CHARGER_230V.value,
            Chargers.CHARGER_110V.value,
        ]

        def get_products(product_article_number: list[str] | str) -> list[ProductPreviewRes]:
            products = get_product_preview_by_article_number(
                product_article_number, self._configuration.user, self._session
            )
            return ProductPreviewMapper(self._session, self._my_hm_client).product_preview_mapper(
                self._configuration.user, products
            )

        quantity_cutter = get_product_quantities(
            self._session, self._configuration, ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST
        )
        for c in self.state_definition["content"]:
            if c["type"] == "products":
                c["meta"]["products"] = get_products(product_article_numbers)
            if quantity_cutter:
                c["meta"]["initial"] = quantity_cutter.get("total_quantity") or 1

        # Pre-select the battery
        self.set_default_product(Batteries.BATTERY_28V_6AH.value)
