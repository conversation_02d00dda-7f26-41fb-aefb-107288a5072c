from holmatro_customer_portal.database.models import Configuration, Product
from holmatro_customer_portal.mappers.configurator_mapper import ConfiguratorMapper
from holmatro_customer_portal.mappers.product_preview_mapper import ProductPreviewMapper
from holmatro_customer_portal.schemas.configurator import ConfiguratorProduct, ConfiguratorStateProducts
from holmatro_customer_portal.schemas.response_schema import ProductPreviewRes
from holmatro_customer_portal.services.configurator.configurator_enums import (
    ConfiguratorSelectionChoices,
    CylinderActingType,
    PumpType,
    SyncForceAttributeId,
)
from holmatro_customer_portal.services.configurator.default_products import AdditionalProducts, GaugeSet
from holmatro_customer_portal.services.configurator.queries import (
    get_configuration_product_attribute,
    get_product_quantities,
)
from holmatro_customer_portal.services.configurator.states import ConfiguratorPumpStageDataIDs, ConfiguratorState
from holmatro_customer_portal.services.configurator.utils import (
    get_configuration_pump_type,
    get_or_query_cylinder_acting_type,
    get_or_query_products,
    get_or_query_value,
)
from holmatro_customer_portal.services.utils.product_queries import get_product_preview_by_article_number
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


def get_product_gaugeset(
    db: Session, configuration: Configuration, my_hm_client: MyHolmatroPortalApiClient, event
) -> list[ProductPreviewRes]:
    """
    1: determine type of pump and cylinder acting type to define correct gaugeset
    2: for hand pump single acting it also checks the pump capacity oil tank effective
    3: for compact pump it checks option selected by user if with needle or not
    """

    pump_type = get_configuration_pump_type(db, configuration)
    cylinder_acting_type = get_or_query_cylinder_acting_type(event, db, configuration)

    def get_products(product_article_number: list[str] | str) -> list[ProductPreviewRes]:
        products = get_product_preview_by_article_number(product_article_number, configuration.user, db)
        return ProductPreviewMapper(db, my_hm_client).product_preview_mapper(configuration.user, products)

    match pump_type:
        case PumpType.HAND.value:
            match cylinder_acting_type:
                case CylinderActingType.SINGLE.value:
                    capacity_oil_tank = get_configuration_product_attribute(
                        db,
                        configuration,
                        state=ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST,
                        product_attribute_id=SyncForceAttributeId.PUMP_CAPACITY_OIL_TANK,
                    )
                    try:
                        if int(capacity_oil_tank) < 1500:
                            return get_products(GaugeSet.GAUGESET_HAND_PUMP_SINGLE_ACTING_OIL_LESS_1700.value)
                        elif int(capacity_oil_tank) > 1500:
                            return get_products(GaugeSet.GAUGESET_HAND_PUMP_SINGLE_ACTING_OIL_MORE_1700.value)
                        else:
                            return get_products(
                                [
                                    GaugeSet.GAUGESET_HAND_PUMP_SINGLE_ACTING_OIL_LESS_1700.value,
                                    GaugeSet.GAUGESET_HAND_PUMP_SINGLE_ACTING_OIL_MORE_1700.value,
                                ]
                            )
                    except (TypeError, ValueError):
                        _logger.debug(f"Capacity oil tank not found in pump from configuration id {configuration.id}")
                        return get_products(
                            [
                                GaugeSet.GAUGESET_HAND_PUMP_SINGLE_ACTING_OIL_LESS_1700.value,
                                GaugeSet.GAUGESET_HAND_PUMP_SINGLE_ACTING_OIL_MORE_1700.value,
                            ]
                        )

                case CylinderActingType.DOUBLE.value:
                    return get_products(GaugeSet.GAUGESET_HAND_PUMP_DOUBLE_ACTING.value)
                case _:
                    gauges = [
                        GaugeSet.GAUGESET_HAND_PUMP_SINGLE_ACTING_OIL_LESS_1700.value,
                        GaugeSet.GAUGESET_HAND_PUMP_SINGLE_ACTING_OIL_MORE_1700.value,
                        GaugeSet.GAUGESET_HAND_PUMP_DOUBLE_ACTING.value,
                    ]
                    return get_products(gauges)

        case PumpType.COMPACT.value:
            compact_pump_type = get_or_query_value(
                event,
                ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_COMPACT_POWER.value,
                db,
                configuration,
            )
            match compact_pump_type:
                case PumpType.COMPACT_ELECTRIC.value:
                    electric_gauge = get_or_query_value(
                        event,
                        ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_COMPACT_ELECTRIC_GAUGE_SELECTION.value,
                        db,
                        configuration,
                    )
                    match electric_gauge:
                        case ConfiguratorSelectionChoices.GAUGE_AND_NEEDLE.value:
                            return get_products(GaugeSet.GAUGESET_COMPACT_PUMP_ELECTRIC_GAUGE_AND_NEEDLE.value)
                        case ConfiguratorSelectionChoices.GAUGE_ONLY.value:
                            return get_products(GaugeSet.GAUGESET_COMPACT_PUMP_ELECTRIC_GAUGE.value)
                        case _:
                            return get_products(
                                [
                                    GaugeSet.GAUGESET_COMPACT_PUMP_ELECTRIC_GAUGE.value,
                                    GaugeSet.GAUGESET_COMPACT_PUMP_ELECTRIC_GAUGE_AND_NEEDLE.value,
                                ]
                            )

                case PumpType.COMPACT_AIR.value:
                    return get_products(GaugeSet.GAUGESET_COMPACT_PUMP_AIR.value)

                case _:
                    return get_products(
                        [
                            GaugeSet.GAUGESET_COMPACT_PUMP_ELECTRIC_GAUGE.value,
                            GaugeSet.GAUGESET_COMPACT_PUMP_ELECTRIC_GAUGE_AND_NEEDLE.value,
                            GaugeSet.GAUGESET_COMPACT_PUMP_AIR.value,
                        ]
                    )

        case PumpType.VARI.value:
            return get_products(GaugeSet.GAUGESET_VARI_PUMP.value)

        case _:
            gaugeset = [gaugeset.value for gaugeset in GaugeSet]
            return get_products(gaugeset)


def add_additional_products_to_gauge(event, db: Session, configuration: Configuration, state: ConfiguratorState):
    mapper = ConfiguratorMapper(db)
    pump_type = get_configuration_pump_type(db, configuration)
    if pump_type == PumpType.VARI.value:
        gauge: list[Product] = get_or_query_products(event, state, db, configuration)
        if gauge:
            gauge_quantity: dict | None = get_product_quantities(db, configuration, state)
            gauge_total_quantity: int | None = gauge_quantity.get("total_quantity") if gauge_quantity else None

            cylinder_acting_type = get_or_query_cylinder_acting_type(event, db, configuration)
            gauge_article_number = AdditionalProducts.GAUGESET_VARI_PUMP_DOUBLE_ACTING.value
            if cylinder_acting_type == CylinderActingType.SINGLE.value:
                gauge_article_number = AdditionalProducts.GAUGESET_VARI_PUMP_SINGLE_ACTING.value

            cover = mapper.store_configuration_product(
                configuration,
                ConfiguratorStateProducts(
                    state_id=ConfiguratorState.INDUSTRIAL_LIFTING_GAUGE_ADDITIONAL_PRODUCT_COVER.value,
                    products=[
                        ConfiguratorProduct(
                            article_number=AdditionalProducts.GAUGESET_VARI_PUMP_COVER.value,
                            quantity=(gauge_total_quantity or 1),
                            product_id=None,
                        )
                    ],
                ),
            )
            additional_gauge = mapper.store_configuration_product(
                configuration,
                ConfiguratorStateProducts(
                    state_id=ConfiguratorState.INDUSTRIAL_LIFTING_GAUGE_ADDITIONAL_PRODUCT_GAUGE.value,
                    products=[
                        ConfiguratorProduct(
                            article_number=gauge_article_number,
                            quantity=(gauge_total_quantity or 1),
                            product_id=None,
                        )
                    ],
                ),
            )

            db.add_all(cover + additional_gauge)
            db.commit()
