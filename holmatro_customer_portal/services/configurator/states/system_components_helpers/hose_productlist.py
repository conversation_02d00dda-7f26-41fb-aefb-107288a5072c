from transitions import EventData

from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.domain.repositories.catalog_repository import CatalogRepository
from holmatro_customer_portal.services.configurator.configurator_enums import (
    CylinderActingType,
    FilterName,
    HoseTypeFilter,
    HoseTypeSelection,
    PumpType,
)
from holmatro_customer_portal.services.configurator.default_products import Hoses
from holmatro_customer_portal.services.configurator.queries import get_product_quantities
from holmatro_customer_portal.services.configurator.states import ConfiguratorHoseStageDataIDs, ConfiguratorState
from holmatro_customer_portal.services.configurator.utils import (
    get_or_query_cylinder_acting_type,
    get_or_query_pump_type,
    get_or_query_value,
)
from holmatro_customer_portal.services.configurator.workflow import Configurator
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.enums import SyncforceCategory


def set_hose_state_content(
    configurator: Configurator,
    event: EventData,
    db: Session,
    configuration: Configuration,
    catalog_repository: CatalogRepository,
):
    """
    1: determine the quantity and type of pump to define the minimum amount of hoses
    2: determine type of hose/connection to define correct hose
    3: if first hose for vari pump was already selected, it returns options for the second hose
    4: set default hose according to type selected
    """
    pump_type = get_or_query_pump_type(event, db, configuration)
    pump_quantity: dict | None = get_product_quantities(
        db, configuration, ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST
    )
    pump_total_quantity: int | None = pump_quantity.get("total_quantity") if pump_quantity else None
    cylinder_acting_type = get_or_query_cylinder_acting_type(event, db, configuration)

    for c in configurator.state_definition["content"]:
        if c["type"] == "products" and isinstance(pump_total_quantity, int):
            if cylinder_acting_type == CylinderActingType.DOUBLE.value:
                c["meta"]["initial"] = pump_total_quantity * 2
            else:
                c["meta"]["initial"] = pump_total_quantity

    category_path: str | None = catalog_repository.get_category_path(
        configuration.user.language.code, SyncforceCategory.LIFTING_HOSES, SyncforceCategory.INDUSTRIAL_LIFTING
    )

    hose_default_product = Hoses.EXTENSION_HOSE_2M.value
    if pump_type == PumpType.VARI.value or not pump_type:
        hose_type_filter = f"{HoseTypeFilter.EXTENSION.value}"

    else:
        hose_type = get_or_query_value(event, ConfiguratorHoseStageDataIDs.HOSE_SETTINGS_TYPE.value, db, configuration)

        match hose_type:
            case HoseTypeSelection.MOUNTED.value:
                hose_type_filter = f"{HoseTypeFilter.STANDARD.value}"
                hose_default_product = Hoses.STANDARD_HOSE_2M.value

            case HoseTypeSelection.EXTENSION.value:
                hose_type_filter = f"{HoseTypeFilter.EXTENSION.value}"

            case _:
                hose_type_filter = f"{HoseTypeFilter.STANDARD.value}|{HoseTypeFilter.EXTENSION.value}"

    for c in configurator.state_definition["content"]:
        if c["type"] == "filters":
            c["meta"]["filters"] = {
                f"{configuration.user.language.code}-{FilterName.HOSE_TYPE.value}": hose_type_filter
            }
            c["meta"]["category"] = category_path

    configurator.set_default_product(hose_default_product)
