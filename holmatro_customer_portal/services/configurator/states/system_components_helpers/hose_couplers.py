from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.mappers.configurator_mapper import ConfiguratorMapper
from holmatro_customer_portal.schemas.configurator import ConfiguratorProduct, ConfiguratorStateProducts
from holmatro_customer_portal.services.configurator.configurator_enums import HoseTypeSelection, PumpType
from holmatro_customer_portal.services.configurator.default_products import AdditionalProducts
from holmatro_customer_portal.services.configurator.queries import get_product_quantities
from holmatro_customer_portal.services.configurator.states import ConfiguratorHoseStageDataIDs, ConfiguratorState
from holmatro_customer_portal.services.configurator.utils import (
    get_configuration_pump_type,
    get_or_query_products,
    get_or_query_value,
)
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


def add_couplers_to_configuration(event, db: Session, configuration: Configuration, state: ConfiguratorState) -> None:
    """
    1. Determine the quantity and type of couplers for hose depending on the quantity, type of cylinder and type of pump
    2. Only add couplers if:
        - Vari pump
        - If hand pump when selected an extension hose
        - If compact pump when selected an extension hose
    """
    mapper = ConfiguratorMapper(db)
    hoses = get_or_query_products(event, state, db, configuration)

    if hoses:
        hoses_quantity: dict | None = get_product_quantities(db, configuration, state)
        hoses_total_quantity: int | None = hoses_quantity.get("total_quantity") if hoses_quantity else None
        if not isinstance(hoses_total_quantity, int):
            _logger.debug(f"Hoses quantity not found in the configuration")
            return

        connector_article_number = None
        additional_products_article_numbers = [AdditionalProducts.HOSE_COUPLER.value]

        pump_type = get_configuration_pump_type(db, configuration)
        if pump_type == PumpType.VARI.value:
            connector_article_number = AdditionalProducts.HOSE_CONNECTOR.value

        else:
            hose_type = get_or_query_value(
                event, ConfiguratorHoseStageDataIDs.HOSE_SETTINGS_TYPE.value, db, configuration
            )
            if hose_type == HoseTypeSelection.EXTENSION.value:
                if pump_type == PumpType.HAND.value:
                    connector_article_number = AdditionalProducts.HOSE_CONNECTOR_HAND_PUMP.value
                else:
                    connector_article_number = AdditionalProducts.HOSE_CONNECTOR.value

        if connector_article_number:
            additional_products_article_numbers.append(connector_article_number)

            products = mapper.store_configuration_product(
                configuration,
                ConfiguratorStateProducts(
                    state_id=ConfiguratorState.INDUSTRIAL_LIFTING_HOSE_ADDITIONAL_PRODUCTS.value,
                    products=[
                        ConfiguratorProduct(
                            article_number=additional_products_article_numbers[0],
                            quantity=hoses_total_quantity,
                            product_id=None,
                        ),
                        ConfiguratorProduct(
                            article_number=additional_products_article_numbers[1],
                            quantity=hoses_total_quantity,
                            product_id=None,
                        ),
                    ],
                ),
            )
            db.add_all(products)
            db.commit()
