from transitions import EventData

from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.domain.repositories.catalog_repository import CatalogRepository
from holmatro_customer_portal.mappers import ConfiguratorMapper
from holmatro_customer_portal.schemas.configurator import ConfiguratorProduct, ConfiguratorStateProducts
from holmatro_customer_portal.services.configurator.configurator_enums import (
    CylinderActingType,
    FilterName,
    FlowPanelType,
)
from holmatro_customer_portal.services.configurator.default_products import AdditionalProducts, Valves
from holmatro_customer_portal.services.configurator.queries import get_product_quantities
from holmatro_customer_portal.services.configurator.states import ConfiguratorFlowPanelStageDataIDs, ConfiguratorState
from holmatro_customer_portal.services.configurator.utils import (
    get_or_query_cylinder_acting_type,
    get_or_query_products,
    get_or_query_value,
)
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.enums import Syncforce<PERSON>ategory


def get_product_flowpanel(
    event: EventData,
    db: Session,
    configuration: Configuration,
    state_definition: dict,
    catalog_repository: CatalogRepository,
) -> None:
    """
    1: determine type of cylinder acting type to define flow panel filters
    2: if cylinder acting type "single", it checks if valve was selected to determine if it should filter
        flow panel by single or double (some single acting pumps need a double acting valve)
    """

    cylinder_acting_type = get_or_query_cylinder_acting_type(event, db, configuration)
    category_path = catalog_repository.get_category_path(
        configuration.user.language.code, SyncforceCategory.FLOW_PANELS, SyncforceCategory.INDUSTRIAL_LIFTING
    )

    match cylinder_acting_type:
        case CylinderActingType.SINGLE.value:
            cylinder_acting_type_filter = CylinderActingType.SINGLE.value

        case CylinderActingType.DOUBLE.value:
            cylinder_acting_type_filter = CylinderActingType.DOUBLE.value

        case _:
            cylinder_acting_type_filter = None

    for c in state_definition["content"]:
        if c["type"] == "filters":
            c["meta"]["filters"] = {FilterName.ACTING_TYPE.value: cylinder_acting_type_filter}
            c["meta"]["category"] = category_path


def get_flowpanel_assemblyset_and_couplers(
    event: EventData,
    db: Session,
    configuration: Configuration,
    state: ConfiguratorState,
) -> None:
    """
    1: determine if flow panel was added to configuration
    2: if yes, it will fetch quantity, cylinder acting type and flow panel type to determine product to be added accordingly
    3: if flow panel 'on top', it adds assembly sets
    4: if cylinder acting type "single", it checks if valve was selected to determine if it should add
        assembly set by single or double (some single acting pumps need a double acting valve)
    """
    mapper = ConfiguratorMapper(db)
    flow_panel = get_or_query_products(event, state, db, configuration)

    if flow_panel:
        flowpanel_type = get_or_query_value(
            event, ConfiguratorFlowPanelStageDataIDs.FLOW_PANEL_SETTINGS_TYPE.value, db, configuration
        )
        flow_panel_quantity: dict | None = get_product_quantities(db, configuration, state)
        acting_type = get_or_query_cylinder_acting_type(event, db, configuration)
        if flowpanel_type == FlowPanelType.ON_TOP.value:
            valves = get_or_query_products(
                event, ConfiguratorState.INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE, db, configuration
            )
            if valves and acting_type:
                if acting_type == CylinderActingType.SINGLE.value:
                    valves_article_number = [valve.article_number for valve in valves]
                    if valves and Valves.VALVE_VARIPUMP_DOUBLE.value in valves_article_number:
                        assembly_set = AdditionalProducts.FLOW_PANEL_ASSEMBLY_SET_DOUBLE_ACTING.value
                    else:
                        assembly_set = AdditionalProducts.FLOW_PANEL_ASSEMBLY_SET_SINGLE_ACTING.value
                else:
                    assembly_set = AdditionalProducts.FLOW_PANEL_ASSEMBLY_SET_DOUBLE_ACTING.value
                products = mapper.store_configuration_product(
                    configuration,
                    ConfiguratorStateProducts(
                        state_id=ConfiguratorState.INDUSTRIAL_LIFTING_FLOW_PANEL_ASSEMBLY_SET.value,
                        products=[
                            ConfiguratorProduct(
                                article_number=assembly_set,
                                quantity=flow_panel_quantity.get("total_quantity") if flow_panel else 1,
                                product_id=None,
                            )
                        ],
                    ),
                )
                db.add(products[0])
                db.commit()
