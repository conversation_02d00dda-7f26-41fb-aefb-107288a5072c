from transitions import EventData

from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.mappers.product_preview_mapper import ProductPreviewMapper
from holmatro_customer_portal.schemas.response_schema import ProductPreviewRes
from holmatro_customer_portal.services.configurator.configurator_enums import CylinderActingType, PumpType
from holmatro_customer_portal.services.configurator.default_products import Valves
from holmatro_customer_portal.services.configurator.utils import (
    get_configuration_pump_type,
    get_or_query_cylinder_acting_type,
)
from holmatro_customer_portal.services.utils.product_queries import get_product_preview_by_article_number
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient


def get_product_valves(
    db: Session, configuration: Configuration, my_hm_client: MyHolmatroPortalApiClient, event: EventData
) -> list[ProductPreviewRes]:
    """
    1: determine type of pump and cylinder acting type to define correct valve
    2: for vari pump checks also the cylinder return type and pump second stage output
    """
    pump_type = get_configuration_pump_type(db, configuration)
    cylinder_acting_type = get_or_query_cylinder_acting_type(event, db, configuration)

    def get_products(product_article_number: list[str] | str) -> list[ProductPreviewRes]:
        products = get_product_preview_by_article_number(product_article_number, configuration.user, db)
        return ProductPreviewMapper(db, my_hm_client).product_preview_mapper(configuration.user, products)

    match pump_type:
        case PumpType.VARI.value:
            match cylinder_acting_type:
                case CylinderActingType.SINGLE.value:
                    return get_products(Valves.VALVE_VARIPUMP_SINGLE.value)
                case CylinderActingType.DOUBLE.value:
                    return get_products(Valves.VALVE_VARIPUMP_DOUBLE.value)
                case _:
                    return get_products([Valves.VALVE_VARIPUMP_DOUBLE.value, Valves.VALVE_VARIPUMP_SINGLE.value])

        case PumpType.HAND.value:
            return get_products(Valves.VALVE_HANDPUMP_DOUBLEACTING.value)

        case _:
            return get_products([valve.value for valve in Valves])
