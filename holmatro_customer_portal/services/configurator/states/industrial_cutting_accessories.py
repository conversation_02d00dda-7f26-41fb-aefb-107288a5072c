from __future__ import annotations

from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.database.enums import RelationshipType
from holmatro_customer_portal.services.configurator.queries import get_configuration_products
from holmatro_customer_portal.services.configurator.states.base import BaseState, ConfiguratorState
from holmatro_customer_portal.services.get_related_products import get_related_products_with_translation
from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class AccessoriesProductList(BaseState):
    state = ConfiguratorState.INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST

    def on_enter(self: BaseState | Configurator, event: EventData) -> None:
        _logger.debug(f"Fetching on enter information for {self.state}")

        # Fetch the selected cutter from a previous state
        selected_cutters = get_configuration_products(
            self._session,
            self._configuration,
            ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST,
        )

        if selected_cutters:
            # Only a single cutter can be selected
            selected_cutter = selected_cutters[0]

            # Use the id of the selected cutter to fetch the available accessories from the db and return them
            # Filter out the batteries and chargers from the related products listing, since they've already
            # been selected during a previous step in the configurator (2109 = Battery, 2110 = Battery Charger).
            related_products = get_related_products_with_translation(
                selected_cutter.id,
                self._configuration.user,
                self._session,
                self._my_hm_client,
                RelationshipType.CONFIGURATOR_ACCESSORIES.value,
                exclude_classifications=[2109, 2110],
            )

            # Convert list[RelatedToRes] -> list[ProductPreviewRes]
            related_products = list(map(lambda res: res.product, related_products))

            for c in self.state_definition["content"]:
                if c["type"] == "products":
                    c["meta"]["products"] = related_products
        else:
            # No cutter was selected in a previous state. Fetch the category path for the accessories and return that
            category_path: str | None = self._catalog_repository.get_category_path(
                self._configuration.user.language.code,
                SyncforceCategory.CUTTING_ACCESSORIES,
                SyncforceCategory.INDUSTRIAL_CUTTING,
            )

            if category_path:
                for c in self.state_definition["content"]:
                    if c["type"] == "filters":
                        c["meta"]["category"] = category_path
