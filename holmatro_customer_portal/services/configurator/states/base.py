from __future__ import annotations

import enum
from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.utils.enums import SyncforceCategory

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator


class ConfiguratorState(enum.Enum):
    # The order of the variables are important to correctly sort the products in the overview.

    # LIFTING
    INDUSTRIAL_LIFTING_CYLINDER_SETTINGS = "industrial.lifting.cylinder.settings"
    INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST = "industrial.lifting.cylinder.productlist"
    INDUSTRIAL_LIFTING_CYLINDER_PULLING_CLEVIS_EYES = "industrial.lifting.cylinder.pulling.pulling_clevis_eyes"
    INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING = "industrial.lifting.cylinder.pulling.protection_spring"
    INDUSTRIAL_LIFTING_PUMP_SETTINGS = "industrial.lifting.pump.settings"
    INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING = "industrial.lifting.pump.settings.acting"
    INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST = "industrial.lifting.pump.productlist"
    INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_VALVE = "industrial.lifting.system_components.valve"
    INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_GAUGESET = "industrial.lifting.system_components.gaugeset"
    INDUSTRIAL_LIFTING_GAUGE_ADDITIONAL_PRODUCT_GAUGE = (
        "industrial.lifting.system_components.gaugeset.productlist.additional_products.gauge"  # background state
    )
    INDUSTRIAL_LIFTING_GAUGE_ADDITIONAL_PRODUCT_COVER = (
        "industrial.lifting.system_components.gaugeset.productlist.additional_products.cover"  # background state
    )
    INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_SETTINGS = "industrial.lifting.system_components.flowpanel.settings"
    INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_FLOWPANEL_PRODUCTLIST = (
        "industrial.lifting.system_components.flowpanel.productlist"
    )
    INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_MAIN = (
        "industrial.lifting.system_components.hose.productlist.main"
    )
    INDUSTRIAL_LIFTING_FLOW_PANEL_ASSEMBLY_SET = (
        "industrial.lifting.system_components.flowpanel.assembly_set"  # background state
    )
    INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_PROTECTION_FRAME = "industrial.lifting.system_components.protection_frame"
    INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_COVER = "industrial.lifting.system_components.cover"
    INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_SETTINGS = "industrial.lifting.system_components.hose.settings"
    INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS_HOSE_PRODUCTLIST_PUMP_TO_PANEL = (
        "industrial.lifting.system_components.hose.productlist.pump_to_panel"
    )
    INDUSTRIAL_LIFTING_HOSE_ADDITIONAL_PRODUCTS = (
        "industrial.lifting.system_components.hose.additional_products"  # background state
    )

    # CUTTING
    INDUSTRIAL_CUTTING_CUTTER_SETTINGS = "industrial.cutting.cutter.settings"
    INDUSTRIAL_CUTTING_CUTTER_WAY_OF_CUTTING = "industrial.cutting.cutter.way_of_cutting"
    INDUSTRIAL_CUTTING_CUTTER_PRODUCTLIST = "industrial.cutting.cutter.productlist"
    INDUSTRIAL_CUTTING_HOSE_PRODUCTLIST = "industrial.cutting.hose.productlist"
    INDUSTRIAL_CUTTING_PUMP_PRODUCTLIST = "industrial.cutting.pump.productlist"
    INDUSTRIAL_CUTTING_CHARGER_BATTERIES_PRODUCTLIST = "industrial.cutting.accessories.charger_batteries.productlist"
    INDUSTRIAL_CUTTING_ACCESSORIES_PRODUCTLIST = "industrial.cutting.accessories.miscellaneous.productlist"

    # Overview need to be the last in the list
    INDUSTRIAL_OVERVIEW = "industrial.overview.main"


class ConfiguratorCylinderStageDataIDs(enum.Enum):
    """IDs corresponding to ConfigurationData keys in the state definitions.
    Not meant to be a complete list, added here if used in code."""

    CYLINDER_SETTINGS_MAIN = "industrial.lifting.cylinder.settings.main"
    CYLINDER_SETTINGS_APPLICATION = "industrial.lifting.cylinder.settings.application"
    CYLINDER_SETTINGS_APPLICATION_LIFTING = "industrial.lifting.cylinder.settings.application.lifting"
    CYLINDER_SETTINGS_APPLICATION_PULLING = "industrial.lifting.cylinder.settings.application.pulling"
    CYLINDER_SETTINGS_CALCULATION_WEIGHT = "industrial.lifting.cylinder.settings.calculation.weight"
    CYLINDER_SETTINGS_CALCULATION_SYMMETRY = "industrial.lifting.cylinder.settings.calculation.symmetry"
    CYLINDER_SETTINGS_CALCULATION_LIFTING_POINTS = "industrial.lifting.cylinder.settings.calculation.liftingpoints"


class ConfiguratorPumpStageDataIDs(enum.Enum):
    """IDs corresponding to ConfigurationData keys in the state definitions.
    Not meant to be a complete list, added here if used in code."""

    PUMP_SETTINGS_ACTING_PLURALITY = "industrial.lifting.pump.settings.acting.plurality"
    PUMP_SETTINGS_TYPE = "industrial.lifting.pump.settings.type"
    PUMP_SETTINGS_COMPACT_POWER = "industrial.lifting.pump.settings.compact.power"
    PUMP_SETTINGS_HAND_VALVE_SELECTION = "industrial.lifting.pump.settings.hand.valve"
    PUMP_SETTINGS_HAND_GAUGESET_SELECTION = "industrial.lifting.pump.settings.hand.gauge"
    PUMP_SETTINGS_COMPACT_ELECTRIC_GAUGE_SELECTION = "industrial.lifting.pump.settings.compact.electric.gauge"
    PUMP_SETTINGS_COMPACT_AIR_GAUGE_SELECTION = "industrial.lifting.pump.settings.compact.air.gauge"
    PUMP_SETTINGS_TYPE_COMPACT = "industrial.lifting.pump.settings.type.compact.title"
    PUMP_SETTINGS_HAND_VALVE = "industrial.lifting.pump.settings.hand.valve"
    PUMP_SETTINGS_NUMBER_OF_PUMPS = "industrial.lifting.pump.settings.acting.numberofpumps"


class ConfiguratorFlowPanelStageDataIDs(enum.Enum):
    """IDs corresponding to ConfigurationData keys in the state definitions.
    Not meant to be a complete list, added here if used in code."""

    FLOW_PANEL_SETTINGS_TYPE = "industrial.lifting.system_components.flowpanel.settings.type"
    FLOW_PANEL_PRODUCTLIST_PRODUCTS = "industrial.lifting.system_components.flowpanel.productlist.products"
    FLOW_PANEL_SETTINGS_FRAME_SELECTION = "industrial.lifting.system_components.flowpanel.settings.separate.frame"
    FLOW_PANEL_SETTINGS_COVER_SELECTION = "industrial.lifting.system_components.flowpanel.settings.separate.cover"
    FLOW_PANEL_SETTINGS_GAUGE_SELECTION = "industrial.lifting.system_components.flowpanel.settings.separate.gaugeset"


class ConfiguratorHoseStageDataIDs(enum.Enum):
    """IDs corresponding to ConfigurationData keys in the state definitions.
    Not meant to be a complete list, added here if used in code."""

    HOSE_SETTINGS_TYPE = "industrial.lifting.system_components.hose.settings.type"
    HOSE_PRODUCT_LIST = "industrial.lifting.system_components.hose.productlist.products"


class ConfiguratorOverviewDataIDs(enum.Enum):
    """IDs corresponding to ConfigurationData keys in the state definitions.
    Not meant to be a complete list, added here if used in code."""

    OVERVIEW_PRODUCTLIST = "industrial.overview.main.products"
    OVERVIEW_DETAILS = "industrial.overview.main.details"


class ConfiguratorCutterStageDataIDs(enum.Enum):
    """IDs corresponding to ConfigurationData keys in the state definitions.
    Not meant to be a complete list, added here if used in code."""

    CUTTER_SETTINGS_APPLICATION = "industrial.cutting.cutter.settings.application"
    CUTTER_SETTINGS_FREQUENCY = "industrial.cutting.cutter.settings.frequency"
    CUTTER_SETTINGS_REQUIREMENTS = "industrial.cutting.cutter.settings.requirements"
    CUTTER_SETTINGS_METAL_RECYCLING_REQUIREMENTS = "industrial.cutting.cutter.settings.metal_recycling.type_of_metal"
    CUTTER_SETTINGS_METAL_RECYCLING_THICKNESS = "industrial.cutting.cutter.settings.metal_recycling.size"
    CUTTER_WAY_OF_CUTTING = "industrial.cutting.cutter.way_of_cutting.type"


class BaseState:
    state: ConfiguratorState

    @property
    def on_enter_method_name(self) -> str:
        return f"on_enter_{self.state.value}".replace(".", "_")

    @property
    def on_exit_method_name(self) -> str:
        return f"on_exit_{self.state.value}".replace(".", "_")

    @property
    def definition(self) -> dict:
        return {
            "name": self.state,
            "on_enter": ["load_state_definition", self.on_enter_method_name],
            "on_exit": [self.on_exit_method_name],
        }

    def on_enter(self: BaseState | Configurator, event: EventData) -> bool:  # type: ignore
        pass

    def on_exit(self: BaseState | Configurator, event: EventData) -> bool:  # type: ignore
        pass


def get_initial_state(category: str | int) -> ConfiguratorState:
    """Get initial state based on category.
    Raises exception when category is not supported."""
    sf_category = SyncforceCategory(int(category))
    match sf_category:
        case SyncforceCategory.INDUSTRIAL_LIFTING:
            return ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS
        case SyncforceCategory.INDUSTRIAL_CUTTING:
            return ConfiguratorState.INDUSTRIAL_CUTTING_CUTTER_SETTINGS

    raise ValueError(f"Category {category} not supported.")
