from enum import Enum


class Valves(Enum):
    VALVE_HANDPUMP_DOUBLEACTING = "100.182.175"
    VALVE_VARIPUMP_SINGLE = "100.181.311"
    VALVE_VARIPUMP_DOUBLE = "100.181.322"


class Pumps(Enum):
    COMPACT_PUMP_ELECTRIC = "100.551.001"
    COMPACT_PUMP_AIR = "100.602.001"


class CylinderEyes(Enum):
    PULLING_EYES_CYLINDER_11T_30T = "100.181.051"
    PULLING_EYES_CYLINDER_60T = "100.181.052"
    CLEVIS_EYES_CYLINDER_11T = "100.181.056"
    CLEVIS_EYES_CYLINDER_30T = "100.181.057"


class CylinderProtectionSpring(Enum):
    PROTECTION_SPRING_11T_30T = "100.581.160"
    PROTECTION_SPRING_60T = "100.581.161"


class GaugeSet(Enum):
    GAUGESET_HAND_PUMP_SINGLE_ACTING_OIL_LESS_1700 = "100.182.213"
    GAUGESET_HAND_PUMP_SINGLE_ACTING_OIL_MORE_1700 = "100.182.214"
    GAUGESET_HAND_PUMP_DOUBLE_ACTING = "100.182.216"
    GAUGESET_COMPACT_PUMP_AIR = "100.182.215"
    GAUGESET_COMPACT_PUMP_ELECTRIC_GAUGE = "100.182.158"
    GAUGESET_COMPACT_PUMP_ELECTRIC_GAUGE_AND_NEEDLE = "100.182.162"
    GAUGESET_VARI_PUMP = "100.582.500"


class FlowPanel(Enum):
    FRAME = "100.151.056"
    COVER = "100.151.040"


class Hoses(Enum):
    EXTENSION_HOSE_2M = "100.572.302"
    STANDARD_HOSE_2M = "100.572.102"


class CuttingPumps(Enum):
    FRIDGE_MOBILE = "170.152.503"
    FRIDGE_STATIONARY = "101.002.639"
    STATIONARY = "101.002.660"
    MOBILE = ["170.152.039", "170.152.007"]


class Chargers(Enum):
    CHARGER_230V = "150.182.208"
    CHARGER_110V = "150.182.209"


class Batteries(Enum):
    BATTERY_28V_6AH = "151.000.307"


class AdditionalProducts(Enum):
    GAUGESET_VARI_PUMP_SINGLE_ACTING = "100.181.108"
    GAUGESET_VARI_PUMP_DOUBLE_ACTING = "100.182.308"
    GAUGESET_VARI_PUMP_COVER = "350.581.160"
    HOSE_COUPLER = "100.181.119"
    HOSE_CONNECTOR_HAND_PUMP = "150.581.218"
    HOSE_CONNECTOR = "100.581.101"
    FLOW_PANEL_ASSEMBLY_SET_SINGLE_ACTING = "100.182.111"
    FLOW_PANEL_ASSEMBLY_SET_DOUBLE_ACTING = "100.182.112"
