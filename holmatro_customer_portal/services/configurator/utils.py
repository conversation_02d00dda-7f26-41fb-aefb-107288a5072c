from transitions import EventData

from holmatro_customer_portal.database.models import Configuration, Product
from holmatro_customer_portal.services.configurator.configurator_enums import (
    CylinderActingType,
    CylinderReturnType,
    SyncForceAttributeId,
)
from holmatro_customer_portal.services.configurator.queries import (
    get_configuration_data_value,
    get_configuration_product_attribute,
    get_configuration_products,
    get_product_quantities,
)
from holmatro_customer_portal.services.configurator.states import ConfiguratorPumpStageDataIDs, ConfiguratorState
from holmatro_customer_portal.utils.database import Session


def get_configuration_cylinder_acting_type(db: Session, configuration: Configuration) -> str | None:
    """Get the acting type of the cylinder in the configuration"""
    acting_type: str | None = get_configuration_product_attribute(
        db,
        configuration,
        ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST,
        product_attribute_id=SyncForceAttributeId.CYLINDER_ACTING_TYPE,
    )

    if not acting_type:
        return_type = get_configuration_product_attribute(
            db,
            configuration,
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST,
            product_attribute_id=SyncForceAttributeId.CYLINDER_RETURN_TYPE,
        )

        cylinder_type_mapping = {
            CylinderReturnType.SPRING.value: CylinderActingType.SINGLE.value,
            CylinderReturnType.GRAVITY.value: CylinderActingType.SINGLE.value,
            CylinderReturnType.HYDRAULIC.value: CylinderActingType.DOUBLE.value,
        }
        acting_type = cylinder_type_mapping.get(return_type)  # type: ignore

    return acting_type


def get_configuration_pump_type(db: Session, configuration: Configuration) -> int | str | None:
    pump_type = get_configuration_data_value(db, configuration, ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_TYPE.value)
    if not pump_type:
        pump_type = get_configuration_product_attribute(
            db,
            configuration,
            ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST,
            product_attribute_id=SyncForceAttributeId.PUMP_OPERATOR_TYPE,
        )

    return pump_type


def get_or_query_value(
    event: EventData, attribute_id: str, db: Session, configuration: Configuration
) -> str | int | None:
    """
    Check if the attribute has already been queried and stored in the Event
    if not, it gets the value for the attribute in the configuration
    """
    if attribute_id not in event.kwargs:
        event.kwargs[attribute_id] = get_configuration_data_value(db, configuration, attribute_id)

    attribute_value: str | int | None = event.kwargs[attribute_id]
    return attribute_value


def get_or_query_products(
    event: EventData, state: ConfiguratorState, db: Session, configuration: Configuration
) -> list[Product]:
    """
    Check if the product has already been queried and stored in the Event
    if not, it fetches the product in the configuration for a specific state
    """
    if state.value not in event.kwargs:
        event.kwargs[state.value] = get_configuration_products(db, configuration, state)

    configuration_product: list[Product] = event.kwargs[state.value]
    return configuration_product


def get_or_query_pump_type(event: EventData, db: Session, configuration: Configuration) -> int | str | None:
    """Get the pump type in the configuration"""
    pump_type_id = ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_TYPE.value
    if pump_type_id not in event.kwargs:
        pump_type = get_configuration_data_value(
            db, configuration, ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_TYPE.value
        )
        if not pump_type:
            pump_type = get_configuration_product_attribute(
                db,
                configuration,
                ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST,
                product_attribute_id=SyncForceAttributeId.PUMP_OPERATOR_TYPE,
            )
        event.kwargs[pump_type_id] = pump_type
    else:
        pump_type = event.kwargs[pump_type_id]

    return pump_type


def get_or_query_cylinder_acting_type(event: EventData, db: Session, configuration: Configuration) -> str | None:
    """
    Get the acting type of the cylinder in the configuration by checking if
      user has selected a product or if user has selected an acting type
    """
    if "cylinder_acting_type" in event.kwargs:
        return event.kwargs["cylinder_acting_type"]  # type: ignore

    acting_type = get_configuration_cylinder_acting_type(db, configuration) or get_configuration_data_value(
        db, configuration, ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_ACTING_PLURALITY.value
    )

    event.kwargs["cylinder_acting_type"] = acting_type
    return event.kwargs["cylinder_acting_type"]  # type: ignore


def get_or_query_cylinder_quantity(event: EventData, db: Session, configuration: Configuration) -> int | None:
    """Get the total quantity of cylinders"""
    if "cylinder_quantity" not in event.kwargs:
        cylinder_quantity: dict | None = get_product_quantities(
            db, configuration, ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST
        )
        if cylinder_quantity:
            event.kwargs["cylinder_quantity"] = cylinder_quantity.get("total_quantity")

        else:
            return None

    return event.kwargs["cylinder_quantity"]  # type: ignore
