# Configurator System Documentation

## Overview

The Configurator is a state machine-based system built using the `transitions` library that manages the flow of product configuration processes. It dynamically creates workflows for different product categories (lifting, cutting) and handles state transitions with custom logic.

## Architecture

### Core Components

1. **Configurator Class** (`workflow.py`)
   - Main state machine instance
   - Manages state definitions and transitions
   - Handles dynamic method binding

2. **ConfiguratorFactory** (`workflow.py`)
   - Creates configurator instances for specific states
   - Dynamically binds state methods and transitions

3. **BaseState** (`states/base.py`)
   - Abstract base class for all states
   - Defines `on_enter` and `on_exit` method patterns

4. **BaseTransition** (`transitions/base.py`)
   - Abstract base class for transitions
   - Defines condition logic for state changes

## State Machine Flow

### Initialization Process

1. **Factory Creation**: `ConfiguratorFactory.get_configurator_for_state()` is called with:
   - Database session
   - Target state
   - Configuration object
   - External service clients

2. **State Registration**: All states from `BaseState.__subclasses__()` are registered with the machine

3. **Method Binding**: For each state class:
   ```python
   enter_method_name = s().on_enter_method_name
   setattr(configurator, enter_method_name, types.MethodType(s.on_enter, configurator))
   
   exit_method_name = s().on_exit_method_name
   setattr(configurator, exit_method_name, types.MethodType(s.on_exit, configurator))
   ```

4. **Transition Setup**: Transitions are added based on the current state's available transitions

### State Transition Flow

When a transition occurs (via `next()`, `back()`, or `skip()`):

1. **Exit Current State**: Current state's `on_exit()` method executes
2. **State Change**: State machine transitions to the target state
3. **Enter New State**: New state's `on_enter()` method executes
4. **Load Definition**: `load_state_definition()` loads the JSON configuration for the new state

## State Definitions

### JSON Structure

Each state has a corresponding JSON file in `states/definitions/` with this structure:

```json
{
    "stage": "industrial.lifting.pump",
    "type": "productlist",
    "id": "industrial.lifting.pump.productlist",
    "title": "industrial.lifting.pump.productlist.title",
    "subtitle": "industrial.lifting.pump.productlist.subtitle",
    "icon": "https://...",
    "content": [
        {
            "id": "industrial.lifting.pump.productlist.products",
            "type": "products",
            "meta": {
                "products": [],
                "initial": 1,
                "maximum": null
            }
        }
    ]
}
```

### Dynamic Loading

The `Configurator.load()` method:
- Reads the JSON file for the current state
- Performs S3 URL replacement for assets
- Returns parsed JSON as the state definition

## Meta Object Management

### Structure

The `meta` object within each content item contains:
- **products**: List of available products
- **initial**: Default quantity/selection
- **maximum**: Maximum allowed quantity
- **choices**: Available options for selection
- **filters**: Product filtering criteria

### Dynamic Population

States modify the meta object in their `on_enter()` methods:

```python
def on_enter(self: BaseState | Configurator, event: EventData) -> None:
    for c in self.state_definition["content"]:
        if c["type"] == "products":
            # Populate products from database/API
            c["meta"]["products"] = get_products_for_state()
            c["meta"]["initial"] = calculate_default_quantity()
```

### Data Flow Between States

1. **Configuration Data**: Stored in database via `ConfigurationData` model
2. **Product Selections**: Stored via `ConfigurationProduct` model  
3. **State Context**: Passed through the configurator instance
4. **Meta Updates**: Applied dynamically when entering each state

## API Integration

### Endpoints

The configurator integrates with FastAPI endpoints:

- **POST /initiate**: Creates new configuration session
- **POST /next**: Advances to next state
- **POST /back**: Returns to previous state  
- **POST /skip**: Skips current stage
- **GET /configurations**: Lists user configurations

### Request/Response Flow

1. Frontend sends configuration data and product selections
2. API validates and stores data via `ConfiguratorMapper`
3. State machine transitions to new state
4. Response includes updated state definition and products
5. Frontend renders new state based on returned meta object

## Key Features

### Default Product Handling

The `set_default_product()` method injects default products into state definitions:

```python
def set_default_product(self, product_article_number: str) -> None:
    default_product = get_product_preview_by_article_number(...)
    mapped_product = ProductPreviewMapper(...).product_preview_mapper(...)
    
    for c in self.state_definition["content"]:
        if default_product and c["type"] == "products":
            c["meta"]["default_product"] = mapped_product[0]
```

### Asset URL Management

S3 URLs are dynamically replaced based on environment:

```python
s3_pattern = re.compile(r"(https://)([^.]+)(.s3.[^.]+.amazonaws.com/)")
parsed = re.sub(Configurator.s3_pattern, rf"\1{Env.HOLMATRO_ASSETS_BUCKET_NAME.get()}\3", read)
```

## State Categories

### Industrial Lifting States
- Cylinder settings and product selection
- Pump configuration and selection  
- System components (valves, gauges, hoses)
- Overview and finalization

### Industrial Cutting States
- Cutter settings and selection
- Hose and pump selection
- Accessories and batteries
- Overview and finalization

Each category follows a similar pattern but with category-specific logic and product filtering.
