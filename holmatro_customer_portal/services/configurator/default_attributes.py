from enum import Enum

from holmatro_customer_portal.utils.enums import SyncforceCategory


class AttributesIds(Enum):
    CAPACITY_OIL_TANK_EFFECTIVE_METRIC = 384
    CAPACITY_OIL_TANK_EFFECTIVE_IMPERIAL = 585
    FIRST_STAGE_OUTPUT_MIN_METRIC = 175
    FIRST_STAGE_OUTPUT_MIN_IMPERIAL = 574
    WEIGHT_READY_FOR_USE_METRIC = 415
    WEIGHT_READY_FOR_USE_IMPERIAL = 416
    LENGTH_METRIC = 598
    LENGTH_IMPERIAL = 599
    BLADE_TYPE = 7
    MAX_CUTTING_OPENING_METRIC = 70
    MAX_CUTTING_OPENING_IMPERIAL = 71
    TONNAGE_METRIC = 98
    TONNAGE_IMPERIAL = 99
    STROKE_METRIC = 100
    STROKE_IMPERIAL = 101
    CLOSED_HEIGHT_METRIC = 102
    CLOSED_HEIGHT_IMPERIAL = 103
    OPERATOR_TYPE = 358
    ACTING_TYPE = 359
    NUMBER_OF_OUTLET_PORTS = 294


class DefaultAttributes(Enum):
    # Define enum members with input and output strings
    CYLINDERS = (
        SyncforceCategory.CYLINDERS.value,
        [
            AttributesIds.TONNAGE_METRIC.value,
            AttributesIds.TONNAGE_IMPERIAL.value,
            AttributesIds.STROKE_METRIC.value,
            AttributesIds.STROKE_IMPERIAL.value,
            AttributesIds.CLOSED_HEIGHT_METRIC.value,
            AttributesIds.CLOSED_HEIGHT_IMPERIAL.value,
        ],
    )
    CYLINDER_WEDGES = (
        SyncforceCategory.CYLINDER_WEDGES.value,
        [
            AttributesIds.TONNAGE_METRIC.value,
            AttributesIds.TONNAGE_IMPERIAL.value,
            AttributesIds.STROKE_METRIC.value,
            AttributesIds.STROKE_IMPERIAL.value,
            AttributesIds.CLOSED_HEIGHT_METRIC.value,
            AttributesIds.CLOSED_HEIGHT_IMPERIAL.value,
        ],
    )
    CYLINDER_TOE_JACKS = (
        SyncforceCategory.CYLINDER_TOE_JACKS.value,
        [
            AttributesIds.TONNAGE_METRIC.value,
            AttributesIds.TONNAGE_IMPERIAL.value,
            AttributesIds.STROKE_METRIC.value,
            AttributesIds.STROKE_IMPERIAL.value,
            AttributesIds.CLOSED_HEIGHT_METRIC.value,
            AttributesIds.CLOSED_HEIGHT_IMPERIAL.value,
        ],
    )
    HYDRAULIC_PUMPS = (
        SyncforceCategory.HYDRAULIC_PUMPS.value,
        [
            AttributesIds.OPERATOR_TYPE.value,
            AttributesIds.CAPACITY_OIL_TANK_EFFECTIVE_METRIC.value,
            AttributesIds.CAPACITY_OIL_TANK_EFFECTIVE_IMPERIAL.value,
        ],
    )
    HOSES = (
        SyncforceCategory.LIFTING_HOSES.value,
        [AttributesIds.LENGTH_METRIC.value, AttributesIds.LENGTH_IMPERIAL.value],
    )
    FLOW_PANELS = (
        SyncforceCategory.FLOW_PANELS.value,
        [AttributesIds.ACTING_TYPE.value, AttributesIds.NUMBER_OF_OUTLET_PORTS.value],
    )
    CUTTERS = (
        SyncforceCategory.CUTTERS.value,
        [
            AttributesIds.BLADE_TYPE.value,
            AttributesIds.MAX_CUTTING_OPENING_METRIC.value,
            AttributesIds.MAX_CUTTING_OPENING_IMPERIAL.value,
            AttributesIds.WEIGHT_READY_FOR_USE_METRIC.value,
            AttributesIds.WEIGHT_READY_FOR_USE_IMPERIAL.value,
        ],
    )
    CUTTING_PUMPS = (
        SyncforceCategory.CUTTER_PUMPS.value,
        [
            AttributesIds.CAPACITY_OIL_TANK_EFFECTIVE_METRIC.value,
            AttributesIds.CAPACITY_OIL_TANK_EFFECTIVE_IMPERIAL.value,
            AttributesIds.FIRST_STAGE_OUTPUT_MIN_METRIC.value,
            AttributesIds.FIRST_STAGE_OUTPUT_MIN_IMPERIAL.value,
            AttributesIds.WEIGHT_READY_FOR_USE_METRIC.value,
            AttributesIds.FIRST_STAGE_OUTPUT_MIN_IMPERIAL.value,
        ],
    )
    CUTTING_HOSES = (
        SyncforceCategory.CUTTER_HOSES.value,
        [AttributesIds.LENGTH_METRIC.value, AttributesIds.LENGTH_IMPERIAL.value],
    )

    @staticmethod
    def get_output_value(input_value: int) -> list:
        for attribute in DefaultAttributes:
            if attribute.value[0] == input_value:
                return attribute.value[1]
        return []  # or raise an exception, or return a default value, as per your requirements
