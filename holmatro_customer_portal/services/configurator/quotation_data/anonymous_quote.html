<html title="{{ quote.title }}" lang="en">
<head title="{{ quote.title }}">
    <meta charset="UTF-8">
    <style>
        body {
            background-color: white;
            font-family: Arial,sans-serif;
            font-size: 12px;
        }

        h1 {
            font-size: 24px;
            line-height: 120%;
            text-transform: uppercase;
            margin-bottom:5.67px;
        }

        h2 {
            font-size: 16px;
            margin-bottom: 5.67px;
        }

        hr {
            background-color: #e0e0e0;
            height: 1px;
            border: 0;
            margin: 16px 0;
        }

        .thematic-break--large {
            margin: 13.33px 0 16px 0;
        }

        table, tr, td, th {
            border-collapse: collapse;
            border-spacing: 0;
        }

        .header__logo {
            text-align: right;
        }

        .header__subtitle {
            margin-bottom: 21.33px;
        }

        .header__table {
            margin-bottom: 48px;
        }

        .header__details {
            margin-bottom: 26.66px;
        }

        .header__details-item {
            margin-bottom: 2.66px;
            line-height: 150%;
        }

        .table-header {
            border-bottom: 0.66px solid #e0e0e0;
        }

        .table-header__item-description {
            font-weight: normal;
        }

        .table-header__item {
            text-align: left;
            vertical-align: top;
            white-space: nowrap;
            line-height: 16px;
        }

        .reference-date-table th {
            padding: 0 16px 0 0;
            text-align: left;
        }

        .reference-date-table th,
        .reference-date-table td {
            padding-bottom: 8px;
            font-size: 12px;

        }
        .products-table {
            width: 100%;
        }
        
        .products-table tr:nth-child(even) {
            background: #fff
        }

        .products-table tr:nth-child(odd) {
            background: #eaeaea
        }

        .products-table td, th {
            padding: 8px 16px;
        }

        .products-table td {
            font-size: 12px;
            vertical-align: top;
            line-height: 100%;
        }

        .products-table th {
            font-size: 9.666px;
            background-color: #fff;
        }

        .products-table__quantity {
            font-size: 9.66px;
        }

        .products-table__product-name {
            font-weight: bold;
            line-height: 120%;
        }

        .products-table__product-number {
            font-size: 9.66px;
        }

        .subtotal {
            text-align: right;
        }

        .subtotal__label {
            font-weight: bold;
            margin-right: 26.67px;
        }

        .notes-table {
            margin-top: 108px;
        }

        .notes-table td {
            vertical-align: top;
            padding-bottom: 2.67px;
            font-size: 9.66px;
            line-height: 100%;
        }

        .notes-table td:first-child {
            padding-right: 10.67px;
        }

        .company-details__item {
            display: inline-block;
            width: fit-content;
            margin-right: 5.33px;

            line-height: 100%;
        }
        footer {
            position: absolute;
            bottom: 50px;
            left: 0;
            right: 0;
            
        }

        .company-details__item:last-child {
            margin-right: 0;
        }
    </style>
    <title></title>
</head>
<body >
<header class="header">
    <div class="header__logo">
        <svg width="278" height="60" viewBox="0 0 278 60" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_4562_17884)">
                <path d="M259.09 22.5462C259.09 17.4947 261.309 14.6978 265.079 14.6978C268.85 14.6978 271.03 17.4947 271.03 22.5462C271.03 27.5978 268.888 30.3947 265.079 30.3947C261.271 30.3947 259.09 27.5978 259.09 22.5462ZM252.206 22.5462C252.206 30.9464 257.11 36.1121 265.089 36.1121C273.068 36.1121 278.009 30.9464 278.009 22.5462C278.009 14.1461 273.106 9.01841 265.089 9.01841C257.072 9.01841 252.206 14.1461 252.206 22.5462ZM235.915 35.1798H242.761V21.8042C242.761 17.9989 244.675 16.1248 248.359 16.1248H250.616V9.28479C250.302 9.19917 249.921 9.19917 249.607 9.19917C246.341 9.19917 243.999 10.7593 242.247 14.2507V9.90315H235.905V35.1703L235.915 35.1798ZM231.601 35.4462V30.2805H229.926C228.098 30.2805 227.631 30.0046 227.631 28.6442V14.6883H231.601V9.90315H227.631V3.03459H220.861V9.92217H217.557V14.7073H220.861V28.3493C220.861 33.6387 222.07 35.5794 227.707 35.5794C228.402 35.5794 229.726 35.5033 231.601 35.4652M207.664 32.088C207.664 33.2962 207.902 34.2665 208.407 35.1988H215.482V34.1143C214.358 33.4865 214.044 32.402 214.044 30.5374V16.1533C214.044 11.606 210.226 9.03744 202.989 9.03744C196.458 9.03744 192.563 12.2244 192.563 17.3235V17.8277H198.981V17.4757C198.981 15.4589 200.495 14.3268 203.265 14.3268C206.217 14.3268 207.464 15.1069 207.464 17.1617C207.464 19.5686 205.636 19.1786 200.304 19.9967C194.391 20.929 191.706 23.1075 191.706 28.0449C191.706 33.2962 194.506 36.2073 199.409 36.2073C203.104 36.2073 205.941 34.7327 207.655 32.088M207.502 22.7936V25.1624C207.502 28.7394 205.246 30.9179 201.552 30.9179C199.571 30.9179 198.248 29.7097 198.248 27.8451C198.248 25.9805 199.295 25.1243 201.437 24.506C202.761 24.1159 204.941 24.0018 207.502 22.7936ZM151.297 35.1988H158.029V19.9586C158.029 16.8859 159.781 15.0974 162.351 15.0974C164.684 15.0974 166.084 16.4483 166.084 18.9407V35.1893H172.739V19.873C172.739 16.8859 174.491 15.0974 177.062 15.0974C179.395 15.0974 180.833 16.4483 180.833 18.9407V35.1893H187.564V17.5803C187.564 12.329 184.299 9.21819 179.205 9.21819C176.167 9.21819 173.758 10.4644 171.968 12.9188C170.292 10.3503 168.074 9.21819 164.884 9.21819C162.047 9.21819 159.676 10.3883 157.848 12.7191V9.92217H151.306V35.1893L151.297 35.1988ZM139.243 35.1988H146.089V0.970215H139.243V35.1798V35.1988ZM116.362 22.5653C116.362 17.5137 118.581 14.7168 122.351 14.7168C126.122 14.7168 128.302 17.5137 128.302 22.5653C128.302 27.6168 126.16 30.4137 122.351 30.4137C118.543 30.4137 116.362 27.6168 116.362 22.5653ZM109.478 22.5653C109.478 30.9655 114.382 36.1311 122.361 36.1311C130.34 36.1311 135.272 30.9655 135.272 22.5653C135.272 14.1651 130.369 9.03744 122.361 9.03744C114.353 9.03744 109.478 14.1651 109.478 22.5653ZM83.2656 35.1988H89.9593V20.1965C89.9593 16.8954 92.0254 14.9071 95.2532 14.9071C99.138 14.9071 99.1856 17.314 99.1856 22.023V35.2084H105.994V18.408C105.994 15.8775 105.917 13.8987 104.594 12.2244C103.08 10.3598 100.585 9.31333 97.5479 9.31333C94.5105 9.31333 91.8731 10.3978 89.9593 12.7381V0.970215H83.2656V35.1798V35.1988Z" fill="#67686A"/>
                <path d="M58.2437 17.7423C58.2437 17.5996 58.2437 15.2404 58.2532 15.0977C58.377 10.0652 59.0721 5.29902 60.2242 1.01807C54.6065 1.48422 49.3316 2.15965 44.5327 2.99682C42.9998 6.72601 42.0667 10.7691 41.8667 15.012C41.8667 15.1833 41.8477 17.5711 41.8477 17.7423C42.3999 37.1684 58.3199 52.7511 77.9057 52.7511V52.4847C67.232 52.4847 58.5579 37.0257 58.2532 17.7328" fill="#F45C00"/>
                <path d="M78.524 33.0966C44.4274 33.0966 16.7769 25.7523 16.7769 16.6957C16.7769 10.9783 27.7933 5.94577 44.5036 3.00618C44.5036 3.00618 44.8559 2.21658 45.1892 1.48407C45.5795 0.637387 45.9223 0 45.9223 0C18.8431 2.91105 0 9.342 0 16.8099C0 27.0176 35.1535 35.2941 78.524 35.2941V33.1061V33.0966Z" fill="#B8B8BB"/>
                <path d="M139.222 48.0323H140.536V49.2785H140.565C141.193 48.3272 142.146 47.8325 143.317 47.8325C144.355 47.8325 145.307 48.2416 145.659 49.2785C146.24 48.3748 147.24 47.8325 148.325 47.8325C150.02 47.8325 151.162 48.5365 151.162 50.306V56.5466H149.763V50.9624C149.763 49.9064 149.487 49.0692 148.116 49.0692C146.744 49.0692 145.887 49.9254 145.887 51.2383V56.5371H144.488V50.9529C144.488 49.8493 144.145 49.0597 142.888 49.0597C141.222 49.0597 140.613 50.5914 140.613 51.2287V56.5276H139.213V48.0133L139.222 48.0323Z" fill="#67686A"/>
                <path d="M161.589 56.5084C161.342 56.6606 161.028 56.7367 160.58 56.7367C159.856 56.7367 159.39 56.3466 159.39 55.4238C158.619 56.3276 157.581 56.7367 156.391 56.7367C154.839 56.7367 153.572 56.0422 153.572 54.3489C153.572 52.4272 155.001 52.0086 156.457 51.7327C158.009 51.4378 159.323 51.5329 159.323 50.4865C159.323 49.2688 158.314 49.069 157.428 49.069C156.238 49.069 155.372 49.4305 155.305 50.6863H153.906C153.991 48.5838 155.619 47.8418 157.514 47.8418C159.047 47.8418 160.713 48.1843 160.713 50.1821V54.5581C160.713 55.2146 160.713 55.5095 161.161 55.5095C161.275 55.5095 161.408 55.4904 161.589 55.4238V56.5084ZM159.314 52.1418C158.771 52.5413 157.714 52.5508 156.771 52.7221C155.848 52.8838 155.058 53.2168 155.058 54.2537C155.058 55.1765 155.848 55.5 156.705 55.5C158.552 55.5 159.304 54.3489 159.304 53.5783V52.1418H159.314Z" fill="#67686A"/>
                <path d="M164.587 53.8637C164.635 55.1099 165.72 55.5095 166.882 55.5095C167.758 55.5095 168.938 55.3097 168.938 54.2442C168.938 53.1787 167.558 52.9789 166.158 52.665C164.778 52.3511 163.378 51.8944 163.378 50.2582C163.378 48.5458 165.073 47.8418 166.558 47.8418C168.434 47.8418 169.938 48.4316 170.053 50.5055H168.653C168.558 49.421 167.596 49.069 166.672 49.069C165.834 49.069 164.863 49.2973 164.863 50.1535C164.863 51.1619 166.349 51.3236 167.644 51.6376C169.043 51.9515 170.424 52.4082 170.424 54.0539C170.424 56.0803 168.529 56.7367 166.796 56.7367C164.882 56.7367 163.273 55.9661 163.188 53.8542H164.587V53.8637Z" fill="#67686A"/>
                <path d="M174.691 48.032H176.386V49.2687H174.691V54.5485C174.691 55.1859 174.872 55.3096 175.748 55.3096H176.386V56.5463H175.319C173.872 56.5463 173.291 56.2514 173.291 54.6817V49.2687H171.844V48.032H173.291V45.4824H174.691V48.032Z" fill="#67686A"/>
                <path d="M185.659 53.8446C185.279 55.7188 183.927 56.7462 182.022 56.7462C179.309 56.7462 178.023 54.8721 177.938 52.2655C177.938 49.7159 179.613 47.8418 181.946 47.8418C184.964 47.8418 185.878 50.6577 185.802 52.7126H179.423C179.375 54.1966 180.213 55.5095 182.079 55.5095C183.231 55.5095 184.041 54.9482 184.288 53.8446H185.669H185.659ZM184.307 51.4759C184.241 50.144 183.241 49.069 181.851 49.069C180.385 49.069 179.509 50.1725 179.413 51.4759H184.307Z" fill="#67686A"/>
                <path d="M188.059 48.032H189.373V49.83H189.411C190.087 48.4601 191.029 47.7846 192.543 47.8417V49.3258C190.287 49.3258 189.458 50.6101 189.458 52.7696V56.5558H188.059V48.0415V48.032Z" fill="#67686A"/>
                <path d="M195.886 46.5005H194.486V44.7881H195.886V46.5005ZM194.486 48.0321H195.886V56.5464H194.486V48.0321Z" fill="#67686A"/>
                <path d="M198.742 48.0323H200.056V49.3832H200.084C200.675 48.3272 201.636 47.8325 202.855 47.8325C205.093 47.8325 205.788 49.1168 205.788 50.9433V56.5371H204.388V50.7721C204.388 49.7352 203.731 49.0597 202.655 49.0597C200.96 49.0597 200.132 50.1918 200.132 51.7234V56.5276H198.732V48.0133L198.742 48.0323Z" fill="#67686A"/>
                <path d="M215.976 55.8234C215.976 58.5918 214.709 59.9997 212.024 59.9997C210.425 59.9997 208.577 59.3623 208.501 57.4977H209.901C209.968 58.5157 211.186 58.8772 212.11 58.8772C213.938 58.8772 214.662 57.5643 214.662 55.6522V55.0909H214.633C214.167 56.1278 213.053 56.6415 211.977 56.6415C209.444 56.6415 208.168 54.6533 208.168 52.332C208.168 50.3248 209.158 47.8418 212.11 47.8418C213.176 47.8418 214.138 48.3175 214.643 49.2593H214.662V48.0416H215.976V55.8234ZM214.595 52.1228C214.595 50.6292 213.938 49.0785 212.177 49.0785C210.415 49.0785 209.653 50.5436 209.653 52.1703C209.653 53.702 210.215 55.4143 212.062 55.4143C213.909 55.4143 214.595 53.721 214.595 52.1228Z" fill="#67686A"/>
                <path d="M222.992 48.032H225.22V49.1165H225.249C225.811 48.2128 226.734 47.8037 227.782 47.8037C230.448 47.8037 231.657 49.9632 231.657 52.3796C231.657 54.6532 230.4 56.7747 227.915 56.7747C226.896 56.7747 225.925 56.3276 225.363 55.4904H225.334V59.543H222.992V48.032ZM229.314 52.313C229.314 50.9621 228.772 49.5637 227.267 49.5637C225.763 49.5637 225.239 50.9336 225.239 52.313C225.239 53.6924 225.763 55.0147 227.286 55.0147C228.81 55.0147 229.314 53.7019 229.314 52.313Z" fill="#67686A"/>
                <path d="M238.085 47.8037C240.77 47.8037 242.502 49.5827 242.502 52.294C242.502 55.0052 240.77 56.7747 238.085 56.7747C235.399 56.7747 233.686 54.9957 233.686 52.294C233.686 49.5922 235.418 47.8037 238.085 47.8037ZM238.085 55.0147C239.684 55.0147 240.16 53.6448 240.16 52.3035C240.16 50.9621 239.684 49.5732 238.085 49.5732C236.485 49.5732 236.028 50.9431 236.028 52.3035C236.028 53.6639 236.504 55.0147 238.085 55.0147Z" fill="#67686A"/>
                <path d="M254.347 56.5466H251.938L250.424 50.8386H250.386L248.939 56.5466H246.521L243.816 48.0322H246.292L247.854 53.8068H247.892L249.31 48.0322H251.586L253.033 53.7972H253.071L254.633 48.0322H257.042L254.357 56.5466H254.347Z" fill="#67686A"/>
                <path d="M260.631 52.8552C260.698 54.3393 261.422 55.0147 262.726 55.0147C263.669 55.0147 264.421 54.4344 264.573 53.9112H266.63C265.973 55.9185 264.573 56.7747 262.64 56.7747C259.955 56.7747 258.289 54.9291 258.289 52.294C258.289 49.6588 260.051 47.8037 262.64 47.8037C265.535 47.8037 266.944 50.2391 266.773 52.8552H260.631ZM264.44 51.3712C264.231 50.182 263.716 49.5637 262.574 49.5637C261.088 49.5637 260.66 50.7148 260.631 51.3712H264.44Z" fill="#67686A"/>
                <path d="M269.068 48.032H271.296V49.6112H271.325C271.753 48.5457 272.906 47.8037 274.048 47.8037C274.21 47.8037 274.41 47.8323 274.562 47.8893V50.0583C274.353 50.0108 274.001 49.9727 273.724 49.9727C272.011 49.9727 271.42 51.2095 271.42 52.703V56.5369H269.078V48.0225L269.068 48.032Z" fill="#67686A"/>
            </g>
            <defs>
                <clipPath id="clip0_4562_17884">
                    <rect width="278" height="60" fill="white"/>
                </clipPath>
            </defs>
        </svg>
    </div>
    <h1 class="header__title">{{ quote.title }}</h1>
    <div class="header__subtitle">
        <span>{{ quote.quotation_number_prefix }}</span>
        <span>{{ quote.quotation_number }}</span>
    </div>
    <table class="header__table reference-date-table">
        <tr>
            <th scope="row">{{ quote.reference_label }}</th>
            <td>{{ quote.reference_value }}</td>
        </tr>
        <tr>
            <th scope="row">{{ quote.date_label }}</th>
            <td>{{ quote.date_value }}</td>
        </tr>
    </table>
    {# <div class="header__details">
        <h2>{{ quote.details_label }}</h2>
        <div class="header__details-item">{{ quote.details_company }}</div>
        <div class="header__details-item">{{ quote.details_name }}</div>
        <div class="header__details-item">{{ quote.details_email }}</div>
        <div class="header__details-item">{{ quote.details_phone }}</div>
    </div> #}
</header>
<main>
    <table class="products-table">
        <tr class="table-header">
            <th class="table-header__item">
                <div>{{ item_header.quantity }}</div>
            </th>
            <th class="table-header__item">
                <div>{{ item_header.product_name }}</div>
                <div class="table-header__item-description">{{ item_header.product_name_sub }}</div>
            </th>
            {# <th class="table-header__item">
                <div>{{ item_header.availability }}</div>
                <div class="table-header__item-description">{{ item_header.availability_sub }}</div>
            </th>
            <th class="table-header__item">
                <div>{{ item_header.unit_price }}</div>
                <div class="table-header__item-description">({{ quote.currency_label }})</div>
            </th>
            <th class="table-header__item">
                <div>{{ item_header.discount }}</div>
            </th>
            <th class="table-header__item">
                <div>{{ item_header.net_price }}</div>
                <div class="table-header__item-description">({{ quote.currency_label }})</div>
            </th>
            <th class="table-header__item">
                <div>{{ item_header.line_total }}</div>
                <div class="table-header__item-description">({{ quote.currency_label }})</div>
            </th> #}
        </tr>
        {% for item in quote_items %}
        <tr>
            <td>
                <div class="products-table__quantity">{{ item.quantity }}</div>
            </td>
            <td>
                <div class="products-table__product-name">{{ item.product_name }}</div>
                <div class="products-table__product-number">{{ item.product_nr }}</div>
            </td>
            {# <td>
                <div>{{ item.availability }}</div>
            </td>
            <td>
                <div>{{ item.unit_price }}</div>
            </td>
            <td>
                <div>{{ item.discount }}</div>
            </td>
            <td>
                <div>{{ item.net_price }}</div>
            </td>
            <td>
                <div>{{ item.line_total }}</div>
            </td> #}
        </tr>
        {% endfor %}
    </table>
    <hr class="thematic-break thematic-break--large" />
    {# <div class="subtotal">
        <span class="subtotal__label">{{ quote.subtotal_label }} {{ quote.currency_label }}</span>
        <span class="subtotal__value">{{ quote.subtotal_value }}</span>
    </div> #}
</main>
<footer>
    <table class="notes-table">
        {# <tr>
            <td>Validity:</td>
            <td>This offer is valid for 45 days.</td>
        </tr> #}
        <tr>
            <td>Conditions:</td>
            <td>
                <div>Delivery according to our general conditions as lodged with the Chamber of Commerce.</div>
                <div>The delivery time mentioned is an indication only from which no rights can be derived.</div>
                <div>Payment term as agreed in Holmatro Partner approach plan.</div>
            </td>
        </tr>
        <tr>
            <td>ISO 9001:</td>
            <td>Holmatro delivers products that are produced in an organization that works in conformity with the ISO 9001 quality management system.</td>
        </tr>
    </table>
    <hr />
    <div class="company-details">
        <div class="company-details__item">
            <div>Holmatro Hydraulic Solutions B.V.</div>
            <div>Lissenveld 30 - P.O. Box 33</div>
            <div>4940 AA Raamsdonksveer</div>
            <div>The Netherlands</div>
        </div>
        <div class="company-details__item">
            <div>T +31 (0) 162 75 15 00</div>
            <div>F +31 (0) 162 75 15 99</div>
            <div>E <EMAIL></div>
            <div>I www.holmatro.com</div>
        </div>
        <div class="company-details__item">
            <div>Bank: Rabobank, account no. 31.44.80.196</div>
            <div>IBAN: ******************</div>
            <div>BIC: RABONL2U</div>
            <div>HR Breda nr. ********</div>
        </div>
    </div>
</footer>
</body>
</html>
