from __future__ import annotations

from typing import TYPE_CHECKING

from sqlalchemy.engine.row import Row

from holmatro_customer_portal.database.models import (
    Attribute,
    Configuration,
    ConfigurationData,
    ConfigurationProduct,
    Product,
)
from holmatro_customer_portal.services.configurator.configurator_enums import SyncForceAttributeId
from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


def get_configuration_product_attribute(
    db: Session, configuration: Configuration, state: ConfiguratorState, product_attribute_id: SyncForceAttributeId
) -> str | None:
    """Get the attribute of a product in configuration"""
    product_attribute: Row | None = (
        db.query(
            Attribute.content,
        )
        .join(Product, Attribute.product_id == Product.id)
        .join(ConfigurationProduct, ConfigurationProduct.product_id == Product.id)
        .where(
            ConfigurationProduct.configuration_id == configuration.id,
            ConfigurationProduct.state_id == state.value,
            Attribute.attribute_id == product_attribute_id.value,
        )
        .group_by(Attribute.content)
        .one_or_none()
    )

    if product_attribute and isinstance(product_attribute.content, str):
        return product_attribute.content

    return None


def get_configuration_data_value(db: Session, configuration: Configuration, attribute_id: str) -> int | str | None:
    """
    Retrieve the value for the given attribute_id from the configuration
    Return None if not found
    """
    configuration_data: ConfigurationData | None = (
        db.query(ConfigurationData)
        .filter(
            ConfigurationData.configuration == configuration,
            ConfigurationData.attribute_id == attribute_id,
        )
        .one_or_none()
    )
    if configuration_data:
        configuration_data_value: str | int = configuration_data.value
        return configuration_data_value
    return None


def get_product_quantities(db: Session, configuration: Configuration, state: ConfiguratorState) -> dict | None:
    """Get the quantity of the product in this configuration associated with the given state"""
    configuration_products = (
        db.query(ConfigurationProduct)
        .where(
            ConfigurationProduct.configuration_id == configuration.id,
            ConfigurationProduct.state_id == state.value,
        )
        .all()
    )

    if configuration_products:
        result = {"products": [], "total_quantity": 0}
        for configuration_product in configuration_products:
            result["products"].append(  # type: ignore
                {"product_id": configuration_product.product_id, "quantity": configuration_product.quantity}
            )
            if isinstance(configuration_product.quantity, int):
                result["total_quantity"] += configuration_product.quantity  # type: ignore
        return result

    return None


def get_configuration_products(db: Session, configuration: Configuration, state: ConfiguratorState) -> list[Product]:
    """Get the product in this configuration associated with the given state"""
    products = (
        db.query(Product)
        .join(ConfigurationProduct)
        .where(
            ConfigurationProduct.configuration_id == configuration.id,
            ConfigurationProduct.state_id == state.value,
        )
        .all()
    )
    return products
