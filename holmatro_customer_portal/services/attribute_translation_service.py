from typing import Any, Dict, List

from sqlalchemy import and_, or_
from sqlalchemy.orm import Session

from holmatro_customer_portal.database.models import Attribute, AttributeTranslation, Language
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class AttributeTranslationService:
    """Service for attribute translation queries using ORM."""

    def __init__(self, session: Session):
        self.session = session

    def get_attribute_translations(
        self, attribute_values_by_id: Dict[int, List[str]], language_code: str
    ) -> Dict[int, Dict[str, Any]]:
        """
        Efficiently retrieve both attribute metadata and value translations in a single query.

        This method combines the functionality of get_attribute_translations and
        get_attribute_value_translations to reduce database round trips.

        Args:
            attribute_values_by_id: Dictionary mapping attribute_id to list of raw values
            language_code: Language code to fetch translations for

        Returns:
            Dictionary mapping attribute IDs to comprehensive translation info:
            {
                attribute_id: {
                    "title": str | None,           # Attribute name
                    "unit": str | None,            # Attribute unit
                    "value_translations": {        # Value translations for this attribute
                        "raw_value": "translated_value",
                        ...
                    }
                }
            }
        """
        # Extract attribute IDs from the dictionary keys
        attribute_ids = list(attribute_values_by_id.keys())

        if not attribute_ids:
            return {}

        # Collect all attribute values that need translation
        all_attribute_values = []
        for values in attribute_values_by_id.values():
            all_attribute_values.extend(values)

        # Build filter conditions for the comprehensive query
        filter_conditions = [
            Language.language_code == language_code,
            Attribute.attribute_id.in_(attribute_ids),
            or_(
                AttributeTranslation.name.isnot(None),  # For attribute metadata
                and_(
                    Attribute.content.in_(all_attribute_values),
                    AttributeTranslation.attribute_value.isnot(None),  # For value translations
                ),
            ),
        ]

        # Single comprehensive query to get both metadata and value translations
        results = (
            self.session.query(
                Attribute.attribute_id,
                AttributeTranslation.name,
                AttributeTranslation.attribute_unit,
                Attribute.content,
                AttributeTranslation.attribute_value,
            )
            .join(AttributeTranslation, Attribute.translations)
            .join(Language)
            .filter(and_(*filter_conditions))
            .distinct()
            .all()
        )

        # Process results into comprehensive format
        translations: Dict[int, Dict[str, Any]] = {}

        for attribute_id, name, unit, content, attribute_value in results:
            # Initialize attribute entry if not exists
            if attribute_id not in translations:
                translations[attribute_id] = {"title": None, "unit": None, "value_translations": {}}

            # Process attribute metadata (name, unit) - only if not None
            if name is not None:
                translations[attribute_id]["title"] = name
                translations[attribute_id]["unit"] = unit

            # Process attribute value translations
            if content is not None and attribute_value is not None:
                # Only include values that were actually requested for this attribute
                if attribute_id in attribute_values_by_id and content in attribute_values_by_id[attribute_id]:
                    translations[attribute_id]["value_translations"][content] = attribute_value

        return translations
