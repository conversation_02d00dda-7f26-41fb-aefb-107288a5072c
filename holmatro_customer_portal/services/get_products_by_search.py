from collections import defaultdict

from sqlalchemy.engine import Row

from holmatro_customer_portal.database.models import User
from holmatro_customer_portal.domain.repositories.catalog_repository import CatalogRepository
from holmatro_customer_portal.mappers.product_preview_mapper import ProductPreviewMapper
from holmatro_customer_portal.schemas.response_schema import (
    ProductPreviewRes,
    ProductsRes,
    SearchFiltersReq,
    SearchParamsReq,
)
from holmatro_customer_portal.services.products.parse_products_and_properties import (
    get_db_products,
    parse_to_paginated_results,
)
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.my_hm.my_hm_interface import MyHolmatroClientInterface
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import ProductsResponse

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


def get_products_by_search_handle(
    catalog_repository: CatalogRepository,
    params: SearchParamsReq,
    filters: SearchFiltersReq | None,
    user: User,
    db: Session,
    my_hm_client: MyHolmatroClientInterface,
) -> ProductsRes:
    _logger.debug("Fetching products")
    result: ProductsResponse = catalog_repository.get_products(params, filters)

    if not result.items and not result.redirects:
        return ProductsRes(products=[], properties=result.properties)

    _logger.debug("Fetching products from database")
    products = get_db_products(result, user, db, params.category_id)

    if not products:
        _logger.error(f"Products {[product.item_no for product in result.items]} not found in the database")
        _, parsed_properties = parse_to_paginated_results([], params)
        return ProductsRes(products=[], properties=parsed_properties)

    # If category_id not requested, it will check the results
    # to find the category that contains the biggest amount of products
    if not params.category_id:
        products, category_id = _find_category_with_more_results(products)
        params.category_id = category_id

    _logger.debug("Parsing products and properties")
    parsed_products, parsed_properties = parse_to_paginated_results(products, params)

    _logger.debug("Mapping products")
    mapper = ProductPreviewMapper(db, my_hm_client)
    mapped_products: list[ProductPreviewRes] = mapper.product_preview_mapper(user, parsed_products)

    return ProductsRes(products=mapped_products, properties=parsed_properties)


def _find_category_with_more_results(products: list[Row]) -> tuple[list, int]:
    # Should return only category with more results
    products_by_categories = defaultdict(list)

    # Loop through all the products and split them in categories
    for product in products:
        products_by_categories[product.category_id].append(product)

    # Find which category has more results
    category_id, products = max(products_by_categories.items(), key=lambda x: len(x[1]))

    return products, int(category_id)
