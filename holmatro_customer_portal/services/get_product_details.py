from http import HTTPStatus
from uuid import UUID

from fastapi import HTTP<PERSON>x<PERSON>
from sqlalchemy.engine import Row

from holmatro_customer_portal.database.models import User
from holmatro_customer_portal.mappers.product_preview_mapper import ProductPreviewMapper
from holmatro_customer_portal.schemas.response_schema import ProductDetailsRes
from holmatro_customer_portal.services.utils.product_queries import get_product_details
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.my_hm.my_hm_interface import MyHolmatroClientInterface

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


def get_product_details_handle(
    product_id: UUID,
    user: User,
    db: Session,
    my_hm_client: MyHolmatroClientInterface,
) -> ProductDetailsRes:
    # Query returns a joined Product and ProductTextTranslation table with a list of products
    # It contains one or many of the same product depending on how many descriptions texts are linked to the product

    query_result: list[Row] = get_product_details(db, user, product_id)
    if not query_result:
        _logger.info(f"Database does not contain product with id {product_id}")
        raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Product not found")

    mapper = ProductPreviewMapper(db, my_hm_client)  # type: ignore
    product_details: ProductDetailsRes = mapper.product_details_mapper(user, query_result)
    return product_details
