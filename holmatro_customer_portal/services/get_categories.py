from itertools import groupby

from sqlalchemy.engine import Row

from holmatro_customer_portal.database.models import Asset, AssetResource
from holmatro_customer_portal.database.models import Category as DBCategory
from holmatro_customer_portal.database.models import CategoryAssociation, Product
from holmatro_customer_portal.services.configurator.default_attributes import DefaultAttributes
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import Category

ASSET_RESOURCE_FILE_TYPES = ["2", "B", "E"]


# Retrieve image URLs for the asset from the database, assuming the categories are subcategories of the root.
def add_image_url_to_category(category_tree: Category, cat_id: str, db: Session) -> Category:
    main_category_ids = (
        str(SyncforceCategory.INDUSTRIAL_LIFTING.value),
        str(SyncforceCategory.INDUSTRIAL_CUTTING.value),
    )
    if str(cat_id).endswith(main_category_ids):
        category_urls = query_category_asset(category_tree, db)
        categories: list = []
        for category in category_tree.children:
            if category_url := category_urls.get(str(category.category_id)[4:]):
                category.image_url = category_url
            categories.append(category)
        category_tree.children = categories
    return category_tree


def map_article_to_categories(query_result: list[Row], db: Session) -> dict:
    # Post-process the results to get a dictionary of article_number: [category_id]
    article_to_categories = {}
    article_numbers = [product.product_article_number for product in query_result]

    category_ids_query_result = query_category_ids(article_numbers, db)
    for article_number, category_id in category_ids_query_result:
        if attribute_category := DefaultAttributes.get_output_value(int(category_id)):
            article_to_categories[article_number] = attribute_category
    return article_to_categories


def query_category_ids(product_article_numbers: list[str], db: Session) -> list[Row]:
    category_ids_query_result = (
        db.query(Product.article_number, DBCategory.category_id)
        .join(Product.categories)
        .join(CategoryAssociation.category)
        .filter(Product.article_number.in_(product_article_numbers))
        .distinct()
    ).all()

    return category_ids_query_result


def query_category_asset(category_tree: Category, db: Session) -> dict:
    category_ids = [str(category.category_id)[4:] for category in category_tree.children]

    def _file_preference(resource: Row) -> int:
        """Provides an index for the AssetResource based on the first character
        This character corresponds with SyncForce file types"""
        try:
            return ASSET_RESOURCE_FILE_TYPES.index(resource.name[0])
        except ValueError:
            return len(ASSET_RESOURCE_FILE_TYPES)

    category_assets = {}

    asset_resources = (
        db.query(
            AssetResource.url.label("asset_url"),
            AssetResource.file_name.label("name"),
            DBCategory.category_id.label("cat_id"),
        )
        .join(Asset, AssetResource.asset_id == Asset.id)
        .join(DBCategory, Asset.category_id == DBCategory.id)
        .filter(DBCategory.category_id.in_(category_ids))
        .all()
    )

    for key, group in groupby(asset_resources, lambda x: x.cat_id):
        category_assets[key] = sorted(group, key=_file_preference)[0].asset_url

    return category_assets
