from uuid import UUID

from holmatro_customer_portal.database.models import User
from holmatro_customer_portal.schemas.response_schema import RelatedToRes
from holmatro_customer_portal.services.utils.product_queries import get_product_preview_by_ids
from holmatro_customer_portal.services.utils.queries import fetch_related_products
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.my_hm.my_hm_interface import MyHolmatroClientInterface


def get_related_products_with_translation(
    product_id: UUID,
    user: User,
    db: Session,
    my_hm_client: MyHolmatroClientInterface,
    relationship_ids: list[int],
    exclude_classifications: list[int] | None = None,
) -> list[RelatedToRes]:
    # Query to get the related products and their translations for a specific product_id and language_id
    related_products = fetch_related_products(db, user, product_id, relationship_ids)

    # Processing the results into a list of dictionaries for easier use
    products = {
        related_product.related_product_id: related_product.relationship_name for related_product in related_products
    }

    # Query products info
    product_previews = get_product_preview_by_ids(
        [product.related_product_id for product in related_products], user, db
    )

    # Optionally, filter out related products with certain classification ids
    if exclude_classifications:
        product_previews = [
            preview for preview in product_previews if preview.product_classification_id not in exclude_classifications
        ]

    from holmatro_customer_portal.mappers.product_preview_mapper import ProductPreviewMapper

    sorted_products = _sort_products(relationship_ids, related_products, product_previews)
    mapper = ProductPreviewMapper(db, my_hm_client)
    mapped_product = mapper.product_preview_mapper(user, sorted_products)

    return [(RelatedToRes(product=product, name=products[product.product_id])) for product in mapped_product]


def _sort_products(relationship_ids: list, related_products: list, related_product_previews: list) -> list:
    # By Holmatro request, the products should be ordered according to their relationship with the main product

    # First sort related products by their relationship id
    sorted_related_results = sorted(related_products, key=lambda res: relationship_ids.index(res.relationship_id))

    # Sorts the products according to the previous sorting results using the product_id
    return sorted(
        related_product_previews,
        key=lambda product_preview: [product.related_product_id for product in sorted_related_results].index(
            product_preview.product_id
        ),
    )
