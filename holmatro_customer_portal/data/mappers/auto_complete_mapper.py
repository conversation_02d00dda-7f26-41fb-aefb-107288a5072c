from holmatro_customer_portal.schemas.opensearch_schemas import AutocompleteResponse
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import Autocomplete, AutocompleteSuggestion, Product


class AutoCompleteMapper:

    @staticmethod
    def convert_opensearch_response_to_autocomplete(opensearch_response: AutocompleteResponse) -> Autocomplete:
        products = []
        for product_result in opensearch_response.products:
            # Use first product name, serves as reference, will be overwritten by database translations,
            product_name = product_result.product_names[0].value

            products.append(
                Product(
                    itemno=str(product_result.product_id) if product_result.product_id else "",
                    title=product_name,
                    brand="Holmatro",  # Default brand
                )
            )

        # Convert suggestions
        suggestions = [
            AutocompleteSuggestion(title=suggestion_text) for suggestion_text in opensearch_response.suggestions
        ]

        return Autocomplete(items=products, suggestions=suggestions)
