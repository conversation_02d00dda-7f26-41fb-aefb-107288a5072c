from holmatro_customer_portal.database.models import Category as DBCategory
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import Category, MainCategory, SubCategory


class CategoryMapper:
    def convert_db_category_to_schema(self, db_category: DBCategory, language_id: str) -> Category:
        """
        Convert a DBCategory to Category schema.

        This is the main entry point for converting database categories to schema objects.
        Always returns a Category (root level) with properly nested children.

        Args:
            db_category: The database category to convert
            language_id: The numeric language ID (e.g., 1000 for English)

        Returns:
            Category schema object with nested MainCategory and SubCategory children
        """
        return self._convert_db_category_recursive(db_category, language_id, parent_category_id=None, depth=0)  # type: ignore[return-value]

    def _convert_db_category_recursive(
        self, db_category: DBCategory, language_id: str, parent_category_id: str | None, depth: int
    ) -> Category | MainCategory | SubCategory:
        """
        Recursively convert a DBCategory to appropriate schema type based on depth.

        This is the internal recursive helper method that handles the depth-based type conversion.

        Args:
            db_category: The database category to convert
            language_id: The numeric language ID (e.g., 1000 for English)
            parent_category_id: The parent category ID for path construction (None for root)
            depth: Current depth level (0=Category, 1=MainCategory, 2=SubCategory)

        Returns:
            Category, MainCategory, or SubCategory depending on depth
        """
        # Create the category_id by concatenating language_id + db_category.category_id
        schema_category_id = int(f"{language_id}{db_category.category_id}")

        # Get the title from the first available translation
        title = "Unknown Category"  # Default fallback
        if (
            db_category.translations
            and len(db_category.translations) > 0
            and db_category.translations[0].name is not None
        ):
            title = db_category.translations[0].name

        # Create the category_path (only 1 level up - immediate parent only)
        if parent_category_id:
            # Include parent: "parent_id-current_id"
            category_path = f"{language_id}{parent_category_id}-{language_id}{db_category.category_id}"
        else:
            # No parent: just the current category
            category_path = f"{language_id}{db_category.category_id}"

        # Convert child categories recursively based on depth
        if depth == 0:  # Category level
            main_children: list[MainCategory] = []
            if db_category.children:
                for child_category_db in db_category.children:
                    child_category = self._convert_db_category_recursive(
                        child_category_db,
                        language_id,
                        str(db_category.category_id),  # Pass current category as parent for child
                        depth + 1,  # Next level is MainCategory
                    )
                    # Type assertion since we know depth + 1 returns MainCategory
                    assert isinstance(child_category, MainCategory)
                    main_children.append(child_category)

            return Category(
                title=title,
                category_id=schema_category_id,
                image_url=None,  # Not available in current data
                category_path=category_path,
                children=main_children,
            )

        elif depth == 1:  # MainCategory level
            sub_children: list[SubCategory] = []
            if db_category.children:
                for child_category_db in db_category.children:
                    child_category = self._convert_db_category_recursive(
                        child_category_db,
                        language_id,
                        str(db_category.category_id),  # Pass current category as parent for child
                        depth + 1,  # Next level is SubCategory
                    )
                    # Type assertion since we know depth + 1 returns SubCategory
                    assert isinstance(child_category, SubCategory)
                    sub_children.append(child_category)

            return MainCategory(
                title=title,
                category_id=schema_category_id,
                image_url=None,  # Not available in current data
                category_path=category_path,
                children=sub_children,
            )

        else:  # SubCategory level (depth >= 2)
            # SubCategory doesn't have children
            return SubCategory(
                title=title,
                category_id=schema_category_id,
                image_url=None,  # Not available in current data
                category_path=category_path,
            )
