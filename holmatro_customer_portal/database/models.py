import uuid
from datetime import datetime

from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    Enum,
    ForeignKey,
    Index,
    Integer,
    Numeric,
    SmallInteger,
    String,
    Table,
    Uuid,
    case,
)
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.ext.indexable import index_property
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func, select

from holmatro_customer_portal.database.base import Base
from holmatro_customer_portal.database.enums import (
    AssetType,
    Currency,
    DBFilterType,
    DBMultiSelectLogic,
    SystemType,
    TextType,
)
from holmatro_customer_portal.schemas.auth import UserLanguage
from holmatro_customer_portal.utils.exceptions.user_setting_exception import UserSettingException


class Language(Base):
    __tablename__ = "languages"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    language_code: Mapped[str] = mapped_column(String(50), unique=True, nullable=True)


class Brand(Base):
    __tablename__ = "brands"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    brand_name: Mapped[str | None] = mapped_column(String(255))
    brand_type: Mapped[str | None] = mapped_column(String(255))
    gbin: Mapped[str | None] = mapped_column(String(255))
    owner_name: Mapped[str | None] = mapped_column(String(255))
    owner_gln: Mapped[str | None] = mapped_column(String(255))
    reference: Mapped[str | None] = mapped_column(String(255))


class ClassificationTranslation(Base):
    __tablename__ = "classification_translations"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    name: Mapped[str | None] = mapped_column(String(255))
    language_id: Mapped[uuid.UUID | None] = mapped_column(
        Uuid,
        ForeignKey(
            "languages.id",
        ),
    )
    language: Mapped["Language"] = relationship("Language")
    classification_id: Mapped[uuid.UUID | None] = mapped_column(
        Uuid, ForeignKey("classifications.id", ondelete="CASCADE")
    )
    classification: Mapped["Classification"] = relationship("Classification", back_populates="translations")


class Classification(Base):
    __tablename__ = "classifications"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    material_type: Mapped[str | None] = mapped_column(String(255))
    classification_id: Mapped[int | None] = mapped_column(Integer)
    translations: Mapped[list["ClassificationTranslation"]] = relationship(
        "ClassificationTranslation", back_populates="classification", cascade="all, delete-orphan"
    )


class HierarchyLevelTranslation(Base):
    __tablename__ = "hierarchy_level_translations"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    translation: Mapped[str | None] = mapped_column(String(255))
    hierarchy_level_id: Mapped[uuid.UUID | None] = mapped_column(
        Uuid, ForeignKey("hierarchy_levels.id", ondelete="CASCADE")
    )
    language_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("languages.id"))
    language: Mapped["Language"] = relationship("Language")
    hierarchy_level: Mapped["HierarchyLevel"] = relationship("HierarchyLevel", back_populates="translations")


class HierarchyLevel(Base):
    __tablename__ = "hierarchy_levels"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    hierarchy_level_id: Mapped[int | None] = mapped_column(Integer)
    translations: Mapped[list["HierarchyLevelTranslation"]] = relationship(
        "HierarchyLevelTranslation", back_populates="hierarchy_level", cascade="all, delete-orphan"
    )


class ProductTextTranslation(Base):
    __tablename__ = "product_text_translations"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    label: Mapped[str | None] = mapped_column(String(255))
    value: Mapped[str | None] = mapped_column(String(2048))
    type: Mapped[TextType] = mapped_column(Enum(TextType))
    sort_order: Mapped[int | None] = mapped_column(SmallInteger, index=True)
    language_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("languages.id"))
    language: Mapped["Language"] = relationship("Language")
    product_text_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("product_texts.id", ondelete="CASCADE"))
    product_text: Mapped["ProductText"] = relationship("ProductText", back_populates="translations")


class ProductText(Base):
    __tablename__ = "product_texts"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    is_derived: Mapped[bool | None] = mapped_column(Boolean)
    translations: Mapped[list["ProductTextTranslation"]] = relationship(
        "ProductTextTranslation", back_populates="product_text", cascade="all, delete-orphan"
    )
    product_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("products.id", ondelete="CASCADE"))


class AttributeTranslation(Base):
    __tablename__ = "attribute_translations"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    name: Mapped[str | None] = mapped_column(String(255))
    language_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("languages.id"))
    language: Mapped["Language"] = relationship("Language")
    attribute_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("attributes.id", ondelete="CASCADE"))
    attribute: Mapped["Attribute"] = relationship("Attribute", back_populates="translations")
    attribute_group_name: Mapped[str | None] = mapped_column(String(255))
    attribute_value: Mapped[str | None] = mapped_column(String(255))
    attribute_unit: Mapped[str | None] = mapped_column(String(255))


class Asset(Base):
    __tablename__ = "assets"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    udai: Mapped[uuid.UUID | None] = mapped_column(Uuid)
    role_id: Mapped[int | None] = mapped_column(Integer)
    asset_id: Mapped[int | None] = mapped_column(Integer)
    asset_type: Mapped[AssetType] = mapped_column(
        Enum(AssetType), index=True, nullable=True, doc="Relation type between asset and product"
    )
    asset_type_id: Mapped[int | None] = mapped_column(Integer, doc="Asset type ID, associated with the asset only")
    asset_sub_type_id: Mapped[int | None] = mapped_column(
        Integer, doc="Asset subtype ID, associated with the asset only"
    )
    char_order: Mapped[int | None] = mapped_column(Integer)
    meta_data_translations: Mapped[list["AssetMetadataTranslations"]] = relationship(
        "AssetMetadataTranslations", back_populates="asset", cascade="all, delete-orphan"
    )
    asset_resources: Mapped[list["AssetResource"]] = relationship(
        "AssetResource", back_populates="asset", cascade="all, delete-orphan"
    )
    product_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("products.id", ondelete="CASCADE"))
    category_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("categories.id"))


class AssetMetadataTranslations(Base):
    __tablename__ = "assets_metadata_translations"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    asset_type: Mapped[str | None] = mapped_column(String(255))
    asset_sub_types_singular: Mapped[str | None] = mapped_column(String(255))
    asset_sub_types_plural: Mapped[str | None] = mapped_column(String(255))
    asset_role: Mapped[str | None] = mapped_column(String(255))
    asset_label: Mapped[str | None] = mapped_column(String(255))
    language_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("languages.id"))
    language: Mapped["Language"] = relationship("Language")

    asset_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("assets.id", ondelete="CASCADE"))
    asset: Mapped["Asset"] = relationship("Asset", back_populates="meta_data_translations")


asset_resource_language_association_table = Table(
    "asset_resource_language_association",
    Base.metadata,
    Column("asset_resource_id", Uuid, ForeignKey("asset_resources.id", ondelete="CASCADE"), primary_key=True),
    Column("language_id", Uuid, ForeignKey("languages.id"), primary_key=True),
)


class AssetResource(Base):
    __tablename__ = "asset_resources"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    url: Mapped[str | None] = mapped_column(String(255))
    asset_type: Mapped[str | None] = mapped_column(String(255))
    file_name: Mapped[str | None] = mapped_column(String(255))
    languages: Mapped[list["Language"]] = relationship("Language", secondary=asset_resource_language_association_table)
    asset_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("assets.id", ondelete="CASCADE"))
    asset: Mapped["Asset"] = relationship("Asset", back_populates="asset_resources")
    size: Mapped[float | None] = mapped_column(Numeric(precision=10, scale=2))


class Attribute(Base):
    __tablename__ = "attributes"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    type: Mapped[str | None] = mapped_column(String(255))
    is_localized: Mapped[bool | None] = mapped_column(Boolean)
    is_read_only: Mapped[bool | None] = mapped_column(Boolean)
    translations: Mapped[list["AttributeTranslation"]] = relationship(
        "AttributeTranslation", back_populates="attribute", cascade="all, delete-orphan"
    )
    attribute_id: Mapped[int | None] = mapped_column(Integer)
    attribute_group_id: Mapped[int | None] = mapped_column(Integer)
    product_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("products.id", ondelete="CASCADE"))
    content: Mapped[str | None] = mapped_column(String(255))
    unit_type: Mapped[str | None] = mapped_column(String(255))
    system_type: Mapped[SystemType] = mapped_column(Enum(SystemType), index=True, nullable=True)
    multi_lingual_values: Mapped[bool | None] = mapped_column(Boolean)


attribute_id_index = Index("attribute_id_idx", Attribute.attribute_id)


class TargetGroup(Base):
    __tablename__ = "target_groups"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    segmentation_id: Mapped[int | None] = mapped_column(Integer)
    name: Mapped[str | None] = mapped_column(String(255))
    from_date: Mapped[datetime | None] = mapped_column(DateTime)
    until_date: Mapped[datetime | None] = mapped_column(DateTime)
    product_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("products.id", ondelete="CASCADE"))


class MasterProductNameTranslation(Base):
    __tablename__ = "master_product_names"

    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    value: Mapped[str] = mapped_column(String(255), nullable=True)
    language_id = Column(Uuid, ForeignKey("languages.id"))
    language: Mapped[Language] = relationship("Language")
    product_id = Column(Uuid, ForeignKey("products.id", ondelete="CASCADE"))


class ProductNameTranslation(Base):
    __tablename__ = "product_names"

    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    value: Mapped[str] = mapped_column(String(255), nullable=True)
    language_id = Column(Uuid, ForeignKey("languages.id"))
    language: Mapped[Language] = relationship("Language")
    product_id = Column(Uuid, ForeignKey("products.id", ondelete="CASCADE"))


product_language_association_table = Table(
    "product_language_association_table",
    Base.metadata,
    Column("products_id", Uuid, ForeignKey("products.id", ondelete="CASCADE"), primary_key=True),
    Column("language_id", Uuid, ForeignKey("languages.id"), primary_key=True),
)
target_group_association_table = Table(
    "target_group_association_table",
    Base.metadata,
    Column("products_id", Uuid, ForeignKey("products.id", ondelete="CASCADE"), primary_key=True),
    Column("target_group_id", Uuid, ForeignKey("target_groups.id", ondelete="CASCADE"), primary_key=True),
)
favorite_products_association_table = Table(
    "user_favorite_products",
    Base.metadata,
    Column("user_id", Uuid, ForeignKey("users.id", ondelete="CASCADE"), primary_key=True),
    Column("product_id", Uuid, ForeignKey("products.id", ondelete="CASCADE"), primary_key=True),
    Column("created_at", DateTime, server_default=func.now()),
)


category_user_association_table = Table(
    "category_user_association_table",
    Base.metadata,
    Column("category_id", Uuid, ForeignKey("categories.id"), primary_key=True),
    Column("user_id", Uuid, ForeignKey("users.id"), primary_key=True),
)


class RelatedProduct(Base):
    __tablename__ = "related_products"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    main_product_id: Mapped[uuid.UUID] = mapped_column(
        Uuid, ForeignKey("products.id", ondelete="CASCADE"), nullable=False
    )
    related_product_id: Mapped[uuid.UUID] = mapped_column(
        Uuid, ForeignKey("products.id", ondelete="CASCADE"), nullable=False
    )
    relationship_id: Mapped[int | None] = mapped_column(Integer)
    main_product: Mapped["Product"] = relationship(
        "Product", foreign_keys=[main_product_id], back_populates="related_products"
    )
    related_product: Mapped["Product"] = relationship("Product", foreign_keys=[related_product_id])
    translations: Mapped[list["RelationshipTranslation"]] = relationship(
        "RelationshipTranslation", cascade="all, delete-orphan"
    )


class RelationshipTranslation(Base):
    __tablename__ = "relationship_translations"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    related_product_id: Mapped[uuid.UUID | None] = mapped_column(
        Uuid, ForeignKey("related_products.id", ondelete="CASCADE")
    )
    language_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("languages.id"))
    language: Mapped["Language"] = relationship("Language")
    translated_name: Mapped[str | None] = mapped_column(String(255))

    related_product: Mapped["RelatedProduct"] = relationship("RelatedProduct", back_populates="translations")


class Product(Base):
    __tablename__ = "products"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    product_id: Mapped[int] = mapped_column(Integer, nullable=False)
    umid: Mapped[uuid.UUID] = mapped_column(Uuid, nullable=False)
    part_nr = Column(String(20))
    gtin = Column(String(255))
    last_modified: Mapped[datetime | None] = mapped_column(DateTime)
    deleted_at: Mapped[datetime | None] = mapped_column(nullable=True)
    article_number: Mapped[str] = mapped_column(String(20), nullable=False)
    master_code = Column(String(20))
    brand_id = Column(Uuid, ForeignKey("brands.id"))
    classification_id = Column(Uuid, ForeignKey("classifications.id"))
    hierarchy_level_id = Column(Uuid, ForeignKey("hierarchy_levels.id"))

    target_groups = relationship("TargetGroup", secondary=target_group_association_table)
    categories: Mapped[list["CategoryAssociation"]] = relationship(
        "CategoryAssociation",
        back_populates="product",
        cascade="all, delete-orphan",
        order_by=lambda: case(
            (CategoryAssociation.product_sort_order.is_(None), 999999), else_=CategoryAssociation.product_sort_order
        ),
    )
    brand = relationship("Brand")
    languages = relationship("Language", secondary=product_language_association_table)
    classification = relationship("Classification")
    hierarchy_level = relationship("HierarchyLevel")
    product_names: Mapped[list[ProductNameTranslation]] = relationship(
        "ProductNameTranslation", cascade="all, delete-orphan"
    )
    master_product_names: Mapped[list[MasterProductNameTranslation]] = relationship(
        "MasterProductNameTranslation", cascade="all, delete-orphan"
    )
    assets = relationship("Asset", cascade="all, delete-orphan")
    product_texts = relationship("ProductText", cascade="all, delete-orphan")
    attributes = relationship("Attribute", cascade="all, delete-orphan")
    related_products = relationship(
        "RelatedProduct",
        foreign_keys="[RelatedProduct.main_product_id]",
        back_populates="main_product",
        cascade="all, delete-orphan",
    )

    dirty = Column(
        Boolean, nullable=False, default=True, comment="Indicates if a product has been updated by the data import"
    )

    __table_args__ = (Index("ix_product_dirty_deleted_at", "dirty", "deleted_at"),)


class User(Base):
    __tablename__ = "users"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    email: Mapped[str] = mapped_column(String(255), nullable=False, unique=True, index=True)
    name: Mapped[str | None] = mapped_column(String(255))
    last_name: Mapped[str | None] = mapped_column(String(255))
    currency: Mapped[Currency | None] = mapped_column(Enum(Currency))
    country: Mapped[str | None] = mapped_column(String(255))
    relation_name: Mapped[str | None] = mapped_column(String(255))
    sf_proposition: Mapped[str | None] = mapped_column(String(255))
    main_language_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("languages.id"))
    main_language: Mapped["Language"] = relationship("Language", foreign_keys=[main_language_id])
    preferred_language_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("languages.id"), nullable=True)
    preferred_language: Mapped["Language"] = relationship("Language", foreign_keys=[preferred_language_id])
    favorite_products: Mapped[list["Product"]] = relationship("Product", secondary=favorite_products_association_table)
    configurations: Mapped[list["Configuration"]] = relationship(
        "Configuration", back_populates="user", cascade="all, delete-orphan"
    )
    categories: Mapped[list["Category"]] = relationship("Category", secondary=category_user_association_table)
    anonymous: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    preferred_system_type: Mapped[SystemType | None] = mapped_column(Enum(SystemType), nullable=True)

    @hybrid_property
    def language(self) -> UserLanguage:
        language_id = self.preferred_language_id if self.preferred_language else self.main_language_id
        language_code = (
            self.preferred_language.language_code if self.preferred_language else self.main_language.language_code
        )
        return UserLanguage(id=language_id, code=language_code)

    @language.setter  # type: ignore[no-redef]
    def language(self, value: Language):
        if self.system_type == SystemType.IMPERIAL and value.language_code != "en":
            raise UserSettingException("Imperial system is only available in combination English language")
        self.preferred_language = value

    @hybrid_property
    def system_type(self) -> SystemType:
        if self.preferred_system_type:
            return self.preferred_system_type
        return SystemType.IMPERIAL if self.country in ("US", "CA") else SystemType.METRIC

    @system_type.setter  # type: ignore[no-redef]
    def system_type(self, value):
        if value == SystemType.IMPERIAL and self.language.code != "en":
            raise UserSettingException("Imperial system is only available in combination English language")
        self.preferred_system_type = value


class Category(Base):
    __tablename__ = "categories"

    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    umid: Mapped[str | None] = mapped_column(String(50), unique=True)
    sort_index: Mapped[int] = mapped_column(Integer)
    parent_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("categories.id"))
    category_umid: Mapped[uuid.UUID | None] = mapped_column(Uuid)
    category_id: Mapped[str] = mapped_column(String(255), unique=True)
    assets: Mapped[list["Asset"]] = relationship("Asset", cascade="all, delete-orphan")
    translations: Mapped[list["CategoryTranslation"]] = relationship(
        "CategoryTranslation", back_populates="category", cascade="all, delete-orphan"
    )
    children = relationship("Category", foreign_keys="Category.parent_id")
    parent = relationship("Category", remote_side=[id], overlaps="children")
    filter_templates: Mapped[list["DBFilterTemplate"]] = relationship(
        "DBFilterTemplate", secondary="category_filter_template_association_table", back_populates="categories"
    )


class CategoryAssociation(Base):
    __tablename__ = "category_association_table"

    category_id: Mapped[uuid.UUID] = mapped_column(Uuid, ForeignKey("categories.id"), primary_key=True)
    product_id: Mapped[uuid.UUID] = mapped_column(Uuid, ForeignKey("products.id"), primary_key=True)
    product_sort_order: Mapped[int | None] = mapped_column(Integer, nullable=True)

    category: Mapped[Category] = relationship(Category)
    product: Mapped[Product] = relationship(Product)


class CategoryTranslation(Base):
    __tablename__ = "category_translations"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, primary_key=True, default=uuid.uuid4)
    name: Mapped[str | None] = mapped_column(String(255))
    language_id: Mapped[uuid.UUID | None] = mapped_column(
        Uuid, ForeignKey("languages.id")
    )  # Ensure 'languages.id' is of type UUID
    language: Mapped["Language"] = relationship("Language")
    category_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("categories.id"))
    category: Mapped["Category"] = relationship("Category", back_populates="translations")


class ConfigurationData(Base):
    __tablename__ = "configuration_data"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    configuration_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("configurations.id"))
    configuration: Mapped["Configuration"] = relationship("Configuration", back_populates="data")

    state_id: Mapped[str] = mapped_column(String(255), nullable=False)
    attribute_id: Mapped[str] = mapped_column(String(255), nullable=False)
    data: Mapped[dict | list | None] = mapped_column(JSON)
    value = index_property("data", "value")

    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now(), onupdate=func.now(), index=True)

    Index("configuration_id_attribute_id_idx", configuration_id, attribute_id, unique=True)


class ConfigurationProduct(Base):
    __tablename__ = "configuration_products"
    configuration_id: Mapped[uuid.UUID] = mapped_column(
        Uuid, ForeignKey("configurations.id", ondelete="CASCADE"), primary_key=True
    )
    product_id: Mapped[uuid.UUID] = mapped_column(Uuid, ForeignKey("products.id", ondelete="CASCADE"), primary_key=True)
    state_id: Mapped[str] = mapped_column(String(255), nullable=False)
    quantity: Mapped[int] = mapped_column(SmallInteger, nullable=False)
    position: Mapped[int] = mapped_column(SmallInteger, nullable=False)

    configuration: Mapped["Configuration"] = relationship("Configuration", back_populates="products")
    product: Mapped["Product"] = relationship("Product")


class Configuration(Base):
    __tablename__ = "configurations"
    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    version: Mapped[str] = mapped_column(String(5), default="v1", nullable=False, comment="Configurator version")
    user_id: Mapped[uuid.UUID] = mapped_column(Uuid, ForeignKey("users.id"), nullable=False)
    user: Mapped["User"] = relationship("User", back_populates="configurations")

    data: Mapped[list["ConfigurationData"]] = relationship("ConfigurationData", cascade="all, delete-orphan")

    reference: Mapped[str | None] = mapped_column(String(255), comment="User reference for this configuration")
    quotation_number: Mapped[str | None] = mapped_column(
        String(255), comment="Quotation number generated in the portal"
    )
    last_updated_state: Mapped[str | None] = mapped_column(String(255), comment="ID of the state last saved")
    last_transition: Mapped[str | None] = mapped_column(String(255), comment="The transition requested")
    started_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())

    @hybrid_property
    def updated_at(self) -> datetime | None:
        if len(self.data) == 0 or any([d.updated_at is None for d in self.data]):
            return None
        return sorted(self.data, key=lambda x: x.updated_at)[-1].updated_at

    @updated_at.expression  # type: ignore
    def updated_at(cls) -> datetime | None:
        subquery = (
            select(func.max(ConfigurationData.updated_at))
            .where(ConfigurationData.configuration_id == cls.id)
            .correlate(cls)
            .as_scalar()
        )
        return subquery

    finished_at: Mapped[datetime | None] = mapped_column(DateTime)
    hidden: Mapped[bool] = mapped_column(Boolean, default=False)
    products: Mapped[list["ConfigurationProduct"]] = relationship(
        "ConfigurationProduct", back_populates="configuration", order_by=ConfigurationProduct.position
    )
    category_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("categories.id"))
    category: Mapped["Category"] = relationship("Category")


class DBFilterTemplate(Base):
    __tablename__ = "filter_templates"

    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    name: Mapped[str] = mapped_column(String(255))
    system_type: Mapped[SystemType | None] = mapped_column(Enum(SystemType), index=True)
    categories: Mapped[list["Category"]] = relationship(
        "Category",
        secondary="category_filter_template_association_table",
        back_populates="filter_templates",
    )
    filters: Mapped[list["DBFilter"]] = relationship(
        "DBFilter", back_populates="filter_template", order_by="DBFilter.position"
    )


class DBCategoryFilterTemplateAssociation(Base):
    __tablename__ = "category_filter_template_association_table"

    category_id: Mapped[uuid.UUID] = mapped_column(
        Uuid, ForeignKey("categories.id", ondelete="CASCADE"), primary_key=True
    )
    filter_template_id: Mapped[uuid.UUID] = mapped_column(
        Uuid, ForeignKey("filter_templates.id", ondelete="CASCADE"), primary_key=True
    )


class DBFilter(Base):
    __tablename__ = "filters"

    id: Mapped[uuid.UUID] = mapped_column(Uuid, default=uuid.uuid4, primary_key=True)
    attribute_id: Mapped[int] = mapped_column(Integer, comment="SyncForce attribute ID", index=True)
    filter_type: Mapped[DBFilterType] = mapped_column(Enum(DBFilterType))
    position: Mapped[int] = mapped_column(Integer, comment="Position of this filter in the template", index=True)
    multi_select_logic: Mapped[DBMultiSelectLogic | None] = mapped_column(Enum(DBMultiSelectLogic))
    is_visible: Mapped[bool] = mapped_column(Boolean, default=True)
    nr_of_shown_values: Mapped[int] = mapped_column(Integer, default=5)
    show_nr_of_results: Mapped[bool] = mapped_column(Boolean, default=False)
    filter_template_id: Mapped[uuid.UUID | None] = mapped_column(Uuid, ForeignKey("filter_templates.id"))
    filter_template: Mapped["DBFilterTemplate"] = relationship("DBFilterTemplate", back_populates="filters")
