from enum import Enum, StrEnum, auto


class SystemType(Enum):
    METRIC = "Metric"
    IMPERIAL = "Imperial"


class AssetType(Enum):
    PRODUCT_MAIN_APPLICATION_SHOT = "PRODUCT_MAIN_APPLICATION_SHOT"
    PRODUCT_APPLICATION_SHOT = "PRODUCT_APPLICATION_SHOT"
    PRODUCT_APPLICATION_SHOTS_WEB = "PRODUCT_APPLICATION_SHOTS_WEB"
    PRODUCT_SHOTS = "PRODUCT_SHOTS"
    PRODUCT_GUIDE = "PRODUCT_GUIDE"
    PRODUCT_RELATED_PICTURES = "PRODUCT_RELATED_PICTURES"
    PRODUCT_CHARACTERISTICS = "PRODUCT_CHARACTERISTICS"
    PRODUCT_DIAGRAM = "PRODUCT_DIAGRAM"
    PRODUCT_TECHNICAL_DRAWING = "PRODUCT_TECHNICAL_DRAWING"
    PRODUCT_TECHNICAL_SPECIFICATION_SHEET = "PRODUCT_TECHNICAL_SPECIFICATION_SHEET"
    PRODUCT_MAIN_IMAGE = "PRODUCT_MAIN_IMAGE"
    PRODUCT_CATEGORY_MAIN_IMAGE = "PRODUCT_CATEGORY_MAIN_IMAGE"


class AssetTypeId(Enum):
    DOCUMENT = 8


class AssetSubTypeId(Enum):
    SERVICE_MANUAL = 61
    TECHNICAL_SPECIFICATION_SHEET = 35


class ExternalLinks(Enum):
    Centix = "Centix"
    SyncForce = "SyncForce"
    CatalogCreator = "CatalogCreator"


class Currency(Enum):
    USD = "USD"
    EUR = "EUR"


class TextType(Enum):
    long_description = "long_description"
    features = "features"
    supplied_with = "supplied_with"
    available_on_request = "available_on_request"
    accessories = "accessories"
    additional_information = "additional_information"
    other = "other"


class LanguageEnum(Enum):
    ENGLISH = "en"
    SPANISH = "es"
    FRENCH = "fr"
    GERMAN = "de"
    CHINESE = "zh"
    DUTCH = "nl"
    POLISH = "pl"
    PORTUGUESE = "pt"


class RelationShipTypeOrder(Enum):
    RECOMMENDED_ACCESSORIES = 1015
    ALTERNATIVE_PRODUCTS = 1062
    ACCESSORIES = 1012
    SERVICESETS = 1014
    REQUIRED_ACCESSORIES = 1013
    COMBINED_WITH = 1061


class RelationshipType(Enum):
    # The order of this list is defined by Holmatro
    # It is used to return products in a specific order
    ALTERNATIVE_PRODUCTS = [RelationShipTypeOrder.ALTERNATIVE_PRODUCTS.value]
    COMBINED_WITH = [
        RelationShipTypeOrder.REQUIRED_ACCESSORIES.value,
        RelationShipTypeOrder.COMBINED_WITH.value,
        RelationShipTypeOrder.RECOMMENDED_ACCESSORIES.value,
        RelationShipTypeOrder.SERVICESETS.value,
        RelationShipTypeOrder.ACCESSORIES.value,
    ]
    CONFIGURATOR_ACCESSORIES = [
        RelationShipTypeOrder.REQUIRED_ACCESSORIES.value,
        RelationShipTypeOrder.RECOMMENDED_ACCESSORIES.value,
        RelationShipTypeOrder.SERVICESETS.value,
        RelationShipTypeOrder.ACCESSORIES.value,
    ]


class RemovedItems(Enum):
    ATTRIBUTES = [1345, 1171, 573]


class DBFilterType(StrEnum):
    CHECKBOX = auto()
    SLIDER = auto()


class DBMultiSelectLogic(StrEnum):
    AND = auto()
    OR = auto()
