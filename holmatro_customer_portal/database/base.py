# 3rd party imports
from typing import Any, Dict, Type

from sqlalchemy.orm import declarative_base
from sqlalchemy.schema import MetaData

meta = MetaData(
    naming_convention={
        "ix": "ix_%(column_0_label)s",
        "uq": "uq_%(table_name)s_%(column_0_name)s",
        "ck": "ck_%(table_name)s_%(constraint_name)s",
        "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
        "pk": "pk_%(table_name)s",
    }
)

_BaseModel = declarative_base(metadata=meta)
BaseModelType: Type = _BaseModel  # Define a type alias for _BaseModel


class Base(BaseModelType):
    __abstract__ = True

    def __init__(self, **kwargs: Any) -> None:
        """
        Default values that are set in table definitions will only become
        populated after the session is flush()'ed by default. This code
        will try to initialize the default values on initialization instead.

        Will only work on attributes that span a single column and on
        non-callables, so e.g. a created_at field will remain None until
        after the session is flush()'ed.
        """
        for attr in self.__mapper__.column_attrs:
            if attr.key in kwargs:
                continue

            if len(attr.columns) > 1:
                continue
            col = attr.columns[0]

            if col.default and not callable(col.default.arg):
                kwargs[attr.key] = col.default.arg

        super(Base, self).__init__(**kwargs)

    def to_dict(self) -> Dict:
        return {field.name: getattr(self, field.name) for field in self.__table__.c}
