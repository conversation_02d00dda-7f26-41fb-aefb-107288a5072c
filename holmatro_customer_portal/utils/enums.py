from enum import Enum

language_values = {"EN": 1000, "NL": 2000, "DE": 3000, "FR": 4000, "PL": 5000, "PT": 6000, "ES": 7000, "ZH": 8000}


# The order of the main categories follows the proposition
class SyncforceCategory(Enum):
    INDUSTRIAL = 8604
    INDUSTRIAL_LIFTING = 7411
    INDUSTRIAL_CUTTING = 2838
    INDUSTRIAL_ACCESSORIES = 6633
    CUTTERS = 6921
    CUTTER_HOSES = 9050
    CUTTER_PUMPS = 7474
    CYLINDERS = 5121
    CYLINDER_WEDGES = 1058
    CYLINDER_TOE_JACKS = 3836
    HYDRAULIC_PUMPS = 1017
    HAND_AND_FOOT_PUMPS = 3118
    COMPACT_PUMPS = 3963
    VARI_PUMPS = 826
    LIFTING_HOSES = 2371
    FLOW_PANELS = 2587
    CUTTING_ACCESSORIES = 831


class SelectionType(Enum):
    SLIDER = "slider"
    CHECKBOX = "checkbox"


def get_language_code_from_id(language_id: int) -> str | None:
    """
    Convert numeric language ID to language code using the language_values mapping.

    Args:
        language_id: Numeric language ID (e.g., 2000)

    Returns:
        Language code (e.g., "NL") or None if not found
    """
    # Reverse lookup in language_values dictionary
    for code, numeric_id in language_values.items():
        if numeric_id == language_id:
            return code
    return None
