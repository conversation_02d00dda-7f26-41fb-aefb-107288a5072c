from __future__ import annotations

from pydantic import BaseModel, ConfigDict, Field


class ResponseProperties(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    no_of_items: int = Field(validation_alias="nrofitems")
    page_size: int = Field(validation_alias="pagesize")
    no_of_pages: int = Field(validation_alias="nrofpages")
    current_page: int = Field(validation_alias="currentpage")
    category_id: int | None = Field(validation_alias="selectedcategory")


class Product(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    item_no: str = Field(alias="itemno")
    title: str
    brand: str


class ProductDetails(Product):
    image: str | None
    url: str | None
    attributes: list


class Redirect(BaseModel):
    article_number: str = Field(
        validation_alias="url",
        description="The url exists when there is a Tweakwise redirect rule for the search query. It contains the article number of the product that should be included in the response",
    )


class ProductsResponse(BaseModel):
    items: list[Product]
    properties: ResponseProperties
    redirects: list[Redirect]


class Attributes(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    key: str = Field(validation_alias="Key")
    values: list = Field(validation_alias="Values")


class ProductAttributes(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    article_number: str = Field(validation_alias="ArticleNumber")
    name: str = Field(validation_alias="Name")
    brand: str = Field(validation_alias="Brand")
    categories: list = Field(validation_alias="Categories")
    attributes: list[Attributes] = Field(validation_alias="Attributes")


class SubCategory(BaseModel):
    model_config = ConfigDict(populate_by_name=True)
    image_url: str | None = None
    title: str
    category_id: int = Field(validation_alias="categoryid")
    category_path: str = Field(validation_alias="categorypath")


class MainCategory(BaseModel):
    model_config = ConfigDict(populate_by_name=True)
    image_url: str | None = None
    title: str
    category_id: int = Field(validation_alias="categoryid")
    category_path: str = Field(validation_alias="categorypath")
    children: list[SubCategory]


class Category(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    title: str
    category_id: int = Field(validation_alias="categoryid")
    image_url: str | None = None
    category_path: str = Field(validation_alias="categorypath")
    children: list[MainCategory]


class FacetSettings(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    facet_id: int = Field(validation_alias="facetid")
    is_visible: bool | None = Field(validation_alias="isvisible")
    attribute_name: str = Field(validation_alias="attributename")
    url_key: str = Field(validation_alias="urlkey")
    title: str = Field(validation_alias="title")
    is_collapsible: bool = Field(validation_alias="iscollapsible")
    is_collapsed: bool = Field(validation_alias="iscollapsed")
    no_of_shown_attributes: int = Field(validation_alias="nrofshownattributes")
    expand_text: str = Field(validation_alias="expandtext")
    collapse_text: str = Field(validation_alias="collapsetext")
    is_multi_select: bool = Field(validation_alias="ismultiselect")
    multi_select_logic: str | None = Field(validation_alias="multiselectlogic")
    selection_type: str = Field(validation_alias="selectiontype")
    no_of_columns: int = Field(validation_alias="nrofcolumns")
    is_no_of_results_visible: bool = Field(validation_alias="isnrofresultsvisible")
    is_info_visible: bool = Field(validation_alias="isinfovisible")
    info_text: str | None = Field(validation_alias="infotext")
    contains_click_points: bool = Field(validation_alias="containsclickpoints")
    contains_buckets: bool = Field(validation_alias="containsbuckets")
    source: str = Field(validation_alias="source")
    prefix: str | None = Field(validation_alias="prefix")
    postfix: str | None = Field(validation_alias="postfix")
    css_class: str | None = Field(validation_alias="cssclass")


class FacetsAttributes(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    title: str
    is_selected: bool = Field(validation_alias="isselected")
    no_of_results: int = Field(validation_alias="nrofresults")
    attribute_id: int | None = Field(validation_alias="attributeid")
    url: str | None


class Filters(BaseModel):
    facets: list[Facets | Filter]


class FilterSettings(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    facet_id: int
    selection_type: str
    url_key: str
    is_collapsed: bool
    title: str
    is_no_of_results_visible: bool


class FilterAttribute(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    title: str
    display_name: str
    is_selected: bool
    no_of_results: int | None


class Filter(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    facet_settings: FilterSettings = Field(validation_alias="facet_settings")
    attributes: list[FilterAttribute]


class Facets(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    facet_settings: FacetSettings = Field(validation_alias="facetsettings")
    attributes: list[FacetsAttributes]


class Navigation(Filters):
    items: list[Product]
    properties: ResponseProperties
    redirects: list[Redirect]


class Template(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    template_id: int = Field(validation_alias="FilterTemplateId")
    name: str = Field(validation_alias="Name")


class TemplateAttribute(Template):
    attribute_id: int = Field(alias="AttributeId")


class AutocompleteSuggestion(BaseModel):
    title: str


class Autocomplete(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    items: list[Product]
    suggestions: list[AutocompleteSuggestion]


class RequestParams(BaseModel):
    tn_q: str | None = Field(default=None, description="Refers to the search query")
    tn_cid: str | None = Field(default=None, description="Refers to the category_path")
    tn_p: int = Field(default=1, description="Refers to the page number")
    tn_ps: int = Field(default=12, description="Refers to the quantity of products per page")
    tn_ft: int | None = Field(default=None, description="Refers to the filter template id")
