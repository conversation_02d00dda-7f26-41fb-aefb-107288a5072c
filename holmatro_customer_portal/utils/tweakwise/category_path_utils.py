from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.tweakwise.tweakwise_category_path import _logger, get_tweakwise_category
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import Category, MainCategory, SubCategory


def find_category_path_in_tree(tree: Category, language: str, target_category: SyncforceCategory) -> str | None:
    """
    Find the category path for a target category within a category tree.

    This utility function extracts the common logic used by both TweakwiseClient
    and HolmatroCatalogRepository to find category paths.

    Args:
        tree: The root category tree to search in
        language: Language code (e.g., "en", "nl")
        target_category: The SyncforceCategory to find

    Returns:
        The category path if found, otherwise None
    """

    def map_categories(category: Category | MainCategory, children: dict) -> dict:
        """Recursively run through the tree and map all categories to a dictionary with category_id as key"""
        for c in category.children:
            children[c.category_id] = c
            if isinstance(c, MainCategory):
                map_categories(c, children)
        return children

    # Map all categories in the tree to a lookup dictionary
    categories: dict[int, MainCategory | SubCategory] = map_categories(tree, {})

    # Generate the lookup ID for the target category
    lookup_id = get_tweakwise_category(language, target_category)

    # Return the category path if found, otherwise None
    if lookup_id in categories:
        return categories[lookup_id].category_path

    _logger.debug(f"Category path for {target_category}, language: {language} not found")

    return None
