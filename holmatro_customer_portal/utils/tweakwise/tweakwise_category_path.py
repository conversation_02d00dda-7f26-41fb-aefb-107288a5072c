from holmatro_customer_portal.utils.enums import SyncforceCategory, language_values
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


def get_tweakwise_category(language_code: str, category: SyncforceCategory) -> int:
    _logger.debug(f"Getting category ID for category {category}, language: {language_code}")

    language_tweakwise_id = language_values[language_code.upper()]
    category_path = int(f"{language_tweakwise_id}{category.value}")

    return category_path
