from http import HTTPStatus
from typing import Any, Optional

import requests
from fastapi import HTT<PERSON>Exception
from requests import Response

from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.tweakwise.category_path_utils import find_category_path_in_tree
from holmatro_customer_portal.utils.tweakwise.tweakwise_category_path import get_tweakwise_category
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import (
    Autocomplete,
    Category,
    FacetsAttributes,
    MainCategory,
    Navigation,
    ProductAttributes,
    ProductsResponse,
    SubCategory,
    Template,
    TemplateAttribute,
)

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class TweakwiseClient:
    def __init__(self) -> None:
        self.session = requests.Session()
        self.frontend_config = self.get_tweakwise_frontend_config()
        self.backend_config = self.get_tweakwise_backend_config()

    @staticmethod
    def get_tweakwise_backend_config() -> dict:
        backend_config = {
            "base_url": "https://navigator-api.tweakwise.com",
            "headers": {
                "TWN-InstanceKey": Env.TWEAKWISE_INSTANCE_KEY.get(),
                "TWN-Authentication": Env.TWEAKWISE_TOKEN.get(),
                "Accept": "application/json",
            },
        }
        return backend_config

    @staticmethod
    def get_tweakwise_frontend_config() -> dict:
        frontend_config = {
            "base_url": "https://gateway.tweakwisenavigator.com",
            "instance_key": Env.TWEAKWISE_INSTANCE_KEY.get(),
            "headers": {"Accept": "application/json"},
        }
        return frontend_config

    def request_frontend_api(
        self, method: str, endpoint: str, data_id: Optional[str] = None, **kwargs: Any
    ) -> Response:
        base_url = f"{self.frontend_config.get('base_url')}/{endpoint}/{self.frontend_config.get('instance_key')}"
        url = f"{base_url}/{data_id}" if data_id else base_url
        response: Response = self.session.request(method, url, headers=self.frontend_config.get("headers"), **kwargs)
        if response.status_code == HTTPStatus.UNAUTHORIZED:
            response.raise_for_status()

        return response

    def request_backend_api(self, method: str, endpoint: str, **kwargs: Any) -> Response:
        url = f"{self.backend_config.get('base_url')}/{endpoint}"
        response: Response = self.session.request(method, url, headers=self.backend_config.get("headers"), **kwargs)
        if response.status_code == HTTPStatus.UNAUTHORIZED:
            response.raise_for_status()

        return response

    def get_products(self, params: Optional[dict] = None) -> ProductsResponse:
        response = self.request_frontend_api("GET", "products", params=params)

        return ProductsResponse(**response.json())

    def get_category_tree(self, category_id: int) -> Category:
        response = self.request_frontend_api("GET", "categorytree", params={"catid": category_id})
        category_tree = response.json().get("categories")
        if not category_tree:
            raise HTTPException(status_code=HTTPStatus.NOT_FOUND)

        # It returns [0] because it is the main category. The other categories are accessible from it
        return Category(**category_tree[0])

    def get_navigation(self, params: Optional[dict] = None) -> Navigation:
        response = self.request_frontend_api("GET", "navigation", params=params)

        return Navigation(**response.json())

    def get_products_by_search(self, params: dict) -> Navigation:
        # ONLY USED in Unit Test.
        response = self.request_frontend_api("GET", "navigation-search", params=params)

        return Navigation(**response.json())

    def get_product_details(self, product_id: str) -> ProductAttributes:
        # ONLY USED in Unit Test.
        response = self.request_backend_api("GET", f"product/{product_id}")

        if response.status_code == HTTPStatus.NOT_FOUND:
            raise HTTPException(status_code=HTTPStatus.NOT_FOUND, detail="Product not found")

        product_details = response.json()["Results"]
        return ProductAttributes(**product_details)

    def get_templates(self) -> list[Template]:
        # ONLY USED in Unit Test.
        response = self.request_backend_api("GET", "filtertemplate")

        return [Template(**template) for template in response.json()]

    def get_template_attributes(self, template_id: int) -> list[TemplateAttribute]:
        # ONLY USED in Unit Test.
        response = self.request_backend_api("GET", f"filtertemplate/{template_id}/attribute")

        return [TemplateAttribute(**attribute) for attribute in response.json()]

    def get_autocomplete(self, params: dict) -> Autocomplete:
        params["tn_instant"] = True
        response = self.request_frontend_api("GET", "autocomplete", params=params)

        return Autocomplete(**response.json())

    def get_category_path(
        self, language: str, category: SyncforceCategory, parent_category: SyncforceCategory
    ) -> str | None:
        """Lookup the current Tweakwise category path filter for a given Category"""
        _logger.debug(f"Getting category path for {category}, language: {language}")

        parent_category_path = get_tweakwise_category(language, parent_category)
        tree = self.get_category_tree(parent_category_path)

        return find_category_path_in_tree(tree, language, category)

    def get_facet_attributes(
        self, urlkey: str, category: SyncforceCategory, parent_category: SyncforceCategory
    ) -> list[FacetsAttributes] | None:
        """
        Get attributes of a specific facet by urlkey.
        The category is mandatory because then Tweakwise understands where it needs to search for the facet.
        """
        en_category_path = self.get_category_path("en", category, parent_category)
        response = self.request_frontend_api("GET", f"facets/{urlkey}/attributes", params={"tn_cid": en_category_path})

        if response.status_code != 200:
            _logger.error(f"An error ocurred: Status Code {response.status_code} - {response.text}")
            return None
        return [FacetsAttributes(**a) for a in response.json()["attributes"]]
