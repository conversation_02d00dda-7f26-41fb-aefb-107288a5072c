import re

from holmatro_customer_portal.database.models import User
from holmatro_customer_portal.utils.enums import SyncforceCategory, language_values


def validate_user_category_id(user: User, category_id: int) -> int | None:
    user_categories = [cat.category_id for cat in user.categories]

    if str(category_id) not in user_categories:
        return None

    return category_id


def retrieve_category_path_from_category_id(user: User, category_id: int) -> str:
    language_tweakwise_id = language_values[user.language.code.upper()]
    return f"{language_tweakwise_id}{category_id}"


def validate_category_path(
    user: User,
    category_path: str,
) -> str | None:
    category_chunks = category_path.split("-")

    # Checks if the first part of the category path is in Industrial
    if not re.match(r"^[1-9]000.*$", category_chunks[0]):
        return None

    # If category path starts with Industrial, validate second part of the category_path to match with user's category
    if category_chunks[0].endswith(str(SyncforceCategory.INDUSTRIAL.value)):
        main_category = category_chunks[1]
    else:
        main_category = category_chunks[0]

    # Get the last 4 numbers of the first part of a category, which should match with the category linked to the user
    user_categories = [cat.category_id for cat in user.categories]
    if main_category[4:] not in user_categories:
        return None

    return category_path


def validate_category_id(
    category_id: int,
) -> int | None:
    # Checks if it is in Industrial
    if not re.match(r"^[1-9]000\d{2,4}$", str(category_id)):
        return None

    return category_id
