from typing import Any, Dict, List, Optional, TypedDict

from holmatro_customer_portal.schemas.opensearch_schemas import FilterConfig


class NestedFieldConfig(TypedDict):
    """Type definition for nested field configuration."""

    path: str
    field: str
    boost: int


class OpenSearchQueryBuilder:

    def __init__(self, fuzzy_enabled: bool) -> None:
        self.fuzzy_enabled = fuzzy_enabled

    def build(
        self,
        search_query: Optional[str] = None,
        category_id: Optional[int] = None,
        filters: Optional[List[FilterConfig]] = None,
        page_number: int = 1,
        page_size: int = 12,
        build_aggregations: bool = False,
    ) -> dict:

        query_part = self._build_search_query(search_query, category_id)
        aggregations = self._build_aggregations() if build_aggregations else None
        from_index = (page_number - 1) * page_size

        body = {
            "query": query_part,
            "from": from_index,
            "size": page_size,
            "sort": [
                {"_score": {"order": "desc"}},
            ],
        }
        if aggregations:
            body["aggs"] = aggregations

        if filters:  # Use filters for post_filter
            body["post_filter"] = self._build_filter_clauses(filters)

        return body

    @staticmethod
    def _build_aggregations() -> Dict[str, Any]:
        """
        Build OpenSearch aggregations to retrieve all possible filterable attributes
        and their values for the current search context.

        Groups by attribute_id first to ensure all unique attribute IDs are captured,
        even when multiple attributes share the same name.
        """
        return {
            "filterable_attributes": {
                "nested": {"path": "filterable_attributes"},
                "aggs": {
                    "attribute_ids": {
                        "terms": {"field": "filterable_attributes.attribute_id", "size": 100},
                        "aggs": {
                            "attribute_name": {
                                "terms": {"field": "filterable_attributes.attribute_name", "size": 1},
                            },
                            "attribute_type": {
                                "terms": {"field": "filterable_attributes.attribute_type", "size": 1},
                            },
                            "values_string": {
                                "terms": {"field": "filterable_attributes.value_string", "size": 30},
                            },
                            # Numeric values can be used for both range and checkbox filters, which one it will be is
                            # unknown at this point, so we include them both in the aggregations. The stats aggregation
                            # will provide min/max values for range filters, while the terms aggregation will provide
                            # unique values for checkbox filters. We shouldn't expect more than 10 unique numeric values
                            # for checkbox filters, so we limit the terms aggregation to 10.
                            "values_numeric_stats": {
                                "stats": {"field": "filterable_attributes.value_numeric"},
                            },
                            "values_numeric_terms": {
                                "terms": {"field": "filterable_attributes.value_numeric", "size": 10},
                            },
                        },
                    }
                },
            }
        }

    @staticmethod
    def build_attribute_aggregation(attribute_id: int, category_id: Optional[int] = None) -> Dict[str, Any]:
        # Build the aggregations part
        aggregations = {
            "filterable_attributes": {
                "nested": {"path": "filterable_attributes"},
                "aggs": {
                    "filter_by_attribute_id": {
                        "filter": {"term": {"filterable_attributes.attribute_id": attribute_id}},
                        "aggs": {
                            "values_string": {"terms": {"field": "filterable_attributes.value_string", "size": 30}},
                            "values_numeric_stats": {"stats": {"field": "filterable_attributes.value_numeric"}},
                        },
                    }
                },
            }
        }

        # Build the complete query structure
        query_structure = {"size": 0, "aggs": aggregations}  # No document results, only aggregations

        # Add category filter if provided
        if category_id is not None:
            query_structure["query"] = {"term": {"category_ids": category_id}}
        else:
            query_structure["query"] = {"match_all": {}}

        return query_structure

    @staticmethod
    def _create_nested_phrase_query(path: str, field: str, search_query: str, boost: int) -> Dict[str, Any]:
        """Create a nested phrase query for exact phrase matching."""
        return {"nested": {"path": path, "query": {"match_phrase": {field: {"query": search_query}}}, "boost": boost}}

    @staticmethod
    def _create_nested_fuzzy_query(path: str, field: str, search_query: str, boost: int) -> Dict[str, Any]:
        """Create a nested fuzzy query for typo correction."""
        return {
            "nested": {
                "path": path,
                "query": {"match": {field: {"query": search_query, "fuzziness": "AUTO"}}},
                "boost": boost,
            }
        }

    @staticmethod
    def _build_filter_clauses(filters: List[FilterConfig]) -> Dict[str, Any]:
        """
        Build OpenSearch filter clauses from a list of FilterConfig objects.
        """
        filter_must_clauses: list[Dict[str, Any]] = []
        for filter_config in filters:
            # Multi-select checkbox filter (OR statement)
            if filter_config.values:
                should_terms = []
                for value in filter_config.values:
                    # Make sure we match on the correct field, based on whether it's numeric or string
                    match_field = "value_numeric" if filter_config.is_numeric_value else "value_string"
                    should_terms.append({"match": {f"filterable_attributes.{match_field}": value}})

                filter_must_clauses.append(
                    {
                        "nested": {
                            "path": "filterable_attributes",
                            "query": {
                                "bool": {
                                    "must": [
                                        {"match": {"filterable_attributes.attribute_id": filter_config.attribute_id}},
                                        {"bool": {"should": should_terms, "minimum_should_match": 1}},
                                    ]
                                }
                            },
                        }
                    }
                )
            # Range filter
            elif filter_config.min_value is not None or filter_config.max_value is not None:
                range_query = {}
                if filter_config.min_value is not None:
                    range_query["gte"] = filter_config.min_value
                if filter_config.max_value is not None:
                    range_query["lte"] = filter_config.max_value

                filter_must_clauses.append(
                    {
                        "nested": {
                            "path": "filterable_attributes",
                            "query": {
                                "bool": {
                                    "must": [
                                        {"match": {"filterable_attributes.attribute_id": filter_config.attribute_id}},
                                        {"range": {"filterable_attributes.value_numeric": range_query}},
                                    ]
                                }
                            },
                        }
                    }
                )

        if not filter_must_clauses:
            return {"match_all": {}}
        elif len(filter_must_clauses) == 1:
            return filter_must_clauses[0]
        else:
            return {"bool": {"must": filter_must_clauses}}

    def _build_search_query(
        self,
        search_query: Optional[str] = None,
        category_id: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Build OpenSearch query based on search parameters.
        Filters for post_filter are handled separately.
        """

        must_clauses: list[Dict[str, Any]] = []

        # Add general search query if provided
        if search_query:
            should_clauses: List[Dict[str, Any]] = []

            # 1a. Regular keyword fields - exact phrase matching (highest priority)
            phrase_query = {
                "multi_match": {
                    "query": search_query,
                    "fields": ["article_number^7", "master_code^7"],
                    "type": "phrase",
                }
            }
            should_clauses.append(phrase_query)

            # 1b. Regular keyword fields - fuzzy matching for typo correction (lower priority)
            if self.fuzzy_enabled:
                fuzzy_query = {
                    "multi_match": {
                        "query": search_query,
                        "fields": ["article_number^4", "master_code^4"],
                        "type": "best_fields",
                        "fuzziness": "AUTO",
                    }
                }
                should_clauses.append(fuzzy_query)

            # 2a. Search terms/synonyms field - exact phrase matching
            synonym_phrase_query = {"match_phrase": {"synonyms": {"query": search_query, "boost": 5}}}
            should_clauses.append(synonym_phrase_query)

            # 2b. Search terms/synonyms field - fuzzy matching for typo correction
            if self.fuzzy_enabled:
                synonym_fuzzy_query = {"match": {"synonyms": {"query": search_query, "boost": 3, "fuzziness": "AUTO"}}}
                should_clauses.append(synonym_fuzzy_query)

            # 3. Nested fields configuration
            nested_configs: List[NestedFieldConfig] = [
                {"path": "product_names", "field": "product_names.value", "boost": 3},
                {"path": "master_product_names", "field": "master_product_names.value", "boost": 3},
                {"path": "category_names", "field": "category_names.value", "boost": 2},
            ]

            # Build nested queries - both phrase and fuzzy matching
            for config in nested_configs:
                # Add phrase query for exact phrase matching (higher boost)
                nested_phrase_query = OpenSearchQueryBuilder._create_nested_phrase_query(
                    config["path"], config["field"], search_query, config["boost"] + 1
                )
                should_clauses.append(nested_phrase_query)

                # Add fuzzy query for typo correction (lower boost)
                if self.fuzzy_enabled:
                    nested_fuzzy_query = OpenSearchQueryBuilder._create_nested_fuzzy_query(
                        config["path"], config["field"], search_query, config["boost"]
                    )
                    should_clauses.append(nested_fuzzy_query)

            # Combine all search clauses
            must_clauses.append({"bool": {"should": should_clauses, "minimum_should_match": 1}})

        # Add category ID filter if provided
        if category_id:
            must_clauses.append({"term": {"category_ids": category_id}})

        # Build final query
        if not must_clauses:
            return {"match_all": {}}
        elif len(must_clauses) == 1:
            return must_clauses[0]
        else:
            return {"bool": {"must": must_clauses}}
