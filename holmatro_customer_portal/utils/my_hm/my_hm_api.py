from typing import Any, Optional

import requests
from cachetools import T<PERSON><PERSON><PERSON>, cached
from fastapi import HTTP<PERSON>x<PERSON>, status
from sqlalchemy import Column

from holmatro_customer_portal.database.enums import Currency
from holmatro_customer_portal.schemas.my_hm_api_schema import <PERSON><PERSON>HMContact, MYHMPrice, MYHMProductInfo, MYHMStock
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.my_hm.my_hm_interface import MyHolmatroClientInterface
from holmatro_customer_portal.utils.transforms import convert_dict_keys_to_snake

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class MyHolmatroPortalApiClient(MyHolmatroClientInterface):
    def __init__(self, jwt_token: str) -> None:
        self.session = requests.Session()
        self.base_url = Env.MY_HM_BASE_URL.get()
        self.headers = {
            "Ocp-Apim-Subscription-Key": Env.MY_HM_SUBSCRIPTION_TOKEN.get(),
            "Content-Type": "application/json",
            "Authorization": f"Bearer {jwt_token}",
        }

    def get_user_info(self) -> MYHMContact:
        """Get user info."""
        user_info = self._handle_request("GET", "/general/user")
        if not user_info:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Not authenticated")
        return MYHMContact(**convert_dict_keys_to_snake(user_info))

    def get_sales_info(
        self, article_numbers: list[str], currency: Currency | Column[Currency] | None
    ) -> list[MYHMProductInfo]:
        """Get sales information, including availability and prices, for a list of products"""

        # Endpoint accepts from 1 to 50 article numbers
        if not article_numbers:
            return []

        if len(article_numbers) > 50:
            _logger.error("Get sales info endpoint from MHApi does not accept more than 50 article numbers")
            raise RuntimeError

        sales_info: dict | None = self._handle_request(
            "GET", f"/products/salesinfo", params={"articleno": article_numbers}
        )
        result = []
        if sales_info:
            prices: list | None = sales_info.get("SalesInfo")
            if prices:
                # Validate each product in response
                for sales_price in prices:
                    article_number = sales_price.get("ArticleNo")
                    result.append(self._parse_product_info(sales_price, currency, article_number))
                    article_numbers.remove(article_number)

        # Make sure that all the products have sales info
        for article_number in article_numbers:
            result.append(MYHMProductInfo(article_no=article_number))

        return result

    def get_product_sales_prices(self, article_no: str, currency: Currency | Column[Currency] | None) -> MYHMPrice:
        """Get sales prices of a product"""

        sales_prices = self._handle_request("GET", f"/products/{article_no}/salesprices")
        if sales_prices and currency and sales_prices.get("Currency") == currency.value:
            return MYHMPrice(**convert_dict_keys_to_snake(sales_prices))

        return MYHMPrice(article_no=article_no)

    @staticmethod
    def _parse_product_info(
        sales_prices: dict | None, currency: Currency | Column[Currency] | None, article_no: str
    ) -> MYHMProductInfo:
        if sales_prices and currency and sales_prices.get("Currency") == currency.value:
            return MYHMProductInfo(**convert_dict_keys_to_snake(sales_prices))

        return MYHMProductInfo(article_no=article_no)

    @cached(cache=TTLCache(maxsize=Env.CACHE_SIZE.get(), ttl=Env.CACHE_TTL.get()))
    def get_stock(self, article_no: str) -> MYHMStock:
        """Get the stock of a product"""
        stock = self._handle_request("GET", f"/products/{article_no}/availability")

        if not stock:
            return MYHMStock(article_no=article_no)

        return MYHMStock(**convert_dict_keys_to_snake(stock))

    def _handle_request(self, method: str, endpoint: str, **kwargs: Any) -> Optional[dict]:
        """Handle API requests with error handling for a FastAPI application."""
        try:
            url = f"{self.base_url}{endpoint}"
            response = self.session.request(method, url, headers=self.headers, **kwargs)
            if response.status_code == 404:
                return None
            response.raise_for_status()
            output = response.json()

            if not isinstance(output, dict):
                raise HTTPException(
                    status_code=400,
                    detail=f"Expected a dictionary, but got {type(output).__name__} from My Holmatro Api.",
                )

            return output

        except requests.exceptions.HTTPError as e:
            status_code = e.response.status_code
            _logger.error(f"HTTP error {status_code} while connecting to My Holmatro Api: {e}")
            raise HTTPException(status_code=status_code, detail=str(e))
        except requests.exceptions.RequestException as e:
            _logger.error(f"Error while connecting to My Holmatro Api: {e}")
            raise HTTPException(status_code=500, detail=str(e))
