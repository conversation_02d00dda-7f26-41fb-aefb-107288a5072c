from sqlalchemy import Column

from holmatro_customer_portal.database.enums import Currency
from holmatro_customer_portal.schemas.my_hm_api_schema import <PERSON><PERSON>HMContact, MYHMPrice, MYHMProductInfo, MYHMStock
from holmatro_customer_portal.utils.my_hm.my_hm_interface import MyHolmatroClientInterface


class MyHolmatroClientStub(MyHolmatroClientInterface):
    """Stub class for MyHolmatroClientInterface that returns the expected models without external calls
    Used for users that do not have access to the MyHolmatro API"""

    def get_user_info(self) -> MYHMContact:
        """Get user info from the MyHolmatro API"""
        raise NotImplementedError("Method not implemented for anonymous users")

    def get_sales_info(
        self, article_numbers: list[str], currency: Currency | Column[Currency] | None
    ) -> list[MYHMProductInfo]:
        """Get sales information, including availability and prices, for a list of products"""
        return [MYHMProductInfo(article_no=article_no) for article_no in article_numbers]

    def get_product_sales_prices(self, article_no: str, currency: Currency | Column[Currency] | None) -> MYHMPrice:
        """Get sales prices of a product"""
        return MYHMPrice(article_no=article_no)

    def get_stock(self, article_no: str) -> MYHMStock:
        """Get the stock of a product"""
        return MYHMStock(article_no=article_no)
