import jwt

from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.my_hm.my_hm_api import MyHolmatroPortalApiClient
from holmatro_customer_portal.utils.my_hm.my_hm_interface import MyHolmatroClientInterface
from holmatro_customer_portal.utils.my_hm.my_hm_stub import MyHolmatroClientStub


class MyHolmatroClientFactory:
    @staticmethod
    def create_client(token: str | None) -> MyHolmatroClientInterface:
        """Determine the correct MyHM client based on the token:
        - If this method is called with token = None, return a stub client
        - If the token is from the anonymous user, return a stub client
        - Otherwise, return a client that uses the token to authenticate with the MyHM API
        """

        if token is None:
            return MyHolmatroClientStub()

        decoded = jwt.decode(token, options={"verify_signature": False})

        if decoded.get("iss") == Env.JWT_ISSUER_ANONYMOUS_USAGE.get():
            return MyHolmatroClientStub()

        return MyHolmatroPortalApiClient(token)
