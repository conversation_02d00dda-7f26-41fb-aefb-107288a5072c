from abc import abstractmethod
from typing import Protocol

from sqlalchemy import Column

from holmatro_customer_portal.database.enums import Currency
from holmatro_customer_portal.schemas.my_hm_api_schema import MYHMContact, MYHMPrice, MYHMProductInfo, MYHMStock


class MyHolmatroClientInterface(Protocol):

    @abstractmethod
    def get_user_info(self) -> MYHMContact:
        """Get user info from the MyHolmatro API"""

    @abstractmethod
    def get_sales_info(
        self, article_numbers: list[str], currency: Currency | Column[Currency] | None
    ) -> list[MYHMProductInfo]:
        """Get sales information, including availability and prices, for a list of products"""

    @abstractmethod
    def get_product_sales_prices(self, article_no: str, currency: Currency | Column[Currency] | None) -> MYHMPrice:
        """Get sales prices of a product"""

    @abstractmethod
    def get_stock(self, article_no: str) -> MYHMStock:
        """Get the stock of a product"""
