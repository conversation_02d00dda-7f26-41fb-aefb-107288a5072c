import re


def camel_to_snake(name: str) -> str:
    """Convert a CamelCase string to snake_case."""
    s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", name)
    return re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1).lower()


def convert_dict_keys_to_snake(data: dict) -> dict:
    """Recursively convert dictionary keys from CamelCase to snake_case."""
    new_data: dict = {}

    for key, value in data.items():
        new_key = camel_to_snake(key)

        if isinstance(value, dict):
            new_data[new_key] = convert_dict_keys_to_snake(value)
        elif isinstance(value, list):
            list_items = [convert_dict_keys_to_snake(item) if isinstance(item, dict) else item for item in value]
            new_data[new_key] = list_items
        else:
            new_data[new_key] = value

    return new_data


def validate_and_transform_article_numbers(article_numbers: list[str]) -> list[str]:
    """
    Some article numbers might not have dots when in the redirects response from Tweakwise.
    This method check if it has 9 numbers and if it doesn't have dots, add them to it.
    """
    result = []
    for article_number in article_numbers:
        # Replace the dots to check if article number contains only 9 numbers
        if re.match(r"\d{9}$", article_number.replace(".", "")):
            if "." in article_number:
                result.append(article_number)
            else:
                # Insert dots every three numbers
                article_number = ".".join([article_number[i : i + 3] for i in range(0, len(article_number), 3)])
                result.append(article_number)

    return result
