from typing import Any, Type

from fastapi import Request
from sqlalchemy import create_engine
from sqlalchemy.orm import Query as BaseQuery
from sqlalchemy.orm import Session as BaseSession
from sqlalchemy.orm import sessionmaker

from holmatro_customer_portal.database.base import Base
from holmatro_customer_portal.utils.env import Env


def session_creator(db_url: str) -> sessionmaker:
    engine = create_engine(db_url, pool_size=5, max_overflow=10, pool_pre_ping=True)
    return sessionmaker(bind=engine)


def get_db(request: Request) -> BaseSession:
    if isinstance(request.state.db_write, BaseSession):
        return request.state.db_write
    raise ValueError("Current session is not stored in the state")


def construct_db_write_url() -> str:
    return f"mysql+pymysql://{Env.DATABASE_USER.get()}:{Env.DATABASE_PASSWORD.get()}@{Env.DATABASE_WRITE_HOST.get()}:{Env.DATABASE_PORT.get()}/{Env.DATABASE_NAME.get()}"


class Session(BaseSession):
    pass


class Query(BaseQuery):
    pass


class DatabaseOperations:
    def __init__(self, session: BaseSession):
        self.session = session

    def upsert(self, model: Type[Base], filter_by: dict, **values: Any) -> Base:
        instance = self.session.query(model).filter_by(**filter_by).one_or_none()

        if not instance:
            instance = model(**values)
        else:
            for key, value in values.items():
                setattr(instance, key, value)
        return instance
