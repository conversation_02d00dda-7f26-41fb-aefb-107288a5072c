import json
import os
import platform
from typing import Any, Optional, <PERSON>ple

import boto3  # type: ignore
import requests

from holmatro_customer_portal.schemas.syncforce_product_schema import AssetDetails as SFAssetDetails
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.transforms import convert_dict_keys_to_snake

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())
s3_client = boto3.client("s3")


class StorageClient:
    def __init__(self) -> None:
        self.api_url = Env.AZURE_API_URL.get()
        self.api_key = Env.AZURE_API_KEY.get()
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "User-Agent": f"HolmatroCustomerPortal (Linux; Python {platform.python_version()}; requests/{requests.__version__})",
        }

    def get_request(self, path: str, params: dict) -> Any:
        try:
            url = f"https://{self.api_url}/files{path}"
            response = requests.get(url, params=params, headers=self.headers, timeout=60)

            if response.status_code != 200:
                raise ValueError(f"Error fetching {params}: {response.status_code}")

            content_type = response.headers.get("content-type")

            if content_type == "application/json":
                return response.json()
            elif content_type and content_type.endswith(("pdf", "png", "jpg", "jpeg")):
                return response.content

        except requests.RequestException as e:
            raise ValueError(f"Error fetching {params}: {str(e)}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Error parsing JSON response: {str(e)}")


storage_client = StorageClient()


def get_api_json_contents(file_name: str) -> Any:
    try:
        _logger.info(f"Fetching {file_name} from Azure API")
        return storage_client.get_request("", params={"file_name": file_name})
    except ValueError as e:
        raise e


def fetch_json_contents_by_ids(ids: list) -> dict:
    _logger.info(f"Fetching product details for {ids}")
    keys = [f"product-details-{id_}.json" for id_ in ids]
    results = {}

    for key in keys:
        try:
            results[key] = storage_client.get_request("", params={"file_name": key})
        except ValueError as e:
            raise e

    return results


def load_assets(uuid: str) -> Tuple[Optional[SFAssetDetails], dict]:
    _logger.info(f"Fetching assets for {uuid}")
    base_path = "assets/"
    s3_path = f"{base_path}{uuid}"
    asset_details: dict = {}
    results = {}

    try:
        directory: list | dict = storage_client.get_request("/list_objects", params={"blob_path": s3_path})
    except ValueError as e:
        raise ValueError(f"Error fetching {s3_path} path: {str(e)}")

    for content in directory:
        key = content["Key"]
        if key.endswith("asset-details.json"):
            try:
                _logger.debug("Retrieving asssset details")
                asset_details = storage_client.get_request("", params={"file_name": key})
                s3_client.put_object(
                    Bucket=Env.HOLMATRO_ASSETS_BUCKET_NAME.get(), Key=key, Body=json.dumps(asset_details)
                )
            except ValueError as e:
                raise ValueError(f"Error fetching asset details for {key}: {str(e)}")
        else:
            try:
                _logger.debug(f"Retrieving asset with {key} from storage")
                asset = storage_client.get_request("", params={"file_name": key})
                if isinstance(asset, bytes):
                    _logger.debug(f"Uploading image with {key} to S3")
                    s3_client.put_object(Bucket=Env.HOLMATRO_ASSETS_BUCKET_NAME.get(), Key=key, Body=asset)
                else:
                    _logger.debug(f"Uploading asset with {key} to S3")
                    s3_client.put_object(Bucket=Env.HOLMATRO_ASSETS_BUCKET_NAME.get(), Key=key, Body=json.dumps(asset))
                s3_client.put_object_acl(Bucket=Env.HOLMATRO_ASSETS_BUCKET_NAME.get(), Key=key, ACL="public-read")
                _logger.debug("Stored asset in S3, set the access control list to public-read")
                public_url = f"https://{Env.HOLMATRO_ASSETS_BUCKET_NAME.get()}.s3.eu-west-1.amazonaws.com/{key}"
                results[os.path.basename(key)] = {"url": public_url, "size": content["Size"] / (1024**2)}
            except ValueError as e:
                raise ValueError(f"Error fetching {key} from Azure API: {str(e)}")
    asset_details_obj = SFAssetDetails(**convert_dict_keys_to_snake(asset_details)) if asset_details else None
    return asset_details_obj, results
