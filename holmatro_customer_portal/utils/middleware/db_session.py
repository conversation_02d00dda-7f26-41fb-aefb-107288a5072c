import traceback

from fastapi import Response
from sqlalchemy.orm import sessionmaker
from starlette.middleware.base import BaseHTTP<PERSON>iddleware, RequestResponseEndpoint
from starlette.requests import Request
from starlette.types import ASGIApp

from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger


class DbSessionMiddleware(BaseHTTPMiddleware):
    """
    Middleware that provides a valid db session to the request handlers, while also
    making sure that the db session is properly closed when an exception is caught.
    """

    def __init__(self, app: ASGIApp, session_creator: sessionmaker) -> None:
        super().__init__(app)
        self.session_creator = session_creator
        self._logger = Logger(__name__, Env.LOGGING_LEVEL.get())

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        try:
            request.state.db_write = self.session_creator()
            response = await call_next(request)
        except Exception as exc:
            exc_info = "".join(traceback.format_exception(type(exc), exc, exc.__traceback__))
            self._logger.debug(exc_info)
            response = Response("Internal server error", status_code=500)
        finally:
            request.state.db_write.close()
        return response
