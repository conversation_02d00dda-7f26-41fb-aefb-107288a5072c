import asyncio
from http import HTTPStatus

from starlette.middleware.base import BaseHTTP<PERSON>iddleware, RequestResponseEndpoint
from starlette.requests import Request
from starlette.responses import JSONResponse, Response
from starlette.types import ASGIApp


class RequestTimeoutMiddleware(BaseHTTPMiddleware):
    """Middleware that returns a 504 error if the request processing time exceeds a certain threshold"""

    def __init__(self, app: ASGIApp, timeout_seconds: float) -> None:
        super().__init__(app)
        self.timeout_seconds = timeout_seconds

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        try:
            return await asyncio.wait_for(call_next(request), timeout=self.timeout_seconds)
        except asyncio.TimeoutError:
            return JSONResponse(
                status_code=HTTPStatus.GATEWAY_TIMEOUT, content={"details": "Request processing time exceeded limit"}
            )
