from fastapi import Depends, HTTPException
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTT<PERSON><PERSON>ear<PERSON>
from python_jwt_auth.fastapi import JWKS, JWTAuthorizationCredentials, JWTAuthorizer  # type: ignore
from requests_cache import CachedSession
from sqlalchemy.orm import joinedload
from starlette.requests import Request

from holmatro_customer_portal.database.models import User
from holmatro_customer_portal.schemas.auth import UserClaims
from holmatro_customer_portal.utils.auth.authorizer import AnonymousJWTAuthorizer
from holmatro_customer_portal.utils.database import Session, get_db
from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.env import Env

# Security scheme for OpenAPI documentation
security_scheme = HTTPBearer(auto_error=False, description="JWT Bearer Token")


def get_jwks(url: str) -> dict:
    session = CachedSession()

    response = session.get(url, timeout=10).json()

    if not isinstance(response, dict):
        raise ValueError("Expected a dictionary in the response, but received something else.")

    return response


class Authenticate:
    def __init__(self) -> None:
        self.jwks_data = get_jwks(
            f"{Env.B2C_BASE_URL.get()}/{Env.B2C_TENANT_ID.get()}/discovery/v2.0/keys?p=b2c_1_myhmb2c_hmportal_sin"
        )

    async def __call__(
        self, request: Request, token: HTTPAuthorizationCredentials = Depends(security_scheme)
    ) -> JWTAuthorizationCredentials:
        # Try to validate token according to Holmatro authentication
        jwks_validated = JWKS.model_validate(self.jwks_data)

        cookie_name = None if token else "token"
        authorizer = JWTAuthorizer(
            jwks_validated, algorithm="RS256", cookie_token_name=cookie_name, options={"verify_aud": False}  # nosec
        )

        try:
            return await authorizer(request)
        except HTTPException as e:
            # If Holmatro authentication fails, try to authenticate anonymous user
            if (e.detail == "JWK public key not found" or e.detail == "JWK invalid") and (
                Env.ALLOW_ANONYMOUS_USER.get() is True
            ):

                authorizer = AnonymousJWTAuthorizer(
                    JWKS(keys=[]),
                    jwt_key=Env.JWT_ENCODING_KEY.get(),
                    algorithm="HS256",
                    cookie_token_name=cookie_name,
                    options={"verify_aud": False},
                )
                return await authorizer(request)
            raise e
        except Exception as e:
            raise HTTPException(status_code=403, detail="Not Authenticated")


auth_instance: Authenticate | None = None


def get_auth_instance() -> Authenticate:
    global auth_instance

    if auth_instance is None:
        auth_instance = Authenticate()
    return auth_instance


def get_user_claims(
    credentials: JWTAuthorizationCredentials = Depends(get_auth_instance()),
) -> UserClaims:
    if not credentials:
        raise HTTPException(status_code=403, detail="Not Authenticated")
    return UserClaims(**credentials.claims)


def get_jwt_token(credentials: JWTAuthorizationCredentials = Depends(get_auth_instance())) -> str:
    if not credentials:
        raise HTTPException(status_code=403, detail="Not Authenticated")

    return str(credentials.jwt_token)


def get_user(
    db: Session = Depends(get_db),
    credentials: JWTAuthorizationCredentials = Depends(get_auth_instance()),
) -> User:
    if not credentials:
        raise HTTPException(status_code=403, detail="Not Authenticated")

    try:
        user_claims = UserClaims(**credentials.claims)
        user = db.query(User).filter(User.email == user_claims.emails[0]).one_or_none()
        if user is None:
            raise HTTPException(status_code=403, detail=f"No user found with email address {user_claims.emails[0]}")
        else:
            return _order_categories(user)
    except KeyError:
        raise HTTPException(status_code=403, detail="No user claims found")


def get_user_with_favorites(
    db: Session = Depends(get_db),
    credentials: JWTAuthorizationCredentials = Depends(get_auth_instance()),
) -> User:
    claims = credentials.claims
    if not credentials:
        raise HTTPException(status_code=403, detail="Not Authenticated")

    try:
        user_claims = UserClaims(**claims)
        user = (
            db.query(User)
            .options(joinedload(User.favorite_products))
            .filter(User.email == user_claims.emails[0])
            .one_or_none()
        )
        if user is None:
            raise HTTPException(status_code=403, detail=f"No user found with email address {user_claims.emails[0]}")
        else:
            return _order_categories(user)
    except KeyError:
        raise HTTPException(status_code=403, detail="No user claims found")


def _order_categories(user: User) -> User:
    # Order user categories according to proposition
    proposition_categories = [enum.value for enum in SyncforceCategory]
    user.categories = sorted(user.categories, key=lambda cat: proposition_categories.index(int(cat.category_id)))
    return user
