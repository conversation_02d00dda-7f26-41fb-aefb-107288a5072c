import uuid
from datetime import datetime, timedelta, timezone
from typing import cast

import jwt

from holmatro_customer_portal.database.enums import LanguageEnum
from holmatro_customer_portal.database.models import Category, Language, User
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.env import Env


def create_anonymous_user(db: Session, language_code: LanguageEnum) -> User:
    db_language = db.query(Language).filter_by(language_code=language_code.value).first()

    # According to RFC 2606 the domain @example.com is reserved by the Internet Engineering Task Force (IETF)
    # for use in documentation and examples and will not result in actual email communication.
    categories = (
        db.query(Category)
        .filter(
            Category.category_id.in_(
                [SyncforceCategory.INDUSTRIAL_LIFTING.value, SyncforceCategory.INDUSTRIAL_CUTTING.value]
            )
        )
        .all()
    )
    user_id = uuid.uuid4()
    anonymous_user = User(
        id=user_id,
        email=f"{user_id}@example.com",
        preferred_language=db_language,
        categories=categories,
        anonymous=True,
    )
    db.add(anonymous_user)
    db.commit()

    return anonymous_user


def generate_token(user: User) -> str:
    jwt_payload = {
        "exp": datetime.now(timezone.utc) + timedelta(weeks=52),
        "sub": str(user.id),
        "emails": [user.email],
        "iss": Env.JWT_ISSUER_ANONYMOUS_USAGE.get(),
    }
    access_token = cast(
        str, jwt.encode(jwt_payload, Env.JWT_ENCODING_KEY.get(), algorithm="HS256")
    )  # jwt.encode always returns a string but MyPy does not understand this because there is a call to a nested function
    return access_token
