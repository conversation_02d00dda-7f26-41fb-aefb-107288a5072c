from typing import Optional

import jwt
from python_jwt_auth.fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON>zer  # type: ignore
from python_jwt_auth.fastapi.models import JWKS, JWTAuthorizationCredentials  # type: ignore

from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class AnonymousJWTAuthorizer(JWTAuthorizer):
    def __init__(
        self,
        jwks: JWKS,
        jwt_key: str,
        auto_error: bool = True,
        algorithm: Optional[str] = None,
        cookie_token_name: Optional[str] = None,
        audience: Optional[str] = None,
        options: dict = {},
    ):
        super().__init__(
            jwks=jwks,
            auto_error=auto_error,
            algorithm=algorithm,
            cookie_token_name=cookie_token_name,
            audience=audience,
            options=options,
        )
        self.jwt_key = jwt_key

    def verify_jwk_token(self, jwt_credentials: JWTAuthorizationCredentials) -> bool:
        """Overwriting this class to use a symmetric key for anonymous users.
        This is needed because the anonymous user token is generated with a symmetric key not with a jwk.
        """
        try:
            jwt.decode(
                jwt_credentials.jwt_token,
                self.jwt_key,
                algorithms=self.algorithm,
                options=self.options,
                audience=self.audience,
            )
            return True
        except Exception as e:
            _logger.error(f"Received exception when validating jwt token: {e}")
            return False
