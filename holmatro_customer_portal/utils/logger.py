import logging
import sys


class Logger(logging.Logger):
    """
    Custom logger class, will have 'name' in front of all log statements
    """

    def __init__(self, name: str, level: str = "INFO") -> None:
        super().__init__(name, level=level)

        self.handler = logging.StreamHandler(sys.stdout)
        self.handler.setLevel(self.get_level(level))
        self.formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        self.handler.setFormatter(self.formatter)
        self.addHandler(self.handler)

    @staticmethod
    def get_level(level: str) -> int:
        """Try to match the provided string to a logging level, default to INFO"""
        if level.upper() == "DEBUG":
            return logging.DEBUG
        if level.upper() == "INFO":
            return logging.INFO
        if "WARN" in level.upper():
            return logging.WARNING
        if level.upper() == "ERROR":
            return logging.ERROR
        if level.upper() == "CRITICAL":
            return logging.CRITICAL

        return logging.INFO
