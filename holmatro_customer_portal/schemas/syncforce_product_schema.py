from typing import Any, Optional

from pydantic import AliasPath
from pydantic import BaseModel as PydanticBaseModel
from pydantic import Field, field_validator, model_validator, root_validator
from pydantic.fields import FieldInfo


class BaseModel(PydanticBaseModel):
    class ConfigDict:
        from_attributes = True


class Language(BaseModel):
    language_code: str = Field(alias="code", description="Code representing the language")


class Market(BaseModel):
    languages: Optional[list[dict]] = Field(default=None, description="list of supported languages for the market")
    name: Optional[str] = Field(default=None, description="Name of the market")


class Header(BaseModel):
    markets: Optional[list[Market]] = Field(default=None, description="list of available markets")
    default_language: Optional[str] = Field(default=None, description="Default language for the header")
    owner: Optional[str] = Field(default=None, description="Owner of the header")


class Brand(BaseModel):
    id: Optional[int] = Field(default=None, description="Unique ID for the brand")
    brand_name: Optional[str] = Field(default=None, description="Name of the brand")
    brand_type: Optional[str] = Field(default=None, description="Type of the brand")
    gbin: Optional[str] = Field(default=None, description="GBIN value of the brand")
    owner_name: Optional[str] = Field(default=None, description="Owner's name of the brand")
    owner_gln: Optional[str] = Field(default=None, description="GLN of the brand's owner")
    reference: Optional[str] = Field(default=None, description="Reference for the brand")


class ClassificationTranslation(BaseModel):
    language: str = Field(description="Language code for the translation")
    name: str = Field(description="Name of the classification in the given language")


class Classification(BaseModel):
    translations: list[ClassificationTranslation] = Field(description="Translations for the classification")
    id: str = Field(description="Unique identifier for the classification")
    material_type: str = Field(description="Material type of the classification")


class HierarchyLevelTranslation(BaseModel):
    language: str = Field(description="Language code for the translation")
    name: str = Field(description="Name of the hierarchy level in the given language")


class HierarchyLevel(BaseModel):
    translations: list[HierarchyLevelTranslation] = Field(description="Translations for the hierarchy level")
    id: int = Field(description="Unique identifier for the hierarchy level")


class AssetTypeTranslation(BaseModel):
    language: str = Field(description="Language code for the translation")
    label: str = Field(description="Label of the asset type in the given language")


class AssetType(BaseModel):
    translations: list[AssetTypeTranslation] = Field(description="Translations for the asset type")
    id: int = Field(description="Unique identifier for the asset type")
    label: str = Field(description="Label for the asset type")


class AssetSubType(BaseModel):
    singular: list[AssetTypeTranslation] = Field(description="list of singular translations for the asset subtype")
    plural: list[AssetTypeTranslation] = Field(description="list of plural translations for the asset subtype")
    id: int = Field(description="Unique identifier for the asset subtype")

    @field_validator("plural", mode="before")
    def extract_plural_translations(cls, value: dict[str, list[AssetTypeTranslation]]) -> list[AssetTypeTranslation]:
        return value["translations"]

    @field_validator("singular", mode="before")
    def extract_singular_translations(cls, value: dict[str, list[AssetTypeTranslation]]) -> list[AssetTypeTranslation]:
        return value["translations"]


class AssetRole(BaseModel):
    translations: Optional[list[AssetTypeTranslation]] = Field(
        default=None, description="list of translations for the asset role"
    )
    id: int = Field(description="Unique identifier for the asset role")
    sort_index: Optional[int] = Field(default=None, description="Sort index for ordering the roles")


class AssetRelationship(BaseModel):
    id: Optional[int] = Field(default=None, description="Unique identifier for the asset relationship")
    udai: str = Field(description="Unique Digital Asset Identifier")
    role_id: int = Field(description="Identifier referencing the type of asset relationship role")


class Asset(BaseModel):
    roles: list[AssetRole] = Field(description="Roles associated with the asset")
    types: list[AssetType] = Field(description="Types of the asset")
    sub_types: list[AssetSubType] = Field(description="Subtypes of the asset")
    asset: Optional[list[AssetRelationship]] = Field(
        default=None, description="Relationships associated with the asset"
    )


class ProductTextTranslation(BaseModel):
    language: str = Field(description="Language code for the translation, e.g., 'en', 'es', etc.")
    label: str = Field(description="Label indicating the type or category of the product text")
    value: Optional[str] = Field(
        default=None, description="Translated text content related to the product in the specified language"
    )


class ProductText(BaseModel):
    translations: list[ProductTextTranslation] = Field(
        description="list of translations for the product text in various languages"
    )
    id: int = Field(description="Unique identifier for the product text")
    is_derived: bool = Field(description="Flag indicating if the text is derived from another source or original")


class AttributeGroupTranslation(BaseModel):
    language: str = Field(description="Language code for the translation")
    name: str = Field(description="Translated name of the attribute group in the specified language")


class AttributeGroup(BaseModel):
    translations: list[AttributeGroupTranslation] = Field(
        description="list of translations for the attribute group name in various languages"
    )
    id: int = Field(description="Unique identifier for the attribute group")


class AttributeValueTranslation(BaseModel):
    language: str = Field(description="Language code for the translation")
    content: Optional[str] = Field(
        default=None,
        serialization_alias="name",
        description="Translated content or value of the attribute in the specified language",
    )

    # noinspection PyNestedDecorators
    @model_validator(mode="before")
    @classmethod
    def populate_content_from_name(cls, data: Any) -> Any:
        """
        Allows 'content' to be populated from a 'name' field in the input data if 'content' is not already provided.
        This is needed because this object is used for both attribute value translations and unit types, where the
        'name' field is used.
        """
        if isinstance(data, dict) and "name" in data and "content" not in data:
            data["content"] = data.pop("name")
        return data


class SegmentationTargetGroup(BaseModel):
    id: int = Field(description="Unique identifier for the segmentation target group")
    name: str = Field(description="Name of the segmentation target group")
    from_date: str = Field(description="Start date for the validity or applicability of the segmentation target group")
    until_date: str = Field(description="End date for the validity or applicability of the segmentation target group")


class SegmentationMarket(BaseModel):
    languages: list[Language] = Field(description="list of languages supported in the segmentation market")
    name: str = Field(description="Name of the segmentation market")


class MasterProductNameTranslation(BaseModel):
    language: str = Field(description="Language code for the translation, e.g., 'en', 'es', etc.")
    value: str = Field(description="Translated name of the master product in the specified language")


class ProductNameTranslation(BaseModel):
    language: str = Field(description="Language code for the translation, e.g., 'en', 'es', etc.")
    value: str = Field(description="Translated name or label of the product in the specified language")


class Translation(BaseModel):
    language: str = Field(description="Language code for the translation, e.g., 'en', 'es', etc.")
    name: str = Field(description="Name or label that's being translated")
    content: str = Field(description="Actual translated content or text in the specified language")


class AttributeTranslation(BaseModel):
    language: str = Field(description="Language code for the translation")
    name: str = Field(description="Name of the attribute in the given language")
    content: str = Field(description="Content of the attribute in the given language")
    attribute_group_id: int = Field(description="ID of the associated attribute group")


class SingleAttributeTranslation(BaseModel):
    language: str = Field(description="Language code for the translation")
    name: str = Field(description="Name of the single attribute in the given language")


class AttributeUnitType(BaseModel):
    id: int = Field(description="Unique identifier for the attribute unit type.")
    unit_before: Optional[bool] = Field(
        default=None, description="Flag indicating if the unit appears before the value. E.g., '$10' vs '10 dollars'."
    )
    translations: Optional[list[AttributeValueTranslation]] = Field(
        default=None, description="List of translations for the attribute's value."
    )


class SingleAttributeValue(BaseModel):
    unit_id: Optional[int] = Field(default=None, description="Foreign key referring to the associated unit type.")
    content: str = Field(description="Actual content or value of the attribute.")
    is_derived: Optional[bool] = Field(
        default=None, description="Flag indicating if the value is derived or computed from other values."
    )
    translations: Optional[list[AttributeValueTranslation]] = Field(
        default=None, description="List of translations for the attribute value."
    )


class SingleAttribute(BaseModel):
    translations: list[SingleAttributeTranslation] = Field(
        description="Translations for the attribute in different languages"
    )
    id: int = Field(description="Unique identifier for the attribute")
    type: str = Field(description="Type of the attribute, e.g., 'text', 'number', etc.")
    multi_lingual_values: bool = Field(description="Indicates if the attribute supports values in multiple languages")
    is_read_only: bool = Field(description="Indicates if the attribute is read-only and cannot be modified")
    attribute_group_id: int = Field(description="Identifier of the group this attribute belongs to")
    value: Optional[SingleAttributeValue] = Field(
        default=None,
        validation_alias=AliasPath("values", 0),
        description="Unique identifier for the product relation role",
    )
    units: Optional[list[AttributeUnitType]] = Field(default=None, description="Unit types")


class ProductRelationTranslation(BaseModel):
    language: Optional[str] = Field(default=None, description="Language code of the translation")
    label: Optional[str] = Field(
        default=None, description="Label or description of the product relation in the specified language"
    )


class ProductRelationRole(BaseModel):
    translations: list[ProductRelationTranslation] = Field(
        description="list of translations for different languages representing the product relation role"
    )
    id: int = Field(description="Unique identifier for the product relation role")


class RelationProduct(BaseModel):
    umid: str = Field(description="UMID of the related product, a unique identifier for products")


class ProductRelation(BaseModel):
    products: list[RelationProduct] = Field(description="list of products involved in a relation")
    role_id: int = Field(description="Identifier referencing the role type of the product relation")


class ProductRelations(BaseModel):
    roles: list[ProductRelationRole] = Field(description="list of roles that a product can have in relations")
    relation: list[ProductRelation] = Field(
        description="list of relations where products are related to each other through specific roles"
    )


class CategoryTranslation(BaseModel):
    language: str
    label: str


class Category(BaseModel):
    translations: list[CategoryTranslation]
    sort_index: int
    syncforce_index: int
    category_umid: str
    assets: Optional[list[AssetRelationship]]
    category_id: str
    product_sort_index: Optional[int] = None

    @root_validator(pre=True)
    def generate_category_id(cls, values: dict[str, Any]) -> dict[str, Any]:
        umid = values["category_umid"]
        partial_uuid = umid.replace("-", "")[:8]

        # Convert the hexadecimal to an integer
        int_value = int(partial_uuid, 16)

        five_digit_int = int_value % 10000
        values["category_id"] = str(five_digit_int)
        return values


class Product(BaseModel):
    umid: str
    part_nr: str = Field(description="Product part number")
    gtin: str
    article_number: str = Field(description="Product article number")
    attributes: dict
    id: int = Field(description="Product ID defined in Tweakwise")
    master_code: str = Field(description="The code of the master product")
    languages: list[Language] = Field(description="The product languages")
    header: Optional[Header] = Field(default=None, description="Header details")
    brand: Brand = Field(description="Brand information")
    segmentation: list[SegmentationTargetGroup]
    classification: Classification = Field(description="Classification of the product")
    hierarchy_level: HierarchyLevel = Field(description="Hierarchy level of the product")
    name: list[ProductNameTranslation] = Field(description="Name of the product")
    master_name: list[MasterProductNameTranslation] = Field(description="Name of the master product")
    assets: Asset = Field(description="Assets associated with the product")
    unit_types: Optional[list[AttributeUnitType]] = Field(default=[], validate_default=True)
    texts: Optional[list[ProductText]] = Field(default=[], description="Textual details of the product")
    attribute_groups: list[AttributeGroup] = Field(description="Groups of attributes")
    product_attributes: list[SingleAttribute] = Field(
        default=[], description="list of attributes", validate_default=True
    )
    relations: Optional[ProductRelations] = Field(default=None, description="Relations to other products")

    @field_validator("unit_types", mode="before")
    def extract_unit_types(cls, value: Any, values: Any) -> Any:
        return values.data["attributes"].get("units")

    @field_validator("product_attributes", mode="before")
    def extract_key_pair(cls, value: Any, values: Any) -> Any:
        return values.data["attributes"]["attribute"]

    @field_validator("segmentation", mode="before")
    def extract_segmentation(cls, value: dict[str, list[SegmentationTargetGroup]]) -> list[SegmentationTargetGroup]:
        return value["target_groups"]

    @field_validator("name", mode="before")
    def extract_name_translations(cls, value: dict[str, list[ProductNameTranslation]]) -> list[ProductNameTranslation]:
        return value["translations"]

    @field_validator("master_name", mode="before")
    def extract_master_name_translations(
        cls, value: dict[str, list[MasterProductNameTranslation]]
    ) -> list[MasterProductNameTranslation]:
        return value["translations"]


class FileLanguage(BaseModel):
    language: str


class File(BaseModel):
    char: str
    file_name: str
    cid: str
    type: str
    url: Optional[str]
    expiry_url: str
    bref: str
    change_date: Optional[str]
    languages: Optional[list[FileLanguage]]
    countries: Optional[list[str]]


class AssetDetails(BaseModel):
    udai: str
    reference_nr: int
    change_date: str
    type_id: int
    sub_type_id: int
    translations: list[AssetTypeTranslation]
    files: list[File]
    segmentation: dict
