from datetime import date, datetime, timedelta, timezone
from decimal import Decimal
from typing import Any, Optional

from pydantic import EmailStr, Field, field_validator, model_validator

from holmatro_customer_portal.database.enums import Currency
from holmatro_customer_portal.schemas.response_schema import Stock
from holmatro_customer_portal.schemas.syncforce_product_schema import BaseModel


class MYHMAttributes(BaseModel):
    sf_proposition: str
    sf_category: str
    sf_sub_category: str


class MYHMContact(BaseModel):
    email_address: EmailStr
    contact_id: str
    currency_id: Currency | None
    language_id: Optional[str] = Field(default="en")
    country_id: str
    created_ts_utc: datetime
    updated_ts_utc: datetime
    relation_name: str
    attributes: list[MYHMAttributes]

    @field_validator("language_id", mode="before")
    def extract_language(cls, value: str | None) -> str | None:
        if not value:
            return None
        return value.lower()

    @model_validator(mode="before")
    def check_currency(cls, values: dict[str, dict]) -> dict[str, Any]:
        base_values = values["user"]

        if base_values["currency_id"] == "*":
            base_values["currency_id"] = None
        base_values["attributes"] = values["attributes"]

        return base_values


class MYHMPrice(BaseModel):
    article_no: str
    gross_price: Decimal | None = Field(default=None)
    nett_price: Decimal | None = Field(default=None)
    discount: int | None = Field(default=None)
    currency: Currency | None = Field(default=None)
    created_ts_utc: datetime = Field(default=datetime.now(tz=timezone.utc))
    updated_ts_utc: datetime = Field(default=datetime.now(tz=timezone.utc))

    @model_validator(mode="before")
    def validate_discount(cls, values: Any) -> Any:
        if not values.get("discount"):
            values["discount"] = values.get("discount%")

        return values


class MYHMStock(BaseModel):
    article_no: str
    stock_status: Stock = Field(default=Stock.ON_REQUEST)
    stock_quantity: float | None = Field(alias="stock", default=None)
    restock_days: int | None = Field(default=None)
    available_from: date | None = Field(default=None)
    created_ts_utc: datetime = Field(default=datetime.now(tz=timezone.utc))
    updated_ts_utc: datetime = Field(default=datetime.now(tz=timezone.utc))

    @model_validator(mode="before")
    def get_stock_status(cls, values: Any) -> Any:
        stock_quantity = values.get("stock")

        # TODO: Tricky, what if it's an integer value? Float doesn't really make sense for quantity,
        # or are there any products that can have decimal quantities?
        if not isinstance(stock_quantity, float):
            values["stock_status"] = Stock.ON_REQUEST
        else:
            if stock_quantity <= 0:
                values["stock_status"] = Stock.NO_STOCK
            else:
                values["stock_status"] = Stock.IN_STOCK
        return values

    @model_validator(mode="before")
    def get_available_from(cls, values: Any) -> Any:
        values["available_from"] = None
        if restock_days := values.get("restock_days"):
            values["available_from"] = datetime.now(timezone.utc).date() + timedelta(days=restock_days)
        return values


class MYHMProductInfo(MYHMStock, MYHMPrice):
    article_no: str
