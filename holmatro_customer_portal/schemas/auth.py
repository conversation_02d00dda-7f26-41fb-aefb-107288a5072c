import uuid
from typing import List

from pydantic import BaseModel, EmailStr, field_validator

from holmatro_customer_portal.utils.env import Env


class UserClaims(BaseModel):
    emails: List[EmailStr]
    name: str | None = None
    family_name: str | None = None
    iss: str | None = None

    @field_validator("name", mode="before")
    def extract_name(cls, value: str) -> str | None:
        if value == "unknown":
            return None
        else:
            return value

    def is_anonymous_user(self) -> bool:
        return bool(self.iss == Env.JWT_ISSUER_ANONYMOUS_USAGE.get())


class UserLanguage(BaseModel):
    id: uuid.UUID
    code: str


class Token(BaseModel):
    token: str
