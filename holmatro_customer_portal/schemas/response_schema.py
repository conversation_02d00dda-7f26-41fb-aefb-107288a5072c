import uuid
from datetime import date
from decimal import Decimal
from enum import Enum
from typing import Any

from pydantic import UUID4, ConfigDict, EmailStr, Field, HttpUrl

from holmatro_customer_portal.database.enums import AssetType, Currency, SystemType
from holmatro_customer_portal.schemas.syncforce_product_schema import BaseModel
from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import Facets, Filter, ResponseProperties


class ProductDescription(BaseModel):
    long_description: list[str]
    features: list[str]
    supplied_with: list[str]
    available_on_request: list[str]
    accessories: list[str]
    additional_information: list[str]
    other: list[str]


class ProductAutocomplete(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    product_id: uuid.UUID
    name: str
    category_id: int | None


class Stock(Enum):
    NO_STOCK = "no_stock"
    LOW_STOCK = "low_stock"
    IN_STOCK = "in_stock"
    UNKNOWN = "unknown"
    ON_REQUEST = "on_request"


class AttributeRes(BaseModel):
    content: str
    name: str


class ProductPreviewRes(ProductAutocomplete):
    model_config = ConfigDict(from_attributes=True)

    article_number: str
    thumbnail_link: str | None
    price_net: Decimal | None
    price_gross: Decimal | None
    favorite: bool | None
    stock_status: Stock
    stock_quantity: float | None
    available_from: date | None
    product_attributes: list[AttributeRes] = Field(default=[])


class GroupedAttributeRes(BaseModel):
    grouped_attributes: list[AttributeRes]
    group_id: int
    group: str


class ImageRes(BaseModel):
    url: str
    type: AssetType | None = None  # property unused but retained for API backward compatibility


class AssetRes(BaseModel):
    id: UUID4
    file_type: str
    url: HttpUrl
    type: AssetType
    size: float


class AssetGroupRes(BaseModel):
    asset_files: list[AssetRes]
    file_name: str
    asset_type: str | None


class RelatedToRes(BaseModel):
    product: ProductPreviewRes
    name: str


class ProductDetailsRes(ProductPreviewRes):
    description: ProductDescription | None
    brand_name: str
    product_images: list[ImageRes]
    attributes: list[GroupedAttributeRes]
    assets: list[AssetGroupRes]
    alternative_products: list[RelatedToRes] = Field(default=[])
    combined_with: list[RelatedToRes] = Field(default=[])


class ProductsRes(BaseModel):
    products: list[ProductPreviewRes]
    properties: ResponseProperties


class ProductsWithFiltersRes(ProductsRes):
    filters: list[Facets | Filter]

    def json(self, *args: Any, **kwargs: Any) -> str:
        return super().json(*args, by_alias=False, **kwargs)


class AutocompleteRes(BaseModel):
    products: list[ProductAutocomplete]
    suggestions: list[str]


class SearchFiltersReq(BaseModel):
    filters: dict

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "filters": {
                        "filter-urlkey": "filter value (examples below)",
                        "material": "stainless steel",
                        "max-working-pressure-bar-mpa": "144 / 14.4",
                        "stroke-mm": "100-150",
                    }
                }
            ]
        }
    }


class ParamsReq(BaseModel):
    search_query: str | None = Field(default=None, description="Refers to the search query")
    category_path: str | None = Field(default=None, description="Refers to the category_path")
    page_number: int = Field(default=1, description="Refers to the page number")
    products_per_page: int = Field(default=12, description="Refers to the quantity of products per page")
    filter_template_id: int | None = Field(default=None, description="Refers to the filter template id")
    category_id: int | None = Field(
        default=None, description="Refers to the category id associated to the category/group of products requested"
    )


class SearchParamsReq(ParamsReq):
    # to use when search query is required
    search_query: str


class LanguageRes(BaseModel):
    code: str

    class Config:
        from_attributes = True


class ProfileRes(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    name: str | None
    id: uuid.UUID
    email: EmailStr | None
    last_name: str | None
    language: LanguageRes
    currency: Currency | None
    categories: list[SyncforceCategory]
    system_type: SystemType
