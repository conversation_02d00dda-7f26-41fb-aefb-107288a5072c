from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field


class QuoteItem(BaseModel):
    quantity: str
    product_name: str
    product_nr: str
    availability: str
    unit_price: Optional[str]
    discount: Optional[str]
    net_price: Optional[str]
    line_total: Optional[str]


class ItemHeader(BaseModel):
    quantity: Optional[str] = Field(default="Qty")
    product_name: Optional[str] = Field(default="Article")
    product_name_sub: Optional[str] = Field(default="Article nr.")
    availability: Optional[str] = Field(default="Availability")
    availability_sub: Optional[str] = Field(default="on quotation date")
    unit_price: Optional[str] = Field(default="Unit Price")
    discount: Optional[str] = Field(default="Discount")
    net_price: Optional[str] = Field(default="Net Price")
    line_total: Optional[str] = Field(default="Line Total")


class QuoteSubtotal(BaseModel):
    subtotal_label: Optional[str] = Field(default="Subtotal")
    currency_label: Optional[str] = Field(default="EUR")
    currency_sign: Optional[str] = Field(default="EUR")  # Euro sign
    subtotal_value: Optional[str]


class Quote(QuoteSubtotal):
    title: Optional[str] = Field(default="Quotation")
    quotation_number_prefix: Optional[str] = Field(default="Nr.")
    quotation_number: str
    reference_label: Optional[str] = Field(default="Reference")
    reference_value: str
    date_label: Optional[str] = Field(default="Date")
    date_value: str
    details_label: Optional[str] = Field(default="Details")
    details_company: str
    details_name: str
    details_email: str
    validity_label: Optional[str] = Field(default="Validity")
    validity_value: Optional[str] = Field(default="This offer is valid for 45 days.")
    conditions_label: Optional[str] = Field(default="Conditions")
    conditions_value: Optional[str] = Field(
        default="Delivery according to our general conditions as lodged with the Chamber of Commerce. The delivery time mentioned is an indication only from which no rights can be derived. Payment term as agreed in Holmatro Partner approach plan."
    )
    iso_label: Optional[str] = Field(default="ISO 9001")
    iso_value: Optional[str] = Field(
        default="Holmatro delivers products that are produced in an organization that works in conformity with the ISO 9001 quality management system."
    )


class QuoteData(BaseModel):
    quote: Optional[Quote]
    item_header: Optional[ItemHeader]
    quote_items: Optional[List[QuoteItem]]


class QuoteStock(Enum):
    NO_STOCK = "Out of stock"
    LOW_STOCK = "Low stock"
    IN_STOCK = "Available"
    UNKNOWN = "On request"
