from datetime import datetime
from enum import Enum
from uuid import UUID

from _decimal import Decimal
from pydantic import BaseModel as PydanticBaseModel
from pydantic import ConfigDict, Field

from holmatro_customer_portal.schemas.response_schema import ProductPreviewRes


class BaseModel(PydanticBaseModel):
    model_config = ConfigDict(extra="forbid")


class Stage(str, Enum):
    INDUSTRIAL_LIFTING_CYLINDER = "industrial.lifting.cylinder"
    INDUSTRIAL_LIFTING_PUMP = "industrial.lifting.pump"
    INDUSTRIAL_LIFTING_SYSTEM_COMPONENTS = "industrial.lifting.system_components"
    INDUSTRIAL_OVERVIEW = "industrial.overview"
    INDUSTRIAL_CUTTING_CUTTER = "industrial.cutting.cutter"
    INDUSTRIAL_CUTTING_HOSE = "industrial.cutting.hose"
    INDUSTRIAL_CUTTING_PUMP = "industrial.cutting.pump"
    INDUSTRIAL_CUTTING_ACCESSORIES = "industrial.cutting.accessories"


class StateType(str, Enum):
    SETTINGS = "settings"
    PRODUCTLIST = "productlist"
    OVERVIEW = "overview"


class ContentType(str, Enum):
    CHOICE = "choice"
    INPUT = "input"
    PRODUCTS = "products"
    FILTERS = "filters"
    OVERVIEW = "overview"


class ConditionType(str, Enum):
    EQUALS = "equals"
    GREATER_THAN = "greater_than"
    LESS_THAN = "less_than"


class Condition(BaseModel):
    type: ConditionType = Field(description="Type of condition e.g. equals, greater_than, less_than")
    field: str = Field(description="Field to check the condition against")
    value: str | int = Field(description="Value to check the condition against")


class MultiConditionType(str, Enum):
    AND = "and"
    OR = "or"


class MultiCondition(BaseModel):
    type: MultiConditionType = Field(description="Type of condition e.g. and, or")
    conditions: list[Condition] = Field(description="List of conditions to check")


class Choice(BaseModel):
    title: str | None = Field(default=None, description="Translation key for the title of the choice")
    img: str | None = Field(default=None, description="Image to show for the choice")
    value: str | int = Field(description="Value of the choice")


class Range(BaseModel):
    min: int | None = Field(default=None, description="Minimum value for the input")
    max: int | None = Field(default=None, description="Maximum value for the input")
    default: int | None = Field(default=None, description="Default value for the input")


class MetaType(str, Enum):
    BUTTON = "button"
    RADIO = "radio"
    INPUT = "input"
    DROPDOWN = "dropdown"
    INTEGER = "integer"


class Unit(str, Enum):
    TONNE = "tonne"
    KG = "kg"
    MM = "mm"


class OverviewPrice(BaseModel):
    configuration: Decimal | None = Field(default=None)
    accessories: Decimal | None = Field(default=None)


class ConfigurationRes(BaseModel):
    configuration_id: UUID
    category_id: int
    started_at: datetime
    quotation_number: str | None
    reference: str
    progress: int
    tags: list[str]


class OverviewDetails(ConfigurationRes):
    price_net: OverviewPrice
    price_gross: OverviewPrice


class ConfiguratorProductRes(ProductPreviewRes):
    quantity: int
    total_price_net: Decimal | None
    total_price_gross: Decimal | None


class Meta(BaseModel):
    type: MetaType | None = Field(default=None, description="Type of meta data e.g. button, integer")
    choices: list[Choice] = Field(default=[], description="List of choices for the meta data")
    placeholder: str | None = Field(
        default=None, description="Translation key for the placeholder of the meta data if meta type is input field"
    )
    label: str | None = Field(default=None, description="Translation key for the label of the meta data")
    range: Range | None = Field(default=None, description="Range for the meta data if meta type is integer")
    options: list[str | int] = Field(
        default=[], description="List of options for the meta data if meta type is dropdown"
    )
    initial: str | int | None = Field(default=None, description="Initial value to set")
    maximum: int | None = Field(default=None, description="Maximum quantity to set")
    unit: Unit | None = Field(
        default=None, description="Translation key for the unit of the meta data if meta type is integer"
    )
    products: list[ProductPreviewRes | ConfiguratorProductRes] = Field(
        default=[], description="List of products if content type is productlist"
    )
    multi_select: bool = Field(default=False, description="True if multi selection of products is allowed")
    default_product: ProductPreviewRes | None = Field(
        default=None, description="A product use as default if content type is productlist"
    )
    filters: dict | None = Field(default=None, description="List of filters to use for a product selection")
    category: str | None = Field(default=None, description="Category to use for product selection")
    overview: OverviewDetails | None = Field(default=None, description="Schema with overview pricing information")


class Content(BaseModel):
    id: str = Field(description="Unique identifier for the content item")
    title: str | None = Field(default=None, description="Translation key for the title of the content item")
    subtitle: str | None = Field(default=None, description="Translation key for the subtitle of the content item")
    type: ContentType | None = Field(
        default=None, description="Type of content item e.g. choice, input. None if no interaction is required"
    )
    condition: Condition | MultiCondition | None = Field(
        default=None, description="Condition for the content item to be shown"
    )
    meta: Meta | None = Field(default=None, description="Meta data for the content item")


class State(BaseModel):
    stage: Stage = Field(description="Configurator stage this state belongs to e.g. cylinder, pump, system_components")
    type: StateType = Field(description="Type of state e.g. settings, product_list, overview")
    id: str = Field(description="Unique identifier for the state")
    title: str = Field(description="Translation key for the title of the state")
    subtitle: str | None = Field(default=None, description="Translation key for the subtitle of the state")
    icon: str | None = Field(description="Url for the icon of the state")
    content: list[Content] = Field(description="List of content items for the state view")


class ConfiguratorData(BaseModel):
    attribute_id: str
    value: str | int


class ConfiguratorProduct(BaseModel):
    product_id: UUID | None
    quantity: int
    article_number: str | None = Field(default=None)


class ConfiguratorStateProducts(BaseModel):
    state_id: str
    products: list[ConfiguratorProduct] = Field(default=[])


class ConfigurationStateReq(ConfiguratorStateProducts):
    configuration_id: str
    configuration_data: list[ConfiguratorData]


class ConfigurationStateRes(BaseModel):
    configuration_id: str
    category_id: int
    state_definition: State
    configuration_data: list[ConfiguratorData]
    products: list[ConfiguratorProductRes] = Field(default=[])


class ConfigurationInitReq(BaseModel):
    reference: str
    category: int | None = Field(default=None)
