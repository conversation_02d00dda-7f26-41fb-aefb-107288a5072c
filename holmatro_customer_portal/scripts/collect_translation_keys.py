import glob
import json


def get_translation_keys(data: dict, keys: list | None = None) -> list[str]:
    if not keys:
        keys = []
    for key, value in data.items():
        if isinstance(value, dict):
            get_translation_keys(value, keys)
        elif isinstance(value, list):
            for item in value:
                if isinstance(item, dict):
                    get_translation_keys(item, keys)
        elif isinstance(value, str) and key in ["title", "subtitle", "label", "placeholder"] and value is not None:
            keys.append(value)
    return keys


def collect_translation_keys_from_files(files: list[str]) -> list[str]:
    translation_keys = []
    for file in files:
        with open(file, "r") as f:
            data = json.load(f)
            translation_keys.extend(get_translation_keys(data))
    return translation_keys


if __name__ == "__main__":
    """Collects all translation keys from the states files and saves them to translation_keys.json."""
    files = glob.glob(r"holmatro_customer_portal/services/configurator/states/definitions/*.json")
    translation_keys = collect_translation_keys_from_files(files)

    json.dump(translation_keys, open("translation_keys.json", "w"), indent=4)
    print(f"Found {len(translation_keys)} translation keys. Saved to translation_keys.json.")
