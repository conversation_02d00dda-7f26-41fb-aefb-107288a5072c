from sqlalchemy import select

from holmatro_customer_portal.database.enums import DBFilterType, DBMultiSelectLogic, SystemType
from holmatro_customer_portal.database.models import Category, CategoryTranslation, DBFilter, DBFilterTemplate, Language
from holmatro_customer_portal.utils.database import Session, construct_db_write_url, session_creator


def import_filters_and_templates() -> None:
    """
    Inserts filter templates and their associated filters into the database.
    To call this function in a remote deployment, follow these steps:
    1. Start a shell session on the backend deployment for the Holmatro Customer Portal.
    2. Start a Python shell with the command: `python`.
    3. Call the function using Typer: `typer holmatro_customer_portal/cli.py run reimport-filters`
    The function can be safely called multiple times, as it truncates the existing filter templates and filters tables
    before inserting new data.
    """
    db = session_creator(construct_db_write_url())()

    # Fetch the Cutting and Lifting categories that the filter templates will be associated with.
    cutting_category = _category_for_name(db, "Cutting")
    lifting_category = _category_for_name(db, "Lifting")

    # Start with a clean slate by truncating existing filter templates and filters tables.
    # The category-template association table is automatically handled by SQLAlchemy.
    db.query(DBFilter).delete()
    db.query(DBFilterTemplate).delete()

    # Lifting Metric template
    db.add(
        DBFilterTemplate(
            name="PP - HHS - Lifting Metric",
            system_type=SystemType.METRIC,
            categories=[lifting_category],
            filters=[
                # Tonnage (t)
                _slider_filter(attribute_id=98, position=0),
                # Stroke (mm)
                _slider_filter(attribute_id=100, position=1),
                # Closed height (mm)
                _slider_filter(attribute_id=102, position=2),
                # Acting type
                _checkbox_filter(attribute_id=359, position=3),
                # Return type
                _checkbox_filter(attribute_id=361, position=4),
                # Material
                _checkbox_filter(attribute_id=362, position=5),
                # Cylinder type
                _checkbox_filter(attribute_id=363, position=6),
                # Required oil content (effective) (cc)
                _slider_filter(attribute_id=357, position=7),
                # Operator type
                _checkbox_filter(attribute_id=358, position=8),
                # Engine
                _checkbox_filter(attribute_id=385, position=9),
                # Capacity oil tank (effective) (cc)
                _slider_filter(attribute_id=384, position=10),
                # First stage output / min. (cc)
                _slider_filter(attribute_id=175, position=11),
                # Second stage output / min. (cc)
                _slider_filter(attribute_id=183, position=12),
                # Max. working pressure (bar)
                _checkbox_filter(attribute_id=4, position=13),
                # Number of outlet ports
                _checkbox_filter(attribute_id=294, position=14),
                # TODO: insert Hose type filter here once attribute is available
                # Length (m)
                _checkbox_filter(attribute_id=598, position=15),
                # Hose connection A
                _checkbox_filter(attribute_id=407, position=16),
                # Hose connection B
                _checkbox_filter(attribute_id=408, position=17),
            ],
        )
    )

    # Lifting Imperial template
    db.add(
        DBFilterTemplate(
            name="PP - HHS - Lifting Imperial",
            system_type=SystemType.IMPERIAL,
            categories=[lifting_category],
            filters=[
                # Tonnage (t)
                _slider_filter(attribute_id=98, position=0),
                # Stroke (in)
                _slider_filter(attribute_id=101, position=1),
                # Closed height (in)
                _slider_filter(attribute_id=103, position=2),
                # Acting type
                _checkbox_filter(attribute_id=359, position=3),
                # Return type
                _checkbox_filter(attribute_id=361, position=4),
                # Material
                _checkbox_filter(attribute_id=362, position=5),
                # Cylinder type
                _checkbox_filter(attribute_id=363, position=6),
                # Required oil content (effective) (oz)
                _slider_filter(attribute_id=581, position=7),
                # Operator type
                _checkbox_filter(attribute_id=358, position=8),
                # Engine
                _checkbox_filter(attribute_id=385, position=9),
                # Capacity oil tank (effective) (oz)
                _slider_filter(attribute_id=585, position=10),
                # First stage output / min. (oz)
                _slider_filter(attribute_id=574, position=11),
                # Second stage output / min. (oz)
                _slider_filter(attribute_id=576, position=12),
                # Max. working pressure (psi)
                _checkbox_filter(attribute_id=6, position=13),
                # Number of outlet ports
                _checkbox_filter(attribute_id=294, position=14),
                # TODO: insert Hose type filter here once attribute is available
                # Length (ft)
                _checkbox_filter(attribute_id=599, position=15),
                # Hose connection A
                _checkbox_filter(attribute_id=407, position=16),
                # Hose connection B
                _checkbox_filter(attribute_id=408, position=17),
            ],
        )
    )

    # Cutting Metric template
    db.add(
        DBFilterTemplate(
            name="PP - HHS - Cutting Metric",
            system_type=SystemType.METRIC,
            categories=[cutting_category],
            filters=[
                # TODO: insert blade type filter here once attribute is available
                # Number of cuts per day
                _slider_filter(attribute_id=1516, position=1),
                # Number of operating hours per day
                _slider_filter(attribute_id=1518, position=2),
                # Max. cutting diameter round bar (S235 acc. To EN 10025-2) (mm)
                _slider_filter(attribute_id=1499, position=3),
                # Max. cutting diameter round tube (E235 acc. To EN 10305-1), based on wall thickness 2mm/0,08in (mm)
                _slider_filter(attribute_id=1502, position=4),
                # Max. cutting diameter power cable (mm)
                _slider_filter(attribute_id=1511, position=5),
                # Catalytic converter (%)
                _slider_filter(attribute_id=1514, position=6),
                # Cutter type
                _checkbox_filter(attribute_id=8, position=7),
                # Max. cutting opening (mm)
                _slider_filter(attribute_id=70, position=8),
                # For cutter type
                _checkbox_filter(attribute_id=9, position=9),
                # Length (m)
                _checkbox_filter(attribute_id=598, position=10),
                # Engine
                _checkbox_filter(attribute_id=385, position=11),
                # Number of stages
                _checkbox_filter(attribute_id=168, position=12),
                # First stage output / min. (cc)
                _slider_filter(attribute_id=175, position=13),
                # Second stage output / min. (cc)
                _slider_filter(attribute_id=183, position=14),
                # Max working pressure (bar)
                _checkbox_filter(attribute_id=600, position=15),
            ],
        )
    )

    # Cutting Imperial template
    db.add(
        DBFilterTemplate(
            name="PP - HHS - Cutting Imperial",
            system_type=SystemType.IMPERIAL,
            categories=[cutting_category],
            filters=[
                # TODO: insert blade type filter here once attribute is available
                # Number of cuts per day
                _slider_filter(attribute_id=1516, position=1),
                # Number of operating hours per day
                _slider_filter(attribute_id=1518, position=2),
                # Max. cutting diameter round bar (S235 acc. To EN 10025-2) (in)
                _slider_filter(attribute_id=1500, position=3),
                # Max. cutting diameter round tube (E235 acc. To EN 10305-1), based on wall thickness 2mm/0,08in (in)
                _slider_filter(attribute_id=1503, position=4),
                # Max. cutting diameter power cable (in)
                _slider_filter(attribute_id=1512, position=5),
                # Catalytic converter (%)
                _slider_filter(attribute_id=1514, position=6),
                # Cutter type
                _checkbox_filter(attribute_id=8, position=7),
                # Max. cutting opening (in)
                _slider_filter(attribute_id=71, position=8),
                # For cutter type
                _checkbox_filter(attribute_id=9, position=9),
                # Length (ft)
                _checkbox_filter(attribute_id=599, position=10),
                # Engine
                _checkbox_filter(attribute_id=385, position=11),
                # Number of stages
                _checkbox_filter(attribute_id=168, position=12),
                # First stage output / min. (oz)
                _slider_filter(attribute_id=574, position=13),
                # Second stage output / min. (oz)
                _slider_filter(attribute_id=576, position=14),
                # Max working pressure (bar)
                _checkbox_filter(attribute_id=600, position=15),
            ],
        )
    )

    db.commit()


def _slider_filter(attribute_id: int, position: int) -> DBFilter:
    return DBFilter(
        attribute_id=attribute_id,
        filter_type=DBFilterType.SLIDER,
        position=position,
        multi_select_logic=None,
    )


def _checkbox_filter(attribute_id: int, position: int) -> DBFilter:
    return DBFilter(
        attribute_id=attribute_id,
        filter_type=DBFilterType.CHECKBOX,
        position=position,
        multi_select_logic=DBMultiSelectLogic.OR,
    )


def _category_for_name(db: Session, name: str) -> Category:
    """Helper function to retrieve the English category by its name."""
    stmt = (
        select(Category)
        .join(Category.translations)
        .join(CategoryTranslation.language)
        .filter(CategoryTranslation.name == name)
        .filter(Language.language_code == "en")
    )
    return db.scalars(stmt).one()
