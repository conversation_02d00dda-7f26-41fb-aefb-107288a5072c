import csv
import re
from pathlib import Path
from typing import Dict, Set

from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class SynonymProcessor:
    """Processes synonym CSV files and creates lookup mappings."""

    def __init__(self) -> None:
        self.synonym_lookup: Dict[str, Set[str]] = {}

    def load_synonyms_from_csv(self, csv_file_path: str | None = None) -> Dict[str, Set[str]]:
        """
        Load synonyms from CSV file and return lookup dictionary.

        Args:
            csv_file_path: Path to the CSV file containing synonym mappings.
                          If None, defaults to synonyms.csv in the scripts directory.

        Returns:
            Dict mapping article numbers to sets of search terms

        Expected CSV format:
        volgorde;zoekwoord;eigenschapId;redirect (art.no);categorieid;RedirectTypeId
        1;RC53;;100112213;;0
        """
        if csv_file_path is None:
            # Default to synonyms.csv in the same directory as this script
            script_dir = Path(__file__).parent
            csv_path = script_dir / "synonyms.csv"
        else:
            csv_path = Path(csv_file_path)
        if not csv_path.exists():
            _logger.info(f"Synonym CSV file not found: {csv_path}, skipping synonym loading")
            return {}

        _logger.info(f"Loading synonyms from: {csv_path}")
        synonym_count = 0
        self.synonym_lookup = {}

        try:
            with open(csv_path, "r", encoding="utf-8") as file:
                reader = csv.DictReader(file, delimiter=";")

                for row in reader:
                    search_term = row.get("zoekwoord", "").strip()
                    article_number_raw = row.get("redirect (art.no)", "").strip()

                    if not search_term or not article_number_raw:
                        continue

                    # Transform article number to correct format (add dots)
                    article_number = self._transform_article_number(article_number_raw)

                    if article_number:
                        if article_number not in self.synonym_lookup:
                            self.synonym_lookup[article_number] = set()

                        self.synonym_lookup[article_number].add(search_term.lower())
                        synonym_count += 1
                        _logger.debug(f"Added synonym: {search_term} -> {article_number}")

            _logger.info(f"Loaded {synonym_count} synonyms for {len(self.synonym_lookup)} products")

        except Exception as e:
            _logger.error(f"Error loading synonyms from CSV: {str(e)}")

        return self.synonym_lookup

    @staticmethod
    def _transform_article_number(article_number: str) -> str | None:
        """
        Transform article number to correct format with dots.

        Args:
            article_number: Raw article number (e.g., "100112213")

        Returns:
            Formatted article number (e.g., "100.112.213") or None if invalid
        """
        # Remove any existing dots and validate format
        clean_number = article_number.replace(".", "")

        if not re.match(r"^\d{9}$", clean_number):
            _logger.warning(f"Invalid article number format: {article_number}")
            return None

        # Insert dots every three digits
        formatted = ".".join([clean_number[i : i + 3] for i in range(0, len(clean_number), 3)])
        return formatted
