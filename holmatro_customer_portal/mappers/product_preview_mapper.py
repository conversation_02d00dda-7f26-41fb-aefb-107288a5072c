from datetime import datetime, timezone
from uuid import UUID

from sqlalchemy import and_, func
from sqlalchemy.engine import Row

from holmatro_customer_portal.database.enums import RelationshipType, TextType
from holmatro_customer_portal.database.models import User, favorite_products_association_table
from holmatro_customer_portal.schemas.configurator import ConfiguratorProductRes
from holmatro_customer_portal.schemas.my_hm_api_schema import MYHMPrice, MYHMProductInfo, MYHMStock
from holmatro_customer_portal.schemas.response_schema import (
    ProductDescription,
    ProductDetailsRes,
    ProductPreviewRes,
    Stock,
)
from holmatro_customer_portal.services.get_attributes import get_attributes, get_product_preview_attributes
from holmatro_customer_portal.services.get_categories import map_article_to_categories
from holmatro_customer_portal.services.get_related_products import get_related_products_with_translation
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.my_hm.my_hm_interface import MyHolmatroClientInterface


class ProductPreviewMapper:
    def __init__(self, db: Session, my_hm_client: MyHolmatroClientInterface):
        self.db = db
        self.my_hm_client = my_hm_client
        self.article_attribute_ids: dict = {}

    def product_preview_mapper(self, user: User, products: list[Row]) -> list[ProductPreviewRes]:
        self.article_attribute_ids = map_article_to_categories(products, self.db)
        product_sales_info: dict[str, MYHMProductInfo] = self._get_sales_info(user, products)
        return [
            ProductPreviewRes(
                **self._map_product(user, product, product_sales_info.get(product.product_article_number))
            )
            for product in products
        ]

    def configurator_product_preview_mapper(self, user: User, products: list[Row]) -> list[ConfiguratorProductRes]:
        self.article_attribute_ids = map_article_to_categories(products, self.db)
        product_sales_info: dict[str, MYHMProductInfo] = self._get_sales_info(user, products)
        mapped_products = []
        for product in products:
            p = self._map_product(user, product, product_sales_info[product.product_article_number])

            # Used in the configurator overview
            if product_sales_info and hasattr(product, "quantity"):
                self._add_quantity_info(p, product_sales_info[product.product_article_number], product.quantity)
            mapped_products.append(ConfiguratorProductRes.model_validate(p))

        return mapped_products

    def product_details_mapper(self, user: User, products: list[Row]) -> ProductDetailsRes:
        from holmatro_customer_portal.services.utils.queries import get_product_assets, get_product_images

        # Query returns a joined Product and ProductTextTranslation table with a list of products
        # It contains one or many of the same product depending on how many descriptions texts are linked to the product
        main_product = products[0]
        product_sales_info: dict[str, MYHMProductInfo] = self._get_sales_info(user, [main_product])

        product = self._map_product(user, main_product, product_sales_info[main_product.Product.article_number])
        product.update(
            {
                "description": self._get_descriptions(products),
                "product_images": get_product_images(self.db, main_product.Product.id),
                "brand_name": main_product.Product.brand.brand_name,
                "assets": get_product_assets(self.db, main_product.Product.id, user.language.id),
                "attributes": get_attributes(
                    self.db, main_product.Product.id, user, main_product.Product.article_number
                ),
                "alternative_products": get_related_products_with_translation(
                    main_product.Product.id,
                    user,
                    self.db,
                    self.my_hm_client,
                    RelationshipType.ALTERNATIVE_PRODUCTS.value,
                ),
                "combined_with": get_related_products_with_translation(
                    main_product.Product.id, user, self.db, self.my_hm_client, RelationshipType.COMBINED_WITH.value
                ),
                "last_modified": datetime.now(timezone.utc),
            }
        )

        return ProductDetailsRes(**product)

    def is_product_favorite(self, user: User, product_id: UUID) -> bool:
        return (  # type: ignore
            self.db.query(func.count())
            .filter(
                and_(
                    favorite_products_association_table.c.product_id == product_id,
                    favorite_products_association_table.c.user_id == user.id,
                )
            )
            .scalar()
            == 1
        )

    @staticmethod
    def _get_descriptions(products: list[Row]) -> ProductDescription | None:
        if not products[0].ProductTextTranslation:
            return None
        else:
            descriptions_data: dict = {text_type.value: [] for text_type in TextType}
            for row in products:
                # The row consist in Product model, ProductTextTranslation, product_name, product_master_name
                description_type = TextType(row.ProductTextTranslation.type)
                description_value = row.ProductTextTranslation.value

                descriptions_data[description_type.value].append(description_value)

            return ProductDescription(**descriptions_data)

    def _map_product(
        self,
        user: User,
        product: Row,
        product_sales_info: MYHMProductInfo | None,
    ) -> dict:
        """It maps a product considering 2 different queries and its labels"""
        article_number = (
            product.product_article_number
            if hasattr(product, "product_article_number")
            else product.Product.article_number
        )

        product_id = product.product_id if hasattr(product, "product_id") else product.Product.id
        article_attributes = self.article_attribute_ids.get(article_number)

        product_attributes = (
            get_product_preview_attributes(
                article_attributes,
                user,
                self.db,
                article_number,
            )
            if article_attributes
            else []
        )

        mapped_product = {
            "product_id": product_id,
            "article_number": article_number,
            "name": product.product_name,
            "thumbnail_link": product.asset_url if hasattr(product, "asset_url") else None,
            "favorite": self.is_product_favorite(user, product_id),  # type: ignore
            "price_net": product_sales_info.nett_price if product_sales_info else None,
            "price_gross": product_sales_info.gross_price if product_sales_info else None,
            "stock_status": product_sales_info.stock_status if product_sales_info else Stock.ON_REQUEST,
            "available_from": product_sales_info.available_from if product_sales_info else None,
            "stock_quantity": product_sales_info.stock_quantity if product_sales_info else None,
            "product_attributes": product_attributes,
            "category_id": product.category_id if hasattr(product, "category_id") else None,
        }

        return mapped_product

    @staticmethod
    def _add_quantity_info(mapped_product: dict, product_sales_info: MYHMProductInfo, quantity: int) -> None:
        mapped_product.update(
            {
                "total_price_net": product_sales_info.nett_price * quantity if product_sales_info.nett_price else None,
                "total_price_gross": (
                    product_sales_info.gross_price * quantity if product_sales_info.gross_price else None
                ),
                "quantity": quantity,
            }
        )

    def _get_sales_info(self, user: User, products: list[Row]) -> dict[str, MYHMProductInfo]:
        # FIX it: convert query answers into a schema, so it won't be need to try different schemas
        prices = [product.product_article_number for product in products if hasattr(product, "product_article_number")]
        if not prices:
            prices = [product.Product.article_number for product in products if hasattr(product, "Product")]

        products_sales_info = self.my_hm_client.get_sales_info(prices, user.currency)

        # Map products sales info to their article numbers for easy manipulation
        return {product_info.article_no: product_info for product_info in products_sales_info}

    def _get_product_sales_prices(self, user: User, article_number: str) -> MYHMPrice:
        return self.my_hm_client.get_product_sales_prices(article_number, user.currency)

    def _get_stock(self, article_number: str) -> MYHMStock:
        return self.my_hm_client.get_stock(article_number)
