import datetime
import re
import uuid
from typing import <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>V<PERSON>, Union

from sqlalchemy.orm import Session

from holmatro_customer_portal.database.enums import AssetType, SystemType
from holmatro_customer_portal.database.models import (
    Asset,
    AssetMetadataTranslations,
    AssetResource,
    Attribute,
    AttributeTranslation,
    Brand,
    Category,
    CategoryAssociation,
    CategoryTranslation,
    Classification,
    ClassificationTranslation,
    HierarchyLevel,
    HierarchyLevelTranslation,
    Language,
    MasterProductNameTranslation,
)
from holmatro_customer_portal.database.models import Product as DBProduct
from holmatro_customer_portal.database.models import (
    ProductNameTranslation,
    ProductText,
    ProductTextTranslation,
    RelatedProduct,
    RelationshipTranslation,
    TargetGroup,
    TextType,
)
from holmatro_customer_portal.schemas.syncforce_product_schema import Asset as SFAsset
from holmatro_customer_portal.schemas.syncforce_product_schema import AssetDetails as SFAssetDetails
from holmatro_customer_portal.schemas.syncforce_product_schema import AssetRelationship as SFAssetRelationship
from holmatro_customer_portal.schemas.syncforce_product_schema import Asset<PERSON>ole as SFAssetRole
from holmatro_customer_portal.schemas.syncforce_product_schema import AssetSubType as SFAssetSubType
from holmatro_customer_portal.schemas.syncforce_product_schema import AssetType as SFAssetType
from holmatro_customer_portal.schemas.syncforce_product_schema import AssetTypeTranslation as SFAssetTypeTranslation
from holmatro_customer_portal.schemas.syncforce_product_schema import AttributeGroup
from holmatro_customer_portal.schemas.syncforce_product_schema import AttributeGroup as SFAssetAttributeGroup
from holmatro_customer_portal.schemas.syncforce_product_schema import AttributeUnitType as SFAttributeUnitType
from holmatro_customer_portal.schemas.syncforce_product_schema import Brand as SFBrand
from holmatro_customer_portal.schemas.syncforce_product_schema import Category as SFCategory
from holmatro_customer_portal.schemas.syncforce_product_schema import CategoryTranslation as SFcategoryTranslation
from holmatro_customer_portal.schemas.syncforce_product_schema import Classification as SFClassification
from holmatro_customer_portal.schemas.syncforce_product_schema import (
    ClassificationTranslation as SFClassificationTranslation,
)
from holmatro_customer_portal.schemas.syncforce_product_schema import HierarchyLevel as SFHierarchyLevel
from holmatro_customer_portal.schemas.syncforce_product_schema import (
    HierarchyLevelTranslation as SFHierarchyLevelTranslation,
)
from holmatro_customer_portal.schemas.syncforce_product_schema import Language as SFLanguage
from holmatro_customer_portal.schemas.syncforce_product_schema import (
    MasterProductNameTranslation as SFMasterProductNameTranslation,
)
from holmatro_customer_portal.schemas.syncforce_product_schema import Product as SFProduct
from holmatro_customer_portal.schemas.syncforce_product_schema import ProductNameTranslation as SFProductNameTranslation
from holmatro_customer_portal.schemas.syncforce_product_schema import ProductRelationRole as SFProductRelationRole
from holmatro_customer_portal.schemas.syncforce_product_schema import ProductRelations as SFProductRelations
from holmatro_customer_portal.schemas.syncforce_product_schema import (
    ProductRelationTranslation as SFProductRelationTranslation,
)
from holmatro_customer_portal.schemas.syncforce_product_schema import ProductText as SFProductText
from holmatro_customer_portal.schemas.syncforce_product_schema import SegmentationTargetGroup as SFTargetGroup
from holmatro_customer_portal.schemas.syncforce_product_schema import SingleAttribute as SFAttribute
from holmatro_customer_portal.utils.database import DatabaseOperations
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.storage_utils import load_assets
from holmatro_customer_portal.utils.transforms import convert_dict_keys_to_snake

AssetModel = TypeVar("AssetModel", bound=Union[SFAssetSubType, SFAssetType])
TranslationModel = TypeVar("TranslationModel", bound=Union[SFProductRelationTranslation, SFAssetTypeTranslation])


class ProductAttributesMapper:
    def __init__(self, db_ops: DatabaseOperations, categories_data: dict, process_assets: bool = True):
        self.db_ops = db_ops
        self.categories_data = categories_data
        self.language_cache: dict[str, Language] = {}
        self.category_cache: dict[uuid.UUID, Category] = {}
        self.process_assets = process_assets

    def map_classification_translations(
        self, translations: list[SFClassificationTranslation]
    ) -> list[ClassificationTranslation]:
        return [
            ClassificationTranslation(language=self.upsert_language(translation.language), name=translation.name)
            for translation in translations
        ]

    def upsert_classification(self, classification: SFClassification) -> Classification:
        translations = self.map_classification_translations(classification.translations)
        return self.db_ops.upsert(
            Classification,
            filter_by={"classification_id": classification.id},
            material_type=classification.material_type,
            classification_id=classification.id,
            translations=translations,
        )

    def map_product_texts(self, texts: Optional[list[SFProductText]]) -> list[ProductText]:
        if not texts:
            return []

        def map_text_type(text_id: int) -> TextType:
            match text_id:
                case 41:
                    return TextType.long_description
                case 42 | 43 | 44 | 45 | 46 | 47 | 48 | 49:
                    return TextType.features
                case 60:
                    return TextType.supplied_with
                case 61:
                    return TextType.available_on_request
                case 62:
                    return TextType.additional_information
                case 1023:
                    return TextType.accessories
                case _:
                    return TextType.other

        product_texts = []
        text_type = None
        count = 1

        for text in texts:
            current_text_type = map_text_type(text.id)

            if current_text_type == text_type:
                count += 1
            else:
                count = 1
                text_type = current_text_type

            product_texts.append(
                ProductText(
                    is_derived=text.is_derived,
                    translations=[
                        ProductTextTranslation(
                            language=self.upsert_language(translation.language),
                            label=translation.label,
                            value=translation.value,
                            type=current_text_type,
                            sort_order=count,
                        )
                        for translation in text.translations
                    ],
                )
            )
        return product_texts

    def map_hierarchy_level_translations(
        self, translations: list[SFHierarchyLevelTranslation]
    ) -> list[HierarchyLevelTranslation]:
        return [
            HierarchyLevelTranslation(language=self.upsert_language(translation.language), translation=translation.name)
            for translation in translations
        ]

    def filter_attribute_group_name(
        self, attribute: SFAttribute, attribute_groups: list[SFAssetAttributeGroup], language: SFLanguage
    ) -> str:
        attribute_group = next(
            (
                attribute_group
                for attribute_group in attribute_groups
                if attribute_group.id == attribute.attribute_group_id
            ),
        )

        return next(
            (
                attribute_group_translation.name
                for attribute_group_translation in attribute_group.translations
                if attribute_group_translation.language == language.language_code
            )
        )

    def get_translated_attribute_value(self, attribute: SFAttribute, language: SFLanguage) -> Optional[str]:
        if attribute.multi_lingual_values and attribute.value and attribute.value.translations:
            return next(
                (avt.content for avt in attribute.value.translations if avt.language == language.language_code), None
            )
        return None

    def get_translated_attribute_unit(
        self, unit_type: Optional[SFAttributeUnitType], language: SFLanguage
    ) -> Optional[str]:
        if unit_type and unit_type.translations:
            return next((aut.content for aut in unit_type.translations if aut.language == language.language_code), None)
        return None

    def map_translated_attributes(
        self,
        attribute: SFAttribute,
        attribute_groups: list[SFAssetAttributeGroup],
        unit_type: Optional[SFAttributeUnitType],
    ) -> list[AttributeTranslation]:
        translated_attributes = []

        for translation in attribute.translations:
            language = self.upsert_language(translation.language)
            name = translation.name
            attribute_group_name = self.filter_attribute_group_name(attribute, attribute_groups, language)
            attribute_value = self.get_translated_attribute_value(attribute, language)
            attribute_unit = (
                self.get_translated_attribute_unit(unit_type, language)
                if attribute.value and attribute.value.unit_id
                else None
            )
            translated_attributes.append(
                AttributeTranslation(
                    language=language,
                    name=name,
                    attribute_group_name=attribute_group_name,
                    attribute_value=attribute_value,
                    attribute_unit=attribute_unit,
                )
            )

        return translated_attributes

    def determine_unit_type(self, attribute: SFAttribute, product: SFProduct) -> Optional[SFAttributeUnitType]:
        if not attribute.multi_lingual_values and attribute.value and product.unit_types:
            return next((au for au in product.unit_types if au.id == attribute.value.unit_id), None)
        return None

    def determine_system_type(
        self, unit_type: Optional[SFAttributeUnitType], metric_units: re.Pattern, imperial_units: re.Pattern
    ) -> Optional[SystemType]:
        if unit_type and unit_type.translations:
            for ut in unit_type.translations:
                if ut.language == "en":
                    if metric_units.search(ut.content):
                        return SystemType.METRIC
                    elif imperial_units.search(ut.content):
                        return SystemType.IMPERIAL
        return None

    def get_attribute_content(self, attribute: SFAttribute) -> Optional[str]:
        return attribute.value.content if attribute.value else None

    def map_attributes(self, attributes: list[SFAttribute], product: SFProduct) -> list[Attribute]:
        result = []
        metric_units = re.compile(r"\b(?:bar|Mpa|mm|kg|cc|rpm|\(bar/Mpa\))\b", re.IGNORECASE)
        imperial_units = re.compile(r"\b(?:psi|lb|in|oz|lbf|ft)\b", re.IGNORECASE)

        for attribute in attributes:
            unit_type = self.determine_unit_type(attribute, product)
            system_type = self.determine_system_type(unit_type, metric_units, imperial_units)
            content = self.get_attribute_content(attribute)
            translated_attributes = self.map_translated_attributes(attribute, product.attribute_groups, unit_type)

            result.append(
                Attribute(
                    type=attribute.type,
                    is_read_only=attribute.is_read_only,
                    system_type=system_type,
                    content=content,
                    attribute_id=attribute.id,
                    attribute_group_id=self.fetch_attribute_group_id(attribute, product.attribute_groups),
                    translations=translated_attributes,
                    multi_lingual_values=attribute.multi_lingual_values,
                    unit_type=unit_type.id if unit_type else None,
                )
            )

        return result

    def fetch_attribute_group_id(self, attribute: SFAttribute, attribute_groups: list[AttributeGroup]) -> int:
        return next(
            (
                attribute_group.id
                for attribute_group in attribute_groups
                if attribute_group.id == attribute.attribute_group_id
            ),
        )

    def upsert_language(self, language_code: str) -> Language:
        if language_code in self.language_cache:
            return self.language_cache[language_code]

        language = self.db_ops.upsert(Language, filter_by={"language_code": language_code}, language_code=language_code)

        self.language_cache[language_code] = language
        return language

    def map_languages(self, languages: list[SFLanguage]) -> list[Language]:
        return [self.upsert_language(lang.language_code) for lang in languages]

    def map_product_name(self, names: list[SFProductNameTranslation]) -> list[ProductNameTranslation]:
        return [
            ProductNameTranslation(language=self.upsert_language(translation.language), value=translation.value)
            for translation in names
        ]

    def map_master_product_name(
        self, master_names: list[SFMasterProductNameTranslation]
    ) -> list[MasterProductNameTranslation]:
        return [
            MasterProductNameTranslation(language=self.upsert_language(translation.language), value=translation.value)
            for translation in master_names
        ]

    def map_hierarchy_level(self, hierarchy_level: SFHierarchyLevel) -> HierarchyLevel:
        return self.db_ops.upsert(
            HierarchyLevel,
            filter_by={"hierarchy_level_id": hierarchy_level.id},
            hierarchy_level_id=hierarchy_level.id,
            translations=self.map_hierarchy_level_translations(hierarchy_level.translations),
        )

    def map_target_group(self, segmentation: list[SFTargetGroup]) -> list[TargetGroup]:
        return [
            self.db_ops.upsert(
                TargetGroup,
                filter_by={"name": tg.name},
                name=tg.name,
                from_date=datetime.datetime.fromisoformat(str(tg.from_date)) if tg.from_date else None,
                until_date=datetime.datetime.fromisoformat(str(tg.until_date)) if tg.until_date else None,
            )
            for tg in segmentation
        ]

    def map_brand(self, brand: SFBrand) -> Brand:
        return self.db_ops.upsert(
            Brand,
            filter_by={"brand_name": brand.brand_name},
            brand_name=brand.brand_name,
            brand_type=brand.brand_type,
            gbin=brand.gbin,
            owner_name=brand.owner_name,
            owner_gln=brand.owner_gln,
            reference=brand.reference,
        )

    def map_assets(self, assets: SFAsset, languages: list[Language]) -> list[Asset]:
        assets_models: list[Asset] = []

        roles_dict = {role.id: role for role in assets.roles}
        types_dict = {item.id: item for item in assets.types}
        sub_types_dict = {item.id: item for item in assets.sub_types}

        if assets.asset is None:
            return assets_models

        for asset in assets.asset:
            filtered_role = roles_dict.get(asset.role_id)
            asset_details, asset_resources = load_assets(asset.udai)
            if asset_details is None:
                continue
            asset_type = types_dict.get(asset_details.type_id)
            asset_sub_type = sub_types_dict.get(asset_details.sub_type_id)
            if filtered_role is None or asset_type is None or asset_sub_type is None or asset.role_id is None:
                continue
            asset_image_type, char_order = self.map_image_type(asset.role_id)
            if asset_image_type is None:
                continue
            imported_asset = Asset(
                udai=uuid.UUID(asset.udai),
                role_id=asset.role_id,
                asset_type=asset_image_type,
                asset_type_id=asset_type.id,
                asset_sub_type_id=asset_sub_type.id,
                asset_id=asset.id,
                char_order=char_order,
                meta_data_translations=self.map_asset_meta_data(
                    asset_sub_type, asset_type, filtered_role, languages, asset_details
                ),
                asset_resources=self.map_asset_resources(asset_details, asset_resources),
            )
            assets_models.append(imported_asset)

        return assets_models

    def map_image_type(self, role_id: int) -> Tuple[Optional[AssetType], Optional[int]]:
        match role_id:
            case 2:
                return AssetType.PRODUCT_TECHNICAL_SPECIFICATION_SHEET, None
            case 3:
                return AssetType.PRODUCT_APPLICATION_SHOT, None
            case 11:
                return AssetType.PRODUCT_DIAGRAM, None
            case 12:
                return AssetType.PRODUCT_TECHNICAL_DRAWING, None
            case 14:
                return AssetType.PRODUCT_SHOTS, None
            case 15:
                return AssetType.PRODUCT_GUIDE, None
            case 51 | 52 | 53 | 54 | 55 | 56 | 57 | 58 | 59:
                return AssetType.PRODUCT_CHARACTERISTICS, int(str(role_id)[1])
            case 1004:
                return AssetType.PRODUCT_MAIN_IMAGE, None
            case 1018:
                return AssetType.PRODUCT_CATEGORY_MAIN_IMAGE, None
            case 1035:
                return AssetType.PRODUCT_APPLICATION_SHOTS_WEB, None
            case _:
                return None, None  # Return a tuple with None for both elements

    def filter_translations_by_language(
        self, model_instance: list[TranslationModel], language: Language
    ) -> Optional[TranslationModel]:
        return next((trans for trans in model_instance if trans.language == language.language_code), None)

    def map_asset_meta_data(
        self,
        asset_sub_types: SFAssetSubType,
        asset_types: SFAssetType,
        filtered_roles: SFAssetRole,
        languages: list[Language],
        asset_details: SFAssetDetails,
    ) -> list[AssetMetadataTranslations]:
        assets_metadata_translations = []

        for language in languages:
            asset_type_translation = self.filter_translations_by_language(asset_types.translations, language)
            asset_sub_types_singular = self.filter_translations_by_language(asset_sub_types.singular, language)
            asset_sub_types_plural = self.filter_translations_by_language(asset_sub_types.plural, language)
            asset_roles = self.filter_translations_by_language(filtered_roles.translations or [], language)
            asset_file = self.filter_translations_by_language(asset_details.translations, language)

            # Ensure all the necessary translations exist before creating the AssetMetadataTranslations
            if asset_type_translation and asset_sub_types_singular and asset_sub_types_plural and asset_roles:
                asset_meta_data = AssetMetadataTranslations(
                    asset_type=asset_type_translation.label,
                    asset_sub_types_singular=asset_sub_types_singular.label,
                    asset_sub_types_plural=asset_sub_types_plural.label,
                    asset_role=asset_roles.label,
                    asset_label=asset_file.label if asset_file else None,
                    language=language,
                )
                assets_metadata_translations.append(asset_meta_data)

        return assets_metadata_translations

    def map_asset_resources(self, asset_details: SFAssetDetails, asset_resources: dict) -> list[AssetResource]:
        asset_resources_list = []
        for key, value in asset_resources.items():
            filtered_file = next((file for file in asset_details.files if file.file_name == key), None)

            if not filtered_file:
                continue
            asset_resource = AssetResource(
                asset_type=filtered_file.type,
                file_name=key,
                url=value["url"],
                size=value["size"],
                languages=(
                    [
                        self.upsert_language(language.language)
                        for language in filtered_file.languages
                        if language.language is not None
                    ]
                    if filtered_file.languages
                    else []
                ),
            )

            asset_resources_list.append(asset_resource)
        return asset_resources_list

    def map_role_translations(
        self, languages: list[Language], product_relationship_role: SFProductRelationRole
    ) -> list[RelationshipTranslation]:
        related_product_translations = []
        for language in languages:
            if not (
                translation := self.filter_translations_by_language(product_relationship_role.translations, language)
            ):
                continue
            related_product_translations.append(
                RelationshipTranslation(language=language, translated_name=translation.label)
            )

        return related_product_translations

    def map_relations(
        self, relations: SFProductRelations, languages: list[Language], main_product_id: uuid.UUID | None, db: Session
    ) -> list[RelatedProduct]:
        roles_dict = {role.id: role for role in relations.roles}
        related_products = []

        for relation in relations.relation:
            for related_product in relation.products:
                if not (
                    product_id := db.query(DBProduct.id).filter_by(umid=uuid.UUID(related_product.umid)).one_or_none()
                ):
                    continue
                related_products.append(
                    self.db_ops.upsert(
                        RelatedProduct,
                        filter_by={"main_product_id": main_product_id, "related_product_id": product_id[0]},
                        related_product_id=product_id[0],
                        translations=self.map_role_translations(languages, roles_dict[relation.role_id]),
                        relationship_id=relation.role_id,
                    )
                )
        return related_products

    def map_category_assets(self, category_asset: SFAssetRelationship) -> Optional[Asset]:
        asset_details, asset_resources = load_assets(category_asset.udai)
        if not asset_details:
            return None
        else:
            return Asset(
                udai=uuid.UUID(category_asset.udai),
                role_id=category_asset.role_id,
                asset_resources=self.map_asset_resources(asset_details, asset_resources),
            )

    def map_categories(self, umid: str) -> list[CategoryAssociation]:
        """Map categories and return CategoryAssociation objects with product_sort_order"""
        categories = self.categories_data[umid]
        sf_categories = [
            [SFCategory(**convert_dict_keys_to_snake(category)) for category in category_sort]
            for category_sort in categories
        ]

        db_categories = self.process_categories_and_products(sf_categories)

        category_associations = []

        sf_category_map = {}
        for category_tree in sf_categories:
            for sf_category in category_tree:
                sf_category_map[uuid.UUID(sf_category.category_umid)] = sf_category

        # Create CategoryAssociation objects for each db_category
        for db_category in db_categories:
            sf_cat = sf_category_map.get(db_category.category_umid) if db_category.category_umid else None
            product_sort_order = sf_cat.product_sort_index if sf_cat else None

            category_association = CategoryAssociation(category=db_category, product_sort_order=product_sort_order)
            category_associations.append(category_association)

        return category_associations

    def map_category_translations(self, translations: list[SFcategoryTranslation]) -> list[CategoryTranslation]:
        return [
            CategoryTranslation(language=self.upsert_language(translation.language), name=translation.label)
            for translation in translations
        ]

    def process_categories_and_products(
        self,
        category_trees: list[list[SFCategory]],
    ) -> list[Category]:
        # Map categories to their parents
        db_categories = []
        for categories in category_trees:
            sorted_categories = sorted(categories, key=lambda x: x.sort_index)
            index_to_id: dict[int, Category] = {}
            for category in sorted_categories:
                category_umid = uuid.UUID(category.category_umid)
                if category_umid in self.category_cache:
                    db_category = self.category_cache[category_umid]
                else:
                    # Calculate parent_id before upsert
                    parent_index = category.sort_index - 1
                    parent_category = index_to_id.get(parent_index)
                    parent_id = parent_category.id if parent_category is not None else None

                    db_category = self.db_ops.upsert(
                        Category,
                        filter_by={"category_umid": category_umid},
                        category_umid=category_umid,
                        sort_index=category.syncforce_index,
                        category_id=category.category_id,
                        parent_id=parent_id,
                        translations=self.map_category_translations(category.translations),
                    )

                    if self.process_assets:
                        assets = (
                            [self.map_category_assets(category_asset) for category_asset in category.assets]
                            if category.assets
                            else []
                        )
                        db_category.assets = [asset for asset in assets if asset is not None]  # Remove None values

                    db_categories.append(db_category)

                index_to_id[category.sort_index] = db_category
                self.category_cache[category_umid] = db_category
        return db_categories


class ProductMapper:
    def __init__(self, session: Session, categories_data: dict, process_assets: bool = True):
        self.db_ops = DatabaseOperations(session)
        self.session = session
        self.process_assets = process_assets
        self.product_attr_mapper = ProductAttributesMapper(
            self.db_ops, categories_data, process_assets=self.process_assets
        )

    def map_domain_to_db_model(self, product: SFProduct, product_id: uuid.UUID | None) -> DBProduct:
        try:
            umid = uuid.UUID(product.umid)
        except ValueError:
            raise ValueError(f"Invalid UUID: {product.umid}")
        languages = self.product_attr_mapper.map_languages(product.languages)
        db_product = DBProduct(
            related_products=(
                self.product_attr_mapper.map_relations(product.relations, languages, product_id, self.session)
                if product.relations
                else []
            ),
            id=product_id,
            product_id=product.id,
            umid=umid,
            part_nr=product.part_nr,
            gtin=product.gtin,
            article_number=product.article_number,
            master_code=product.master_code,
            languages=languages,
            master_product_names=self.product_attr_mapper.map_master_product_name(product.master_name),
            product_names=self.product_attr_mapper.map_product_name(product.name),
            brand=self.product_attr_mapper.map_brand(product.brand),
            classification=self.product_attr_mapper.upsert_classification(product.classification),
            target_groups=self.product_attr_mapper.map_target_group(product.segmentation),
            product_texts=self.product_attr_mapper.map_product_texts(product.texts),
            hierarchy_level=self.product_attr_mapper.map_hierarchy_level(product.hierarchy_level),
            attributes=self.product_attr_mapper.map_attributes(product.product_attributes, product),
            last_modified=datetime.datetime.now(),
            categories=self.product_attr_mapper.map_categories(product.umid),
            dirty=True,
        )

        if self.process_assets:
            db_product.assets = self.product_attr_mapper.map_assets(product.assets, languages)

        return db_product
