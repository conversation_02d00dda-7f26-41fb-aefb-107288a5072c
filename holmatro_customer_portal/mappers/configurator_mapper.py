from __future__ import annotations

from typing import TYPE_CHECKING
from uuid import UUID

from sqlalchemy import and_, desc
from sqlalchemy.orm import Session

from holmatro_customer_portal.database.models import (
    Configuration,
    ConfigurationData,
    ConfigurationProduct,
    Product,
    User,
)
from holmatro_customer_portal.mappers.product_preview_mapper import ProductPreviewMapper
from holmatro_customer_portal.schemas.configurator import (
    ConfigurationRes,
    ConfigurationStateReq,
    ConfiguratorData,
    ConfiguratorProductRes,
    ConfiguratorStateProducts,
    Stage,
)
from holmatro_customer_portal.services.utils.product_queries import get_configurator_products
from holmatro_customer_portal.utils.database import DatabaseOperations
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.my_hm.my_hm_interface import MyHolmatroClientInterface

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.states import ConfiguratorState

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class ConfiguratorMapper:
    def __init__(self, session: Session) -> None:
        self.db_ops = DatabaseOperations(session)

    def store_configuration_data(
        self, configuration: Configuration, configuration_state: ConfigurationStateReq
    ) -> list[ConfigurationData]:
        mapped = []
        for data in configuration_state.configuration_data:
            mapped.append(
                self.db_ops.upsert(
                    ConfigurationData,
                    filter_by={"configuration_id": configuration.id, "attribute_id": data.attribute_id},
                    configuration=configuration,
                    state_id=configuration_state.state_id,
                    attribute_id=data.attribute_id,
                    value=data.value,
                )
            )
        return mapped

    def clear_configuration_data_for_state(self, request: ConfigurationStateReq) -> None:
        data = (
            self.db_ops.session.query(ConfigurationData)
            .filter(
                ConfigurationData.configuration_id == UUID(request.configuration_id),
                ConfigurationData.state_id == request.state_id,
            )
            .all()
        )
        for d in data:
            self.db_ops.session.delete(d)

    def store_configuration_product(
        self, configuration: Configuration, configuration_state: ConfigurationStateReq | ConfiguratorStateProducts
    ) -> list[ConfigurationProduct]:
        """It can store a product selected by user or implemented in the background"""
        from holmatro_customer_portal.services.configurator.states.base import ConfiguratorState

        if configuration_state.products is None:
            return []

        queried_products = []
        """ Fetch products by article number """
        for configurator_product in configuration_state.products:
            product = (
                self.db_ops.session.query(Product)
                .filter(Product.article_number == configurator_product.article_number)
                .one_or_none()
            )
            if not product:
                """Fetch product by id"""
                product = self.db_ops.session.query(Product).get(configurator_product.product_id)

            if not product:
                if configurator_product.product_id:
                    _logger.debug(f"No product found with id {configurator_product.product_id}")
                    raise RuntimeError(f"No product found with id {configurator_product.product_id}")
                else:
                    _logger.debug(f"No product found with article number {configurator_product.article_number}")
                    raise RuntimeError(f"No product found with article number {configurator_product.article_number}")

            else:
                queried_products.append(product)

        position = next(
            state_index
            for state_index, state in enumerate(ConfiguratorState)
            if state.value == configuration_state.state_id
        )
        configuration_products: list[ConfigurationProduct] = []
        self.db_ops.session.query(ConfigurationProduct).filter(
            and_(
                ConfigurationProduct.configuration_id == configuration.id,
                ConfigurationProduct.state_id == configuration_state.state_id,
            )
        ).delete()
        for product in queried_products:
            quantity = next(
                p.quantity
                for p in configuration_state.products
                if (p.product_id == product.id or p.article_number == product.article_number)
            )
            configuration_product = ConfigurationProduct(
                configuration=configuration,
                product=product,
                state_id=configuration_state.state_id,
                quantity=quantity,
                position=position,
            )
            configuration_products.append(configuration_product)

        return configuration_products

    def clear_configuration_product_for_state(self, request: ConfigurationStateReq) -> None:
        products = (
            self.db_ops.session.query(ConfigurationProduct)
            .filter(
                ConfigurationProduct.configuration_id == UUID(request.configuration_id),
                ConfigurationProduct.state_id == request.state_id,
            )
            .all()
        )
        for product in products:
            self.db_ops.session.delete(product)

    @staticmethod
    def collect_configuration_data(configuration: Configuration, state: ConfiguratorState) -> list[ConfiguratorData]:
        data = []
        for configuration_data in configuration.data:
            if configuration_data.state_id == state.value:
                data.append(
                    ConfiguratorData(
                        attribute_id=configuration_data.attribute_id,
                        value=configuration_data.value,
                    )
                )
        return data

    def collect_configuration_product(
        self, configuration: Configuration, state: ConfiguratorState, my_hm_client: MyHolmatroClientInterface
    ) -> list[ConfiguratorProductRes]:
        # Check if the configuration has a product in that state
        configuration_products = (
            self.db_ops.session.query(ConfigurationProduct)
            .filter_by(configuration_id=configuration.id, state_id=state.value)
            .all()
        )
        if not configuration_products:
            return []

        # Fetch product data
        queried_products = get_configurator_products(
            [product.product_id for product in configuration_products], configuration, self.db_ops.session  # type: ignore
        )
        mapper = ProductPreviewMapper(self.db_ops.session, my_hm_client)  # type: ignore
        return mapper.configurator_product_preview_mapper(configuration.user, queried_products)

    def collect_configuration_overview(self, user: User) -> list[ConfigurationRes]:
        configurations = (
            self.db_ops.session.query(Configuration)
            .filter(Configuration.user == user, Configuration.hidden == False)
            .order_by(desc(Configuration.started_at))
            .all()
        )
        return [
            ConfigurationRes(
                configuration_id=configuration.id,
                category_id=int(configuration.category.category_id) if configuration.category.category_id else 0,
                started_at=configuration.started_at,
                quotation_number=configuration.quotation_number,
                reference=configuration.reference,
                progress=self._determine_progress(configuration),  # type: ignore
                tags=self._determine_tags(configuration),  # type: ignore
            )
            for configuration in configurations
        ]

    @staticmethod
    def _determine_progress(configuration: Configuration) -> int:
        """Determine the progress (%) of the configuration based on the stage.
        The stage is always the first part of the state id.
        """
        if configuration.finished_at:
            return 100
        if not configuration.last_updated_state:
            return 0

        # The type of configurator is defined by the first two parts of the state id e.g. 'industrial.lifting'
        configurator_type: str = ".".join(configuration.last_updated_state.split(".")[:2])

        # Determine the steps (stages) for this configurator type
        steps: list[str] = list(
            filter(
                lambda itm, t=configurator_type: itm.startswith(t),  # type: ignore
                list(map(lambda itm: itm.value, list(Stage))),
            )
        )
        steps.append(Stage.INDUSTRIAL_OVERVIEW.value)
        # Determine the progress based on the index of the last updated state
        return int((100 / (len(steps))) * (steps.index(".".join(configuration.last_updated_state.split(".")[:3])) + 1))

    @staticmethod
    def _determine_tags(configuration: Configuration) -> list[str]:
        tags: set = set()
        if not configuration.last_updated_state:
            return list(tags)

        mapped_tags = list(map(lambda itm: itm.value, list(Stage)))

        for product in configuration.products:
            # Stages are defined by the first three parts of the state id e.g. 'industrial.lifting.cylinder'
            stage_id = ".".join(product.state_id.split(".")[:3])
            if stage_id not in mapped_tags:
                _logger.warning(
                    f"Detected an error while mapping tags for product {product.product_id} in configuration {product.configuration_id}, state {product.state_id}"
                )
                continue
            # Tags are the last part of the stage id e.g. 'cylinder'
            tags.add(stage_id.split(".")[-1])

        return list(tags)
