name: Merge to master
on:
  push:
    branches:
      - 'master'
    paths-ignore:
      - 'build/*'

jobs:
  run-tests:
    runs-on: [self-hosted, linux]
    container: python:3.12-slim-bookworm
    steps:
      - name: Install git
        run: apt-get update
      - run: apt-get -y install git
      - name: Apply fix for https://github.com/actions/runner-images/issues/6775
        run: git config --system --add safe.directory '*'
      - name: Install project and dependencies
        uses: actions/checkout@v3
      - name: Run tests
        uses: Harborn-digital/github-action-poetry-poe-task@python3.12
        env:
          STAGE: test
          POETRY_HTTP_BASIC_HARBORN_USERNAME: ${{ secrets.PYPI_USERNAME }}
          POETRY_HTTP_BASIC_HARBORN_PASSWORD: ${{ secrets.PYPI_READ_SECRET }}
       