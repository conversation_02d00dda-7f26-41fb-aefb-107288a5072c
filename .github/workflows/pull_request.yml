name: Pull Request
on:
  pull_request:
    branches: [ master ]

jobs:
  security-checks:
    runs-on: [self-hosted, linux]
    container: python:3.12-slim-bookworm
    steps:
      - name: Install project and dependencies
        uses: actions/checkout@v3
      - name: Find common security issues
        uses: Harborn-digital/github-action-poetry-poe-task@python3.12
        env:
          POETRY_HTTP_BASIC_HARBORN_USERNAME: ${{ secrets.PYPI_USERNAME }}
          POETRY_HTTP_BASIC_HARBORN_PASSWORD: ${{ secrets.PYPI_READ_SECRET }}
        with:
          task: security
  code-quality-checks:
    runs-on: [self-hosted, linux]
    container: python:3.12-slim-bookworm
    steps:
      - name: Install project and dependencies
        uses: actions/checkout@v3
      - name: Coding best practices and complexity checks
        uses: Harborn-digital/github-action-poetry-poe-task@python3.12
        env:
          POETRY_HTTP_BASIC_HARBORN_USERNAME: ${{ secrets.PYPI_USERNAME }}
          POETRY_HTTP_BASIC_HARBORN_PASSWORD: ${{ secrets.PYPI_READ_SECRET }}
        with:
          task: code_quality
        continue-on-error: true
  run-tests:
    runs-on: [self-hosted, linux]
    container: python:3.12-slim-bookworm
    steps:
      - name: Install git
        run: apt-get update
      - run: apt-get -y install git
      - name: Apply fix for https://github.com/actions/runner-images/issues/6775
        run: git config --system --add safe.directory '*'
      - name: Install project and dependencies
        uses: actions/checkout@v3
      - name: Run tests
        uses: Harborn-digital/github-action-poetry-poe-task@python3.12
        env:
          STAGE: test
          POETRY_HTTP_BASIC_HARBORN_USERNAME: ${{ secrets.PYPI_USERNAME }}
          POETRY_HTTP_BASIC_HARBORN_PASSWORD: ${{ secrets.PYPI_READ_SECRET }}
        with:
          task: test
      - name: Test Report
        uses: dorny/test-reporter@v1
        if: ${{ always() }}
        with:
            name: Test results
            path: ./test-results.xml
            reporter: java-junit
            only-summary: true
