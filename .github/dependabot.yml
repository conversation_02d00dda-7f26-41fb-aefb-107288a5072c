version: 2
# Add registries for dependabot to use
registries:
  github:
    type: "git"
    url: "https://github.com"
    username: "harborn-sudo"
    password: "${{secrets.HARBORN_SUDO_PAT}}"
  pypi:
    type: "python-index"
    url: "https://pypi.packages.harborn.com/simple/"
    username: "${{secrets.HARBORN_PYPI_USER}}"
    password: "${{secrets.HARBORN_PYPI_SECRET}}"
updates:
  # Maintain dependencies for terraform
  - package-ecosystem: "terraform"
    directories:
      - "**/*"
    schedule:
      interval: "monthly"
      time: "02:00"
      timezone: "Europe/Amsterdam"
    registries: "*"
    reviewers:
      - Harborn-digital/team-engineering-1
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]
    groups:
      tf-security-update:
        applies-to: security-updates
        patterns:
          - "*"
        update-types:
          - "minor"
          - "patch"
      tf-version-update:
        applies-to: version-updates
        patterns:
          - "*"
        update-types:
          - "minor"
          - "patch"
      tf-major-update:
        patterns:
          - "*"
        update-types:
          - "major"

  # Maintain dependencies for pip / poetry
  - package-ecosystem: "pip"
    directories:
      - "/"
    schedule:
      interval: "monthly"
      time: "02:00"
      timezone: "Europe/Amsterdam"
    registries: "*"
    reviewers:
      - Harborn-digital/team-engineering-1
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]
    groups:
      pip-security-update:
        applies-to: security-updates
        patterns:
          - "*"
        update-types:
          - "minor"
          - "patch"
      pip-version-update:
        applies-to: version-updates
        patterns:
          - "*"
        update-types:
          - "minor"
          - "patch"
      pip-major-update:
        patterns:
          - "*"
        update-types:
          - "major"

  # Maintain dependencies for docker
  - package-ecosystem: "docker"
    directories:
      - "/docker-resources"
    schedule:
      interval: "monthly"
      time: "02:00"
      timezone: "Europe/Amsterdam"
    registries: "*"
    reviewers:
      - Harborn-digital/team-engineering-1
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]
    groups:
      docker-security-update:
        applies-to: security-updates
        patterns:
          - "*"
        update-types:
          - "minor"
          - "patch"
      docker-version-update:
        applies-to: version-updates
        patterns:
          - "*"
        update-types:
          - "minor"
          - "patch"
      docker-major-update:
        patterns:
          - "*"
        update-types:
          - "major"

  # Maintain dependencies for composer
  - package-ecosystem: "composer"
    directories:
      - "/"
    schedule:
      interval: "monthly"
      time: "02:00"
      timezone: "Europe/Amsterdam"
    registries: "*"
    reviewers:
      - Harborn-digital/team-engineering-1
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]
    groups:
      composer-security-update:
        applies-to: security-updates
        patterns:
          - "*"
        update-types:
          - "minor"
          - "patch"
      composer-version-update:
        applies-to: version-updates
        patterns:
          - "*"
        update-types:
          - "minor"
          - "patch"
      composer-major-update:
        patterns:
          - "*"
        update-types:
          - "major"

  # Maintain dependencies for npm
  - package-ecosystem: "npm"
    directories:
      - "/"
    schedule:
      interval: "monthly"
      time: "02:00"
      timezone: "Europe/Amsterdam"
    registries: "*"
    reviewers:
      - Harborn-digital/team-engineering-1
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]
    groups:
      npm-security-update:
        applies-to: security-updates
        patterns:
          - "*"
        update-types:
          - "minor"
          - "patch"
      npm-version-update:
        applies-to: version-updates
        patterns:
          - "*"
        update-types:
          - "minor"
          - "patch"
      npm-major-update:
        patterns:
          - "*"
        update-types:
          - "major"

  # Maintain dependencies for github-actions
  - package-ecosystem: "github-actions"
    directories:
      - "/"
    schedule:
      interval: "monthly"
      time: "02:00"
      timezone: "Europe/Amsterdam"
    registries: "*"
    reviewers:
      - Harborn-digital/team-engineering-1
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]
    groups:
      ga-security-update:
        applies-to: security-updates
        patterns:
          - "*"
        update-types:
          - "minor"
          - "patch"
      ga-version-update:
        applies-to: version-updates
        patterns:
          - "*"
        update-types:
          - "minor"
          - "patch"
      ga-major-update:
        patterns:
          - "*"
        update-types:
          - "major"

  # Maintain dependencies for gomod
  - package-ecosystem: "gomod"
    directories:
      - "/"
    schedule:
      interval: "monthly"
      time: "02:00"
      timezone: "Europe/Amsterdam"
    registries: "*"
    reviewers:
      - Harborn-digital/team-engineering-1
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]
    groups:
      gomod-security-update:
        applies-to: security-updates
        patterns:
          - "*"
        update-types:
          - "minor"
          - "patch"
      gomod-version-update:
        applies-to: version-updates
        patterns:
          - "*"
        update-types:
          - "minor"
          - "patch"
      gomod-major-update:
        patterns:
          - "*"
        update-types:
          - "major"
