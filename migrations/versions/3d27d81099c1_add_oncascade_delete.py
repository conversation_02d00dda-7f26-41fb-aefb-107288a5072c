"""add oncascade delete

Revision ID: 3d27d81099c1
Revises: 3a20475d2722
Create Date: 2023-12-04 17:09:51.059833

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3d27d81099c1'
down_revision: Union[str, None] = '3a20475d2722'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fk_asset_resource_language_association_asset_resource_id_e634', 'asset_resource_language_association', type_='foreignkey')
    op.create_foreign_key(op.f('fk_asset_resource_language_association_asset_resource_id_asset_resources'), 'asset_resource_language_association', 'asset_resources', ['asset_resource_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('fk_asset_resources_asset_id_assets', 'asset_resources', type_='foreignkey')
    op.create_foreign_key(op.f('fk_asset_resources_asset_id_assets'), 'asset_resources', 'assets', ['asset_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('fk_assets_product_id_products', 'assets', type_='foreignkey')
    op.create_foreign_key(op.f('fk_assets_product_id_products'), 'assets', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('fk_assets_metadata_translations_asset_id_assets', 'assets_metadata_translations', type_='foreignkey')
    op.create_foreign_key(op.f('fk_assets_metadata_translations_asset_id_assets'), 'assets_metadata_translations', 'assets', ['asset_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('fk_attribute_translations_attribute_id_attributes', 'attribute_translations', type_='foreignkey')
    op.create_foreign_key(op.f('fk_attribute_translations_attribute_id_attributes'), 'attribute_translations', 'attributes', ['attribute_id'], ['id'], ondelete='CASCADE')
    op.add_column('attributes', sa.Column('attribute_group_id', sa.Integer(), nullable=True))
    op.drop_constraint('fk_attributes_product_id_products', 'attributes', type_='foreignkey')
    op.create_foreign_key(op.f('fk_attributes_product_id_products'), 'attributes', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('fk_classification_translations_classification_id_classifications', 'classification_translations', type_='foreignkey')
    op.create_foreign_key(op.f('fk_classification_translations_classification_id_classifications'), 'classification_translations', 'classifications', ['classification_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('fk_configuration_products_product_id_products', 'configuration_products', type_='foreignkey')
    op.create_foreign_key(op.f('fk_configuration_products_product_id_products'), 'configuration_products', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('fk_hierarchy_level_translations_hierarchy_level_id_hiera_eb6e', 'hierarchy_level_translations', type_='foreignkey')
    op.create_foreign_key(op.f('fk_hierarchy_level_translations_hierarchy_level_id_hierarchy_levels'), 'hierarchy_level_translations', 'hierarchy_levels', ['hierarchy_level_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('fk_master_product_names_product_id_products', 'master_product_names', type_='foreignkey')
    op.create_foreign_key(op.f('fk_master_product_names_product_id_products'), 'master_product_names', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('fk_product_language_association_table_products_id_products', 'product_language_association_table', type_='foreignkey')
    op.create_foreign_key(op.f('fk_product_language_association_table_products_id_products'), 'product_language_association_table', 'products', ['products_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('fk_product_names_product_id_products', 'product_names', type_='foreignkey')
    op.create_foreign_key(op.f('fk_product_names_product_id_products'), 'product_names', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('fk_product_text_translations_product_text_id_product_texts', 'product_text_translations', type_='foreignkey')
    op.create_foreign_key(op.f('fk_product_text_translations_product_text_id_product_texts'), 'product_text_translations', 'product_texts', ['product_text_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('fk_product_texts_product_id_products', 'product_texts', type_='foreignkey')
    op.create_foreign_key(op.f('fk_product_texts_product_id_products'), 'product_texts', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    op.add_column('products', sa.Column('last_modified', sa.TIMESTAMP(), nullable=True))
    op.drop_constraint('fk_related_products_related_product_id_products', 'related_products', type_='foreignkey')
    op.drop_constraint('fk_related_products_main_product_id_products', 'related_products', type_='foreignkey')
    op.create_foreign_key(op.f('fk_related_products_related_product_id_products'), 'related_products', 'products', ['related_product_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key(op.f('fk_related_products_main_product_id_products'), 'related_products', 'products', ['main_product_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('fk_relationship_translations_related_product_id_related_products', 'relationship_translations', type_='foreignkey')
    op.create_foreign_key(op.f('fk_relationship_translations_related_product_id_related_products'), 'relationship_translations', 'related_products', ['related_product_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('fk_target_group_association_table_products_id_products', 'target_group_association_table', type_='foreignkey')
    op.drop_constraint('fk_target_group_association_table_target_group_id_target_groups', 'target_group_association_table', type_='foreignkey')
    op.create_foreign_key(op.f('fk_target_group_association_table_products_id_products'), 'target_group_association_table', 'products', ['products_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key(op.f('fk_target_group_association_table_target_group_id_target_groups'), 'target_group_association_table', 'target_groups', ['target_group_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('fk_target_groups_product_id_products', 'target_groups', type_='foreignkey')
    op.create_foreign_key(op.f('fk_target_groups_product_id_products'), 'target_groups', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('fk_user_favorite_products_user_id_users', 'user_favorite_products', type_='foreignkey')
    op.drop_constraint('fk_user_favorite_products_product_id_products', 'user_favorite_products', type_='foreignkey')
    op.create_foreign_key(op.f('fk_user_favorite_products_user_id_users'), 'user_favorite_products', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key(op.f('fk_user_favorite_products_product_id_products'), 'user_favorite_products', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f('fk_user_favorite_products_product_id_products'), 'user_favorite_products', type_='foreignkey')
    op.drop_constraint(op.f('fk_user_favorite_products_user_id_users'), 'user_favorite_products', type_='foreignkey')
    op.create_foreign_key('fk_user_favorite_products_product_id_products', 'user_favorite_products', 'products', ['product_id'], ['id'])
    op.create_foreign_key('fk_user_favorite_products_user_id_users', 'user_favorite_products', 'users', ['user_id'], ['id'])
    op.drop_constraint(op.f('fk_target_groups_product_id_products'), 'target_groups', type_='foreignkey')
    op.create_foreign_key('fk_target_groups_product_id_products', 'target_groups', 'products', ['product_id'], ['id'])
    op.drop_constraint(op.f('fk_target_group_association_table_target_group_id_target_groups'), 'target_group_association_table', type_='foreignkey')
    op.drop_constraint(op.f('fk_target_group_association_table_products_id_products'), 'target_group_association_table', type_='foreignkey')
    op.create_foreign_key('fk_target_group_association_table_target_group_id_target_groups', 'target_group_association_table', 'target_groups', ['target_group_id'], ['id'])
    op.create_foreign_key('fk_target_group_association_table_products_id_products', 'target_group_association_table', 'products', ['products_id'], ['id'])
    op.drop_constraint(op.f('fk_relationship_translations_related_product_id_related_products'), 'relationship_translations', type_='foreignkey')
    op.create_foreign_key('fk_relationship_translations_related_product_id_related_products', 'relationship_translations', 'related_products', ['related_product_id'], ['id'])
    op.drop_constraint(op.f('fk_related_products_main_product_id_products'), 'related_products', type_='foreignkey')
    op.drop_constraint(op.f('fk_related_products_related_product_id_products'), 'related_products', type_='foreignkey')
    op.create_foreign_key('fk_related_products_main_product_id_products', 'related_products', 'products', ['main_product_id'], ['id'])
    op.create_foreign_key('fk_related_products_related_product_id_products', 'related_products', 'products', ['related_product_id'], ['id'])
    op.drop_column('products', 'last_modified')
    op.drop_constraint(op.f('fk_product_texts_product_id_products'), 'product_texts', type_='foreignkey')
    op.create_foreign_key('fk_product_texts_product_id_products', 'product_texts', 'products', ['product_id'], ['id'])
    op.drop_constraint(op.f('fk_product_text_translations_product_text_id_product_texts'), 'product_text_translations', type_='foreignkey')
    op.create_foreign_key('fk_product_text_translations_product_text_id_product_texts', 'product_text_translations', 'product_texts', ['product_text_id'], ['id'])
    op.drop_constraint(op.f('fk_product_names_product_id_products'), 'product_names', type_='foreignkey')
    op.create_foreign_key('fk_product_names_product_id_products', 'product_names', 'products', ['product_id'], ['id'])
    op.drop_constraint(op.f('fk_product_language_association_table_products_id_products'), 'product_language_association_table', type_='foreignkey')
    op.create_foreign_key('fk_product_language_association_table_products_id_products', 'product_language_association_table', 'products', ['products_id'], ['id'])
    op.drop_constraint(op.f('fk_master_product_names_product_id_products'), 'master_product_names', type_='foreignkey')
    op.create_foreign_key('fk_master_product_names_product_id_products', 'master_product_names', 'products', ['product_id'], ['id'])
    op.drop_constraint(op.f('fk_hierarchy_level_translations_hierarchy_level_id_hierarchy_levels'), 'hierarchy_level_translations', type_='foreignkey')
    op.create_foreign_key('fk_hierarchy_level_translations_hierarchy_level_id_hiera_eb6e', 'hierarchy_level_translations', 'hierarchy_levels', ['hierarchy_level_id'], ['id'])
    op.drop_constraint(op.f('fk_configuration_products_product_id_products'), 'configuration_products', type_='foreignkey')
    op.create_foreign_key('fk_configuration_products_product_id_products', 'configuration_products', 'products', ['product_id'], ['id'])
    op.drop_constraint(op.f('fk_classification_translations_classification_id_classifications'), 'classification_translations', type_='foreignkey')
    op.create_foreign_key('fk_classification_translations_classification_id_classifications', 'classification_translations', 'classifications', ['classification_id'], ['id'])
    op.drop_constraint(op.f('fk_attributes_product_id_products'), 'attributes', type_='foreignkey')
    op.create_foreign_key('fk_attributes_product_id_products', 'attributes', 'products', ['product_id'], ['id'])
    op.drop_column('attributes', 'attribute_group_id')
    op.drop_constraint(op.f('fk_attribute_translations_attribute_id_attributes'), 'attribute_translations', type_='foreignkey')
    op.create_foreign_key('fk_attribute_translations_attribute_id_attributes', 'attribute_translations', 'attributes', ['attribute_id'], ['id'])
    op.drop_constraint(op.f('fk_assets_metadata_translations_asset_id_assets'), 'assets_metadata_translations', type_='foreignkey')
    op.create_foreign_key('fk_assets_metadata_translations_asset_id_assets', 'assets_metadata_translations', 'assets', ['asset_id'], ['id'])
    op.drop_constraint(op.f('fk_assets_product_id_products'), 'assets', type_='foreignkey')
    op.create_foreign_key('fk_assets_product_id_products', 'assets', 'products', ['product_id'], ['id'])
    op.drop_constraint(op.f('fk_asset_resources_asset_id_assets'), 'asset_resources', type_='foreignkey')
    op.create_foreign_key('fk_asset_resources_asset_id_assets', 'asset_resources', 'assets', ['asset_id'], ['id'])
    op.drop_constraint(op.f('fk_asset_resource_language_association_asset_resource_id_asset_resources'), 'asset_resource_language_association', type_='foreignkey')
    op.create_foreign_key('fk_asset_resource_language_association_asset_resource_id_e634', 'asset_resource_language_association', 'asset_resources', ['asset_resource_id'], ['id'])
    # ### end Alembic commands ###
