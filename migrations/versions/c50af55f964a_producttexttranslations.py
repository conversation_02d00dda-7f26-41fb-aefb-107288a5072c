"""ProductTextTranslations

Revision ID: c50af55f964a
Revises: f5934967dfdc
Create Date: 2023-10-28 10:36:24.696712

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c50af55f964a'
down_revision: Union[str, None] = 'f5934967dfdc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product_text_translations', sa.Column('type', sa.Enum('long_description', 'features', 'supplied_with', 'available_on_request', 'accessories', 'additional_information', 'other', name='texttype'), nullable=True))
    op.add_column('product_text_translations', sa.Column('sort_order', sa.<PERSON><PERSON>(), nullable=True))
    op.create_index(op.f('ix_product_text_translations_sort_order'), 'product_text_translations', ['sort_order'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_product_text_translations_sort_order'), table_name='product_text_translations')
    op.drop_column('product_text_translations', 'sort_order')
    op.drop_column('product_text_translations', 'type')
    # ### end Alembic commands ###
