"""add_favourite_products

Revision ID: 5c36f291842a
Revises: 554c9e0343b7
Create Date: 2023-11-09 13:27:48.739944

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5c36f291842a'
down_revision: Union[str, None] = '07939bb86992'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_favorite_products',
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('product_id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], name=op.f('fk_user_favorite_products_product_id_products')),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_user_favorite_products_user_id_users')),
    sa.PrimaryKeyConstraint('user_id', 'product_id', name=op.f('pk_user_favorite_products'))
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_favorite_products')
    # ### end Alembic commands ###
