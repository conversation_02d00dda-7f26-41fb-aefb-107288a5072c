"""index to system type

Revision ID: 3a20475d2722
Revises: c1afcd735f09
Create Date: 2023-11-30 07:28:09.242421

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3a20475d2722'
down_revision: Union[str, None] = 'c1afcd735f09'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_assets_asset_type'), 'assets', ['asset_type'], unique=False)
    op.create_index(op.f('ix_attributes_system_type'), 'attributes', ['system_type'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_attributes_system_type'), table_name='attributes')
    op.drop_index(op.f('ix_assets_asset_type'), table_name='assets')
    # ### end Alembic commands ###
