"""Add index on Product dirty and deleted_at

Revision ID: f14f15f29251
Revises: 5cb0089731e3
Create Date: 2025-06-12 15:45:15.909354

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f14f15f29251'
down_revision: Union[str, None] = '5cb0089731e3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_product_dirty_deleted_at', 'products', ['dirty', 'deleted_at'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_product_dirty_deleted_at', table_name='products')
    # ### end Alembic commands ###
