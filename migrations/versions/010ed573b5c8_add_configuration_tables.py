"""Add configuration tables

Revision ID: 010ed573b5c8
Revises: c50af55f964a
Create Date: 2023-11-01 18:13:25.580539

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '010ed573b5c8'
down_revision: Union[str, None] = 'c50af55f964a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('configurations',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('version', sa.String(length=5), nullable=False, comment='Configurator version'),
    sa.Column('reference', sa.String(length=255), nullable=True, comment='User reference for this configuration'),
    sa.Column('started_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('finished_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_configurations'))
    )
    op.create_table('configuration_data',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('configuration_id', sa.Uuid(), nullable=True),
    sa.Column('state_id', sa.String(length=255), nullable=False),
    sa.Column('attribute_id', sa.String(length=255), nullable=False),
    sa.Column('data', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['configuration_id'], ['configurations.id'], name=op.f('fk_configuration_data_configuration_id_configurations')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_configuration_data'))
    )
    op.create_index('configuration_id_attribute_id_idx', 'configuration_data', ['configuration_id', 'attribute_id'], unique=True)
    op.create_index(op.f('ix_configuration_data_updated_at'), 'configuration_data', ['updated_at'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('configuration_data')
    op.drop_table('configurations')
    # ### end Alembic commands ###
