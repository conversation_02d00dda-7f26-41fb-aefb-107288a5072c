"""link user to category

Revision ID: 31c74acadec8
Revises: 2d22ba10e9f2
Create Date: 2023-12-13 14:28:56.912716

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '31c74acadec8'
down_revision: Union[str, None] = 'bdb31814380c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('category_user_association_table',
    sa.Column('category_id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['category_id'], ['categories.id'], name=op.f('fk_category_user_association_table_category_id_categories')),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_category_user_association_table_user_id_users')),
    sa.PrimaryKeyConstraint('category_id', 'user_id', name=op.f('pk_category_user_association_table'))
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('category_user_association_table')
    # ### end Alembic commands ###
