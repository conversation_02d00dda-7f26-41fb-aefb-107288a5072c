"""Add configuration-product table

Revision ID: 7924489642cf
Revises: 010ed573b5c8
Create Date: 2023-11-07 07:37:44.598422

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7924489642cf'
down_revision: Union[str, None] = '010ed573b5c8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('configuration_products',
    sa.Column('configuration_id', sa.Uuid(), nullable=False),
    sa.Column('product_id', sa.Uuid(), nullable=False),
    sa.Column('state_id', sa.String(length=255), nullable=False),
    sa.Column('quantity', sa.<PERSON>Integer(), nullable=False),
    sa.ForeignKeyConstraint(['configuration_id'], ['configurations.id'], name=op.f('fk_configuration_products_configuration_id_configurations')),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], name=op.f('fk_configuration_products_product_id_products')),
    sa.PrimaryKeyConstraint('configuration_id', 'product_id', name=op.f('pk_configuration_products'))
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('configuration_products')
    # ### end Alembic commands ###
