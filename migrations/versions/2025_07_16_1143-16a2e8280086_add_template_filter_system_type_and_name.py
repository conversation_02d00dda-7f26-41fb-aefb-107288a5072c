"""Add template filter system type and name

Revision ID: 16a2e8280086
Revises: f86d4ae6f533
Create Date: 2025-07-16 11:43:39.958177

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '16a2e8280086'
down_revision: Union[str, None] = 'f86d4ae6f533'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('filter_templates', sa.Column('name', sa.String(length=255), nullable=False))
    op.add_column('filter_templates', sa.Column('system_type', sa.Enum('METRIC', 'IMPERIAL', name='systemtype'), nullable=True))
    op.create_index(op.f('ix_filter_templates_system_type'), 'filter_templates', ['system_type'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_filter_templates_system_type'), table_name='filter_templates')
    op.drop_column('filter_templates', 'system_type')
    op.drop_column('filter_templates', 'name')
    # ### end Alembic commands ###
