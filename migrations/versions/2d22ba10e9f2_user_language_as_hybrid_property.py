"""user language as hybrid property

Revision ID: 2d22ba10e9f2
Revises: 7fe4d47e5231
Create Date: 2023-12-12 13:15:22.251502

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '2d22ba10e9f2'
down_revision: Union[str, None] = 'c826b3b41ad3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fk_configuration_products_product_id_products', 'configuration_products', type_='foreignkey')
    op.create_foreign_key(op.f('fk_configuration_products_product_id_products'), 'configuration_products', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    op.add_column('users', sa.Column('main_language_id', sa.Uuid(), nullable=True))
    op.drop_constraint('fk_users_language_id_languages', 'users', type_='foreignkey')
    op.create_foreign_key(op.f('fk_users_main_language_id_languages'), 'users', 'languages', ['main_language_id'], ['id'])
    op.drop_column('users', 'language_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('language_id', mysql.CHAR(length=32), nullable=True))
    op.drop_constraint(op.f('fk_users_main_language_id_languages'), 'users', type_='foreignkey')
    op.create_foreign_key('fk_users_language_id_languages', 'users', 'languages', ['language_id'], ['id'])
    op.drop_column('users', 'main_language_id')
    op.drop_constraint(op.f('fk_configuration_products_product_id_products'), 'configuration_products', type_='foreignkey')
    op.create_foreign_key('fk_configuration_products_product_id_products', 'configuration_products', 'products', ['product_id'], ['id'])
    # ### end Alembic commands ###
