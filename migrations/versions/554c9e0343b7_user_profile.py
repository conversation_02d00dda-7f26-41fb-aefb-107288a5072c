"""user profile

Revision ID: 554c9e0343b7
Revises: 02423c1e370e
Create Date: 2023-11-09 12:19:11.566390

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '554c9e0343b7'
down_revision: Union[str, None] = '02423c1e370e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('users',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('last_name', sa.String(length=255), nullable=True),
    sa.Column('external_links', sa.JSON(), nullable=True),
    sa.Column('currency', sa.Enum('USD', 'EUR', name='currency'), nullable=True),
    sa.Column('country', sa.String(length=255), nullable=True),
    sa.Column('sf_proposition', sa.String(length=255), nullable=True),
    sa.Column('language_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['language_id'], ['languages.id'], name=op.f('fk_users_language_id_languages')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_users'))
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    # ### end Alembic commands ###
