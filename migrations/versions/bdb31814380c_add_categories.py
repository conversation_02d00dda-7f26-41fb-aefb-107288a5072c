"""add categories

Revision ID: bdb31814380c
Revises: 2d22ba10e9f2
Create Date: 2023-12-13 16:20:53.642110

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'bdb31814380c'
down_revision: Union[str, None] = '2d22ba10e9f2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('categories',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('umid', sa.String(length=50), nullable=True),
    sa.Column('parent_id', sa.Uuid(), nullable=True),
    sa.Column('category_umid', sa.Uuid(), nullable=True),
    sa.Column('category_id', sa.String(length=255), nullable=True),
    sa.ForeignKeyConstraint(['parent_id'], ['categories.id'], name=op.f('fk_categories_parent_id_categories')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_categories')),
    sa.UniqueConstraint('umid', name=op.f('uq_categories_umid'))
    )
    op.create_table('category_translations',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('language_id', sa.Uuid(), nullable=True),
    sa.Column('category_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['categories.id'], name=op.f('fk_category_translations_category_id_categories')),
    sa.ForeignKeyConstraint(['language_id'], ['languages.id'], name=op.f('fk_category_translations_language_id_languages')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_category_translations'))
    )
    op.create_table('category_association_table',
    sa.Column('category_id', sa.Uuid(), nullable=False),
    sa.Column('product_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['category_id'], ['categories.id'], name=op.f('fk_category_association_table_category_id_categories')),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], name=op.f('fk_category_association_table_product_id_products')),
    sa.PrimaryKeyConstraint('category_id', 'product_id', name=op.f('pk_category_association_table'))
    )
    op.add_column('assets', sa.Column('category_id', sa.Uuid(), nullable=True))
    op.create_foreign_key(op.f('fk_assets_category_id_categories'), 'assets', 'categories', ['category_id'], ['id'])
    op.alter_column('product_text_translations', 'value',
               existing_type=mysql.VARCHAR(length=1024),
               type_=sa.String(length=2048),
               existing_nullable=True)
    op.drop_column('users', 'external_links')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('external_links', mysql.JSON(), nullable=True))
    op.alter_column('product_text_translations', 'value',
               existing_type=sa.String(length=2048),
               type_=mysql.VARCHAR(length=1024),
               existing_nullable=True)
    op.drop_constraint(op.f('fk_assets_category_id_categories'), 'assets', type_='foreignkey')
    op.drop_column('assets', 'category_id')
    op.drop_table('category_association_table')
    op.drop_table('category_translations')
    op.drop_table('categories')
    # ### end Alembic commands ###
