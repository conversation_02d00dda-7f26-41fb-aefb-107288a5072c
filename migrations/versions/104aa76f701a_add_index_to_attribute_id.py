"""Add index to attribute id

Revision ID: 104aa76f701a
Revises: 3110df4cd57c
Create Date: 2024-02-01 10:03:21.501803

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '104aa76f701a'
down_revision: Union[str, None] = '3110df4cd57c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('attribute_id_idx', 'attributes', ['attribute_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('attribute_id_idx', table_name='attributes')
    # ### end Alembic commands ###
