"""merge_multiple_heads

Revision ID: 838dcc700520
Revises: bffa750aac82, 251008978ee1
Create Date: 2025-07-22 16:26:06.109475

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '838dcc700520'
down_revision: Union[str, None] = ('bffa750aac82', '251008978ee1')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
