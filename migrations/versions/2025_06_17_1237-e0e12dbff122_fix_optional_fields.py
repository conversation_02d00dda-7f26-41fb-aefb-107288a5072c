"""Fix optional fields

Revision ID: e0e12dbff122
Revises: f14f15f29251
Create Date: 2025-06-17 12:37:01.479447

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'e0e12dbff122'
down_revision: Union[str, None] = 'f14f15f29251'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('categories', 'category_id',
               existing_type=mysql.VARCHAR(length=255),
               nullable=False)
    op.create_unique_constraint(op.f('uq_categories_category_id'), 'categories', ['category_id'])
    op.alter_column('configuration_data', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=False,
               existing_server_default=sa.text('(now())'))
    op.alter_column('configuration_data', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=False,
               existing_server_default=sa.text('(now())'))
    op.alter_column('configurations', 'started_at',
               existing_type=mysql.DATETIME(),
               nullable=False,
               existing_server_default=sa.text('(now())'))
    op.alter_column('configurations', 'hidden',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=False)
    op.alter_column('product_text_translations', 'type',
               existing_type=mysql.ENUM('long_description', 'features', 'supplied_with', 'available_on_request', 'accessories', 'additional_information', 'other'),
               nullable=False)
    op.alter_column('products', 'product_id',
               existing_type=mysql.INTEGER(),
               nullable=False)
    op.alter_column('products', 'umid',
               existing_type=mysql.CHAR(length=32),
               nullable=False)
    op.alter_column('products', 'article_number',
               existing_type=mysql.VARCHAR(length=20),
               nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('products', 'article_number',
               existing_type=mysql.VARCHAR(length=20),
               nullable=True)
    op.alter_column('products', 'umid',
               existing_type=mysql.CHAR(length=32),
               nullable=True)
    op.alter_column('products', 'product_id',
               existing_type=mysql.INTEGER(),
               nullable=True)
    op.alter_column('product_text_translations', 'type',
               existing_type=mysql.ENUM('long_description', 'features', 'supplied_with', 'available_on_request', 'accessories', 'additional_information', 'other'),
               nullable=True)
    op.alter_column('configurations', 'hidden',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=True)
    op.alter_column('configurations', 'started_at',
               existing_type=mysql.DATETIME(),
               nullable=True,
               existing_server_default=sa.text('(now())'))
    op.alter_column('configuration_data', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=True,
               existing_server_default=sa.text('(now())'))
    op.alter_column('configuration_data', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=True,
               existing_server_default=sa.text('(now())'))
    op.drop_constraint(op.f('uq_categories_category_id'), 'categories', type_='unique')
    op.alter_column('categories', 'category_id',
               existing_type=mysql.VARCHAR(length=255),
               nullable=True)
    # ### end Alembic commands ###
