"""asset_types_enums

Revision ID: 8e97f8c0bf80
Revises: 7924489642cf
Create Date: 2023-11-07 16:04:49.159969

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8e97f8c0bf80'
down_revision: Union[str, None] = '7924489642cf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('assets', sa.Column('asset_type', sa.Enum('PRODUCT_MAIN_APPLICATION_SHOT', 'PRODUCT_APPLICATION_SHOT', 'PRODUCT_APPLICATION_SHOTS_WEB', 'PRODUCT_SHOTS', 'PRODUCT_GUIDE', 'PRODUCT_RELATED_PICTURES', 'PRODUCT_CHARACTERISTICS', 'PRODUCT_DIAGRAM', 'PRODUCT_TECHNICAL_DRAWING', 'PRODUCT_TECHNICAL_SPECIFICATION_SHEET', 'PRODUCT_MAIN_IMAGE', 'PRODUCT_CATEGORY_MAIN_IMAGE', name='assettype'), nullable=True))
    op.add_column('assets', sa.Column('char_order', sa.Integer(), nullable=True))
    op.add_column('assets_metadata_translations', sa.Column('asset_label', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('assets_metadata_translations', 'asset_label')
    op.drop_column('assets', 'char_order')
    op.drop_column('assets', 'asset_type')
    # ### end Alembic commands ###
