"""Add quote_number, last_updated_state and hidden to Configuration

Revision ID: 02423c1e370e
Revises: 8e97f8c0bf80
Create Date: 2023-11-07 15:44:06.975780

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '02423c1e370e'
down_revision: Union[str, None] = '8e97f8c0bf80'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('configurations', sa.Column('quotation_number', sa.String(length=255), nullable=True, comment='Quotation number generated in the portal'))
    op.add_column('configurations', sa.Column('last_updated_state', sa.String(length=255), nullable=True, comment='ID of the state last saved'))
    op.add_column('configurations', sa.Column('hidden', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('configurations', 'hidden')
    op.drop_column('configurations', 'last_updated_state')
    op.drop_column('configurations', 'quotation_number')
    # ### end Alembic commands ###
