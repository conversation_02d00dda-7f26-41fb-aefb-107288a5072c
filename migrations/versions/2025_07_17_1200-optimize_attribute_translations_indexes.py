"""Optimize AttributeTranslation indexes for performance

Revision ID: 251008978ee1
Revises: 2e55ffb7018d
Create Date: 2025-07-17 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '251008978ee1'
down_revision: Union[str, None] = '2e55ffb7018d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create composite index for the most common query pattern:
    # filtering by attribute_id and language_id with non-null name and attribute_unit
    op.create_index(
        'ix_attribute_translations_attr_lang_composite',
        'attribute_translations',
        ['attribute_id', 'language_id'],
        unique=False
    )
    
    # Create index for filtering by language_id (used in language_code IN queries)
    op.create_index(
        'ix_attribute_translations_language_id',
        'attribute_translations',
        ['language_id'],
        unique=False
    )
    
    # Create partial index for non-null name and attribute_unit (common filter condition)
    # Note: MySQL doesn't support partial indexes like PostgreSQL, so we create a regular index
    # on name and attribute_unit columns for better performance on NOT NULL filters
    op.create_index(
        'ix_attribute_translations_name_unit',
        'attribute_translations',
        ['name', 'attribute_unit'],
        unique=False
    )
    
    # Create composite index for the opensearch query pattern specifically:
    # attribute_id + language_id + name + attribute_unit (covering index)
    op.create_index(
        'ix_attribute_translations_covering',
        'attribute_translations',
        ['attribute_id', 'language_id', 'name', 'attribute_unit'],
        unique=False
    )


def downgrade() -> None:
    # Drop the indexes in reverse order
    op.drop_index('ix_attribute_translations_covering', table_name='attribute_translations')
    op.drop_index('ix_attribute_translations_name_unit', table_name='attribute_translations')
    op.drop_index('ix_attribute_translations_language_id', table_name='attribute_translations')
    op.drop_index('ix_attribute_translations_attr_lang_composite', table_name='attribute_translations')
