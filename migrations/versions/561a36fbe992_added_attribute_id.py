"""added-attribute-id

Revision ID: 561a36fbe992
Revises: 5c36f291842a
Create Date: 2023-11-21 10:43:07.523954

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '561a36fbe992'
down_revision: Union[str, None] = '5c36f291842a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('attributes', sa.Column('attribute_id', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('attributes', 'attribute_id')
    # ### end Alembic commands ###
