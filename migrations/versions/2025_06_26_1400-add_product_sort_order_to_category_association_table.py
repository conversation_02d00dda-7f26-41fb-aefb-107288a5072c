"""add product_sort_order to category_association_table

Revision ID: 34f827324879
Revises: 9f99ee6771c8
Create Date: 2025-06-26 14:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '34f827324879'
down_revision: Union[str, None] = '9f99ee6771c8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add the product_sort_order column to category_association_table
    op.add_column('category_association_table', 
                  sa.Column('product_sort_order', sa.Integer(), nullable=True))


def downgrade() -> None:
    # Remove the product_sort_order column from category_association_table
    op.drop_column('category_association_table', 'product_sort_order')
