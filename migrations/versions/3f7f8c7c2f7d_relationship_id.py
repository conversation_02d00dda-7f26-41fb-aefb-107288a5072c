"""relationship id

Revision ID: 3f7f8c7c2f7d
Revises: 5b898ff57454
Create Date: 2023-12-07 09:05:29.697179

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3f7f8c7c2f7d'
down_revision: Union[str, None] = '5b898ff57454'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('related_products', sa.Column('relationship_id', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('related_products', 'relationship_id')
    # ### end Alembic commands ###
