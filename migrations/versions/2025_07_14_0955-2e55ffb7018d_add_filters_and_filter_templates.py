"""Add filters and filter templates

Revision ID: 2e55ffb7018d
Revises: 34f827324879
Create Date: 2025-07-14 09:55:59.627713

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2e55ffb7018d'
down_revision: Union[str, None] = '34f827324879'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('filter_templates',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_filter_templates'))
    )
    op.create_table('category_filter_template_association_table',
    sa.Column('category_id', sa.Uuid(), nullable=False),
    sa.Column('filter_template_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['category_id'], ['categories.id'], name=op.f('fk_category_filter_template_association_table_category_id_categories'), ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['filter_template_id'], ['filter_templates.id'], name=op.f('fk_category_filter_template_association_table_filter_template_id_filter_templates'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('category_id', 'filter_template_id', name=op.f('pk_category_filter_template_association_table'))
    )
    op.create_table('filter_template_translations',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('language_id', sa.Uuid(), nullable=True),
    sa.Column('filter_template_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['filter_template_id'], ['filter_templates.id'], name=op.f('fk_filter_template_translations_filter_template_id_filter_templates'), ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['language_id'], ['languages.id'], name=op.f('fk_filter_template_translations_language_id_languages')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_filter_template_translations'))
    )
    op.create_table('filters',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('attribute_id', sa.Integer(), nullable=False, comment='SyncForce attribute ID'),
    sa.Column('filter_type', sa.Enum('CHECKBOX', 'SLIDER', name='filtertype'), nullable=False),
    sa.Column('position', sa.Integer(), nullable=False, comment='Position of this filter in the template'),
    sa.Column('multi_select_logic', sa.Enum('AND', 'OR', name='multiselectlogic'), nullable=True),
    sa.Column('is_visible', sa.Boolean(), nullable=False),
    sa.Column('nr_of_shown_values', sa.Integer(), nullable=False),
    sa.Column('show_nr_of_results', sa.Boolean(), nullable=False),
    sa.Column('filter_template_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['filter_template_id'], ['filter_templates.id'], name=op.f('fk_filters_filter_template_id_filter_templates')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_filters'))
    )
    op.create_index(op.f('ix_filters_attribute_id'), 'filters', ['attribute_id'], unique=False)
    op.create_index(op.f('ix_filters_position'), 'filters', ['position'], unique=False)
    op.create_table('filter_translations',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('language_id', sa.Uuid(), nullable=True),
    sa.Column('filter_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['filter_id'], ['filters.id'], name=op.f('fk_filter_translations_filter_id_filters'), ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['language_id'], ['languages.id'], name=op.f('fk_filter_translations_language_id_languages')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_filter_translations'))
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('filter_translations')
    op.drop_index(op.f('ix_filters_position'), table_name='filters')
    op.drop_index(op.f('ix_filters_attribute_id'), table_name='filters')
    op.drop_table('filters')
    op.drop_table('filter_template_translations')
    op.drop_table('category_filter_template_association_table')
    op.drop_table('filter_templates')
    # ### end Alembic commands ###
