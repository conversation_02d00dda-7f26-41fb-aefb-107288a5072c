"""associate existing configurations with Lifting

Revision ID: 0358b5446bac
Revises: 06fdedcf33b3
Create Date: 2024-04-03 09:47:31.179860

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0358b5446bac'
down_revision: Union[str, None] = '06fdedcf33b3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Get database connection
    connection = op.get_bind()

    # Find the lifting category with category_id "7411"
    lifting_result = connection.execute(
        sa.text("SELECT id FROM categories WHERE category_id = :category_id"),
        {"category_id": "7411"}
    ).fetchone()

    # If lifting category exists, update configurations with NULL category_id
    if lifting_result:
        lifting_id = lifting_result[0]
        connection.execute(
            sa.text("UPDATE configurations SET category_id = :lifting_id WHERE category_id IS NULL"),
            {"lifting_id": lifting_id}
        )

def downgrade() -> None:
    pass