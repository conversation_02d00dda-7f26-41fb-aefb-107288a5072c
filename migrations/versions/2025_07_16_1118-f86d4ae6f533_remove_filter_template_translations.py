"""Remove filter template translations

Revision ID: f86d4ae6f533
Revises: 2e55ffb7018d
Create Date: 2025-07-16 11:18:33.487847

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'f86d4ae6f533'
down_revision: Union[str, None] = '2e55ffb7018d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('filter_template_translations')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('filter_template_translations',
    sa.Column('id', mysql.CHAR(collation='utf8mb4_unicode_ci', length=32), nullable=False),
    sa.Column('name', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=255), nullable=True),
    sa.Column('language_id', mysql.CHAR(collation='utf8mb4_unicode_ci', length=32), nullable=True),
    sa.Column('filter_template_id', mysql.CHAR(collation='utf8mb4_unicode_ci', length=32), nullable=True),
    sa.ForeignKeyConstraint(['filter_template_id'], ['filter_templates.id'], name=op.f('fk_filter_template_translations_filter_template_id_filte_c88a'), ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['language_id'], ['languages.id'], name=op.f('fk_filter_template_translations_language_id_languages')),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_unicode_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    # ### end Alembic commands ###
