"""Store anonymous property to user

Revision ID: 50d566b1b5b2
Revises: 554d3562c7de
Create Date: 2024-10-15 14:41:26.793241

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '50d566b1b5b2'
down_revision: Union[str, None] = '554d3562c7de'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('users', sa.Column('anonymous', sa.<PERSON>an(), nullable=False, server_default=sa.false()))


def downgrade() -> None:
    op.drop_column('users', 'anonymous')
