"""Associate configuration with category

Revision ID: 39b80b5e4f5c
Revises: cbca9e015f64
Create Date: 2024-03-27 09:59:35.614183

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '39b80b5e4f5c'
down_revision: Union[str, None] = 'cbca9e015f64'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fk_configuration_products_product_id_products', 'configuration_products', type_='foreignkey')
    op.create_foreign_key(op.f('fk_configuration_products_product_id_products'), 'configuration_products', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    op.add_column('configurations', sa.Column('category_id', sa.Uuid(), nullable=True))
    op.create_foreign_key(op.f('fk_configurations_category_id_categories'), 'configurations', 'categories', ['category_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f('fk_configurations_category_id_categories'), 'configurations', type_='foreignkey')
    op.drop_column('configurations', 'category_id')
    op.drop_constraint(op.f('fk_configuration_products_product_id_products'), 'configuration_products', type_='foreignkey')
    op.create_foreign_key('fk_configuration_products_product_id_products', 'configuration_products', 'products', ['product_id'], ['id'])
    # ### end Alembic commands ###
