"""alter configuration_products primarykey

Revision ID: cbca9e015f64
Revises: 115146becb79
Create Date: 2024-03-18 08:50:30.975484

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cbca9e015f64'
down_revision: Union[str, None] = '115146becb79'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Drop foreign key constraints first
    op.drop_constraint('fk_configuration_products_configuration_id_configurations', 'configuration_products',
                       type_='foreignkey')
    op.drop_constraint('fk_configuration_products_product_id_products', 'configuration_products',
                       type_='foreignkey')

    # Drop primary key constraint if it exists
    op.drop_constraint('pk_configuration_products', 'configuration_products', type_='primary')

    # Re-create the foreign key constraints
    op.create_foreign_key('fk_configuration_products_configuration_id_configurations', 'configuration_products',
                          'configurations', ['configuration_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('fk_configuration_products_product_id_products', 'configuration_products', 'products',
                          ['product_id'], ['id'])


def downgrade() -> None:
    # Drop foreign key constraints first
    op.drop_constraint('fk_configuration_products_configuration_id_configurations', 'configuration_products',
                       type_='foreignkey')
    op.drop_constraint('fk_configuration_products_product_id_products', 'configuration_products',
                       type_='foreignkey')

    # Re-create the primary key constraint
    op.create_primary_key('pk_configuration_products', 'configuration_products', ['configuration_id', 'product_id', 'state_id'])

    # Re-create the foreign key constraints
    op.create_foreign_key('fk_configuration_products_configuration_id_configurations', 'configuration_products',
                          'configurations', ['configuration_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('fk_configuration_products_product_id_products', 'configuration_products', 'products',
                          ['product_id'], ['id'])
