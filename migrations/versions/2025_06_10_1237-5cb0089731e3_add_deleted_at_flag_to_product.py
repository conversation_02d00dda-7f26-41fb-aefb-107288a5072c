"""Add deleted_at flag to Product

Revision ID: 5cb0089731e3
Revises: a6364c2dcf63
Create Date: 2025-06-10 12:37:53.680819

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5cb0089731e3'
down_revision: Union[str, None] = 'a6364c2dcf63'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('products', sa.Column('deleted_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('products', 'deleted_at')
    # ### end Alembic commands ###
