"""empty message

Revision ID: 14202daca6dc
Revises: 40b64ab4a036
Create Date: 2023-10-31 10:02:30.312866

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '14202daca6dc'
down_revision: Union[str, None] = '40b64ab4a036'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('assets',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('udai', sa.Uuid(), nullable=True),
    sa.Column('role_id', sa.Integer(), nullable=True),
    sa.Column('asset_id', sa.Integer(), nullable=True),
    sa.Column('product_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], name=op.f('fk_assets_product_id_products')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_assets'))
    )
    op.create_table('asset_resources',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('url', sa.String(length=255), nullable=True),
    sa.Column('asset_type', sa.String(length=255), nullable=True),
    sa.Column('file_name', sa.String(length=255), nullable=True),
    sa.Column('language_id', sa.Uuid(), nullable=True),
    sa.Column('asset_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['asset_id'], ['assets.id'], name=op.f('fk_asset_resources_asset_id_assets')),
    sa.ForeignKeyConstraint(['language_id'], ['languages.id'], name=op.f('fk_asset_resources_language_id_languages')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_asset_resources'))
    )
    op.create_table('assets_metadata_translations',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('asset_type', sa.String(length=255), nullable=True),
    sa.Column('asset_sub_types_singular', sa.String(length=255), nullable=True),
    sa.Column('asset_sub_types_plural', sa.String(length=255), nullable=True),
    sa.Column('asset_role', sa.String(length=255), nullable=True),
    sa.Column('language_id', sa.Uuid(), nullable=True),
    sa.Column('asset_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['asset_id'], ['assets.id'], name=op.f('fk_assets_metadata_translations_asset_id_assets')),
    sa.ForeignKeyConstraint(['language_id'], ['languages.id'], name=op.f('fk_assets_metadata_translations_language_id_languages')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_assets_metadata_translations'))
    )
    op.create_table('asset_resource_language_association',
    sa.Column('asset_resource_id', sa.Uuid(), nullable=False),
    sa.Column('language_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['asset_resource_id'], ['asset_resources.id'], name=op.f('fk_asset_resource_language_association_asset_resource_id_asset_resources')),
    sa.ForeignKeyConstraint(['language_id'], ['languages.id'], name=op.f('fk_asset_resource_language_association_language_id_languages')),
    sa.PrimaryKeyConstraint('asset_resource_id', 'language_id', name=op.f('pk_asset_resource_language_association'))
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('asset_resource_language_association')
    op.drop_table('assets_metadata_translations')
    op.drop_table('asset_resources')
    op.drop_table('assets')
    # ### end Alembic commands ###
