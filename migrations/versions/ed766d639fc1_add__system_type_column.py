"""Add preferred_system_type column

Revision ID: ed766d639fc1
Revises: 50d566b1b5b2
Create Date: 2024-10-29 15:58:06.457724

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'ed766d639fc1'
down_revision: Union[str, None] = '50d566b1b5b2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('users', sa.Column('preferred_system_type', sa.Enum('METRIC', 'IMPERIAL', name='systemtype'), nullable=True))


def downgrade() -> None:
    op.drop_column('users', 'preferred_system_type')
