"""Relate configuration to user

Revision ID: 93ab7dec1390
Revises: 554c9e0343b7
Create Date: 2023-11-10 15:53:46.957995

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '93ab7dec1390'
down_revision: Union[str, None] = '554c9e0343b7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('configurations', sa.Column('user_id', sa.Uuid(), nullable=False))
    op.create_foreign_key(op.f('fk_configurations_user_id_users'), 'configurations', 'users', ['user_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f('fk_configurations_user_id_users'), 'configurations', type_='foreignkey')
    op.drop_column('configurations', 'user_id')
    # ### end Alembic commands ###
