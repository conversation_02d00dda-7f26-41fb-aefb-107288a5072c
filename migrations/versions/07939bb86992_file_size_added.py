"""file_size added

Revision ID: 07939bb86992
Revises: 00210426f0a6
Create Date: 2023-11-15 14:56:59.855683

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '07939bb86992'
down_revision: Union[str, None] = '00210426f0a6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('asset_resources', sa.Column('size', sa.Numeric(precision=10, scale=2), nullable=True))
    op.add_column('classifications', sa.Column('classification_id', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('classifications', 'classification_id')
    op.drop_column('asset_resources', 'size')
    # ### end Alembic commands ###
