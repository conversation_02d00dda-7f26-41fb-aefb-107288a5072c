"""ConfigurationProduct state_id as primare_key

Revision ID: c826b3b41ad3
Revises: 3f7f8c7c2f7d
Create Date: 2023-12-07 14:41:34.839579

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'c826b3b41ad3'
down_revision: Union[str, None] = '3f7f8c7c2f7d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    op.create_table(
        'new_configuration_products',
        sa.Column('configuration_id', sa.Uuid(), nullable=False),
        sa.Column('product_id', sa.Uuid(), nullable=False),
        sa.Column('state_id', sa.String(length=255), nullable=False),
        sa.Column('quantity', sa.<PERSON>Integer(), nullable=False),
        sa.PrimaryKeyConstraint('configuration_id', 'product_id', 'state_id', name=op.f('pk_configuration_products'))
    )

    op.execute(
        f"INSERT INTO new_configuration_products (configuration_id, product_id, state_id, quantity) "
        f"SELECT configuration_id, product_id, state_id, quantity FROM configuration_products"
    )
    op.drop_table('configuration_products')
    op.rename_table('new_configuration_products', 'configuration_products')

    op.create_foreign_key('fk_configuration_products_configuration_id_configurations', 'configuration_products',
                          'configurations', ['configuration_id'], ['id'])
    op.create_foreign_key('fk_configuration_products_product_id_products', 'configuration_products', 'products', ['product_id'], ['id'])


def downgrade():
    # It might have issues while downgrading if the configuration_products table have the same product more than once for one configuration

    op.create_table(
        "new_configuration_products",
        sa.Column('configuration_id', sa.Uuid(), nullable=False),
        sa.Column('product_id', sa.Uuid(), nullable=False),
        sa.Column('state_id', sa.String(length=255), nullable=False),
        sa.Column('quantity', sa.SmallInteger(), nullable=False),
        sa.PrimaryKeyConstraint('configuration_id', 'product_id', name=op.f('pk_configuration_products'))
    )
    op.execute(
        f"INSERT INTO new_configuration_products (configuration_id, product_id, state_id, quantity) "
        f"SELECT configuration_id, product_id, state_id, quantity FROM configuration_products"
    )
    op.drop_table('configuration_products')
    op.rename_table('new_configuration_products', 'configuration_products')

    op.create_foreign_key('fk_configuration_products_product_id_products', 'configuration_products', 'products',
                          ['product_id'], ['id'])
    op.create_foreign_key('fk_configuration_products_configuration_id_configurations', 'configuration_products',
                          'configurations', ['configuration_id'], ['id'])
