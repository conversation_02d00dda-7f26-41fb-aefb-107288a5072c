"""Add sort index on Categorie

Revision ID: 9f99ee6771c8
Revises: e0e12dbff122
Create Date: 2025-06-19 14:31:06.155740

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9f99ee6771c8'
down_revision: Union[str, None] = 'e0e12dbff122'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('categories', sa.Column('sort_index', sa.Integer(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('categories', 'sort_index')
    # ### end Alembic commands ###
