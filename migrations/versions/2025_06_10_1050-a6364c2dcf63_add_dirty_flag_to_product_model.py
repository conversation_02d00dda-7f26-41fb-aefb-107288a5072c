"""Add dirty flag to Product model

Revision ID: a6364c2dcf63
Revises: ed766d639fc1
Create Date: 2025-06-10 10:50:52.243638

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a6364c2dcf63'
down_revision: Union[str, None] = 'ed766d639fc1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('products', sa.Column('dirty', sa.Boolean(), nullable=False, server_default=sa.true(), comment='Indicates if a product has been updated by the data import'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('products', 'dirty')
    # ### end Alembic commands ###
