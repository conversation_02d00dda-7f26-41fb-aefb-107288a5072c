"""ConfigurationProduct position

Revision ID: 115146becb79
Revises: 104aa76f701a
Create Date: 2024-02-02 15:06:16.786864

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '115146becb79'
down_revision: Union[str, None] = '104aa76f701a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('configuration_products', sa.Column('position', sa.SmallInteger(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('configuration_products', 'position')
    # ### end Alembic commands ###
