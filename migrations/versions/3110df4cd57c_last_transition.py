"""last_transition

Revision ID: 3110df4cd57c
Revises: ef6d3c208d08
Create Date: 2024-01-17 09:56:40.148628

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '3110df4cd57c'
down_revision: Union[str, None] = 'ef6d3c208d08'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('configurations', sa.Column('last_transition', sa.String(length=255), nullable=True, comment='The transition requested'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('configurations', 'last_transition')
    # ### end Alembic commands ###
