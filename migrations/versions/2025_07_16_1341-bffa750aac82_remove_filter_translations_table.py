"""Remove filter translations table

Revision ID: bffa750aac82
Revises: 16a2e8280086
Create Date: 2025-07-16 13:41:44.867099

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'bffa750aac82'
down_revision: Union[str, None] = '16a2e8280086'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('filter_translations')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('filter_translations',
    sa.Column('id', mysql.CHAR(collation='utf8mb4_unicode_ci', length=32), nullable=False),
    sa.Column('title', mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=255), nullable=True),
    sa.Column('language_id', mysql.CHAR(collation='utf8mb4_unicode_ci', length=32), nullable=True),
    sa.Column('filter_id', mysql.CHAR(collation='utf8mb4_unicode_ci', length=32), nullable=True),
    sa.ForeignKeyConstraint(['filter_id'], ['filters.id'], name=op.f('fk_filter_translations_filter_id_filters'), ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['language_id'], ['languages.id'], name=op.f('fk_filter_translations_language_id_languages')),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_unicode_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    # ### end Alembic commands ###
