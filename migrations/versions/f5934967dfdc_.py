"""Attribute values

Revision ID: f5934967dfdc
Revises: 14202daca6dc
Create Date: 2023-11-01 17:42:37.071560

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'f5934967dfdc'
down_revision: Union[str, None] = '14202daca6dc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fk_asset_resources_language_id_languages', 'asset_resources', type_='foreignkey')
    op.drop_column('asset_resources', 'language_id')
    op.add_column('attribute_translations', sa.Column('attribute_group_name', sa.String(length=255), nullable=True))
    op.add_column('attribute_translations', sa.Column('attribute_value', sa.String(length=255), nullable=True))
    op.add_column('attribute_translations', sa.Column('attribute_unit', sa.String(length=255), nullable=True))
    op.add_column('attributes', sa.Column('system_type', sa.Enum('METRIC', 'IMPERIAL', name='systemtype'), nullable=True))
    op.add_column('attributes', sa.Column('content', sa.String(length=255), nullable=True))
    op.add_column('attributes', sa.Column('unit_type', sa.String(length=255), nullable=True))
    op.add_column('attributes', sa.Column('multi_lingual_values', sa.Boolean(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('attributes', 'multi_lingual_values')
    op.drop_column('attributes', 'unit_type')
    op.drop_column('attributes', 'content')
    op.drop_column('attributes', 'system_type')
    op.drop_column('attribute_translations', 'attribute_unit')
    op.drop_column('attribute_translations', 'attribute_value')
    op.drop_column('attribute_translations', 'attribute_group_name')
    op.add_column('asset_resources', sa.Column('language_id', mysql.CHAR(length=32), nullable=True))
    op.create_foreign_key('fk_asset_resources_language_id_languages', 'asset_resources', 'languages', ['language_id'], ['id'])
    # ### end Alembic commands ###
