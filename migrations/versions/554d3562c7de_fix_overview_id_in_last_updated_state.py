"""fix overview id in last updated state

Revision ID: 554d3562c7de
Revises: 21cbe8b31382
Create Date: 2024-04-15 11:05:56.100997

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '554d3562c7de'
down_revision: Union[str, None] = '21cbe8b31382'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute(
        """
        UPDATE configurations
        SET last_updated_state = 'industrial.overview.main'
        WHERE last_updated_state LIKE 'industrial.lifting.overview.main'
        """
    )


def downgrade() -> None:
    op.execute(
        """
        UPDATE configurations
        SET last_updated_state = 'industrial.lifting.overview.main'
        WHERE last_updated_state LIKE 'industrial.overview.main'
        """
    )