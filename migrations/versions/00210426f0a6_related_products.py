"""related products

Revision ID: 00210426f0a6
Revises: 93ab7dec1390
Create Date: 2023-11-14 16:16:58.306893

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '00210426f0a6'
down_revision: Union[str, None] = '93ab7dec1390'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('related_products',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('main_product_id', sa.Uuid(), nullable=False),
    sa.Column('related_product_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['main_product_id'], ['products.id'], name=op.f('fk_related_products_main_product_id_products')),
    sa.ForeignKeyConstraint(['related_product_id'], ['products.id'], name=op.f('fk_related_products_related_product_id_products')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_related_products'))
    )
    op.create_table('relationship_translations',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('related_product_id', sa.Uuid(), nullable=True),
    sa.Column('language_id', sa.Uuid(), nullable=True),
    sa.Column('translated_name', sa.String(length=255), nullable=True),
    sa.ForeignKeyConstraint(['language_id'], ['languages.id'], name=op.f('fk_relationship_translations_language_id_languages')),
    sa.ForeignKeyConstraint(['related_product_id'], ['related_products.id'], name=op.f('fk_relationship_translations_related_product_id_related_products')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_relationship_translations'))
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('relationship_translations')
    op.drop_table('related_products')
    # ### end Alembic commands ###
