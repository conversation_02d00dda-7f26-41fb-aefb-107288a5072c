"""Rename configurator ids

Revision ID: 06fdedcf33b3
Revises: 39b80b5e4f5c
Create Date: 2024-03-28 09:57:47.585019

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

from holmatro_customer_portal.utils.database import construct_db_write_url, session_creator


# revision identifiers, used by Alembic.
revision: str = '06fdedcf33b3'
down_revision: Union[str, None] = '39b80b5e4f5c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute(
        """
        UPDATE configurations
        SET last_updated_state = CONCAT('industrial.lifting.', last_updated_state)
        WHERE last_updated_state NOT LIKE 'industrial.%'
        """
    )
    op.execute(
        """
        UPDATE configuration_data
        SET state_id = CONCAT('industrial.lifting.', state_id),
        attribute_id = CONCAT('industrial.lifting.', attribute_id)
        WHERE state_id NOT LIKE 'industrial.%'
        """
    )
    op.execute(
        """
        UPDATE configuration_products
        SET state_id = CONCAT('industrial.lifting.', state_id)
        WHERE state_id NOT LIKE 'industrial.%'
        """
    )


def downgrade() -> None:
    op.execute(
        """
        UPDATE configurations
        SET last_updated_state = SUBSTR(last_updated_state, 20)
        WHERE last_updated_state LIKE 'industrial.lifting.%'
        """
    )
    op.execute(
        """
        UPDATE configuration_data
        SET state_id = SUBSTR(state_id, 20),
        attribute_id = SUBSTR(attribute_id, 20)
        WHERE state_id LIKE 'industrial.lifting.%'
        """
    )
    op.execute(
        """
        UPDATE configuration_products
        SET state_id = SUBSTR(state_id, 20)
        WHERE state_id LIKE 'industrial.lifting.%'
        """
    )
