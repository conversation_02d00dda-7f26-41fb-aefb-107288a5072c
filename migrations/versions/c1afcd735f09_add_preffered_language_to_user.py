"""add preffered language to user

Revision ID: c1afcd735f09
Revises: 561a36fbe992
Create Date: 2023-11-23 13:23:22.634784

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c1afcd735f09'
down_revision: Union[str, None] = '561a36fbe992'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('preferred_language_id', sa.Uuid(), nullable=True))
    op.create_foreign_key(op.f('fk_users_preferred_language_id_languages'), 'users', 'languages', ['preferred_language_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f('fk_users_preferred_language_id_languages'), 'users', type_='foreignkey')
    op.drop_column('users', 'preferred_language_id')
    # ### end Alembic commands ###
