"""Initial migration

Revision ID: 40b64ab4a036
Revises: 
Create Date: 2023-10-19 12:42:05.873594

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '40b64ab4a036'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('brands',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('brand_name', sa.String(length=255), nullable=True),
    sa.Column('brand_type', sa.String(length=255), nullable=True),
    sa.Column('gbin', sa.String(length=255), nullable=True),
    sa.Column('owner_name', sa.String(length=255), nullable=True),
    sa.Column('owner_gln', sa.String(length=255), nullable=True),
    sa.Column('reference', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_brands'))
    )
    op.create_table('classifications',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('material_type', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_classifications'))
    )
    op.create_table('hierarchy_levels',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('hierarchy_level_id', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_hierarchy_levels'))
    )
    op.create_table('languages',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('language_code', sa.String(length=50), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_languages')),
    sa.UniqueConstraint('language_code', name=op.f('uq_languages_language_code'))
    )
    op.create_table('classification_translations',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('language_id', sa.Uuid(), nullable=True),
    sa.Column('classification_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['classification_id'], ['classifications.id'], name=op.f('fk_classification_translations_classification_id_classifications')),
    sa.ForeignKeyConstraint(['language_id'], ['languages.id'], name=op.f('fk_classification_translations_language_id_languages')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_classification_translations'))
    )
    op.create_table('hierarchy_level_translations',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('translation', sa.String(length=255), nullable=True),
    sa.Column('hierarchy_level_id', sa.Uuid(), nullable=True),
    sa.Column('language_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['hierarchy_level_id'], ['hierarchy_levels.id'], name=op.f('fk_hierarchy_level_translations_hierarchy_level_id_hierarchy_levels')),
    sa.ForeignKeyConstraint(['language_id'], ['languages.id'], name=op.f('fk_hierarchy_level_translations_language_id_languages')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_hierarchy_level_translations'))
    )
    op.create_table('products',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=True),
    sa.Column('umid', sa.Uuid(), nullable=True),
    sa.Column('part_nr', sa.String(length=20), nullable=True),
    sa.Column('gtin', sa.String(length=255), nullable=True),
    sa.Column('article_number', sa.String(length=20), nullable=True),
    sa.Column('master_code', sa.String(length=20), nullable=True),
    sa.Column('brand_id', sa.Uuid(), nullable=True),
    sa.Column('classification_id', sa.Uuid(), nullable=True),
    sa.Column('hierarchy_level_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['brand_id'], ['brands.id'], name=op.f('fk_products_brand_id_brands')),
    sa.ForeignKeyConstraint(['classification_id'], ['classifications.id'], name=op.f('fk_products_classification_id_classifications')),
    sa.ForeignKeyConstraint(['hierarchy_level_id'], ['hierarchy_levels.id'], name=op.f('fk_products_hierarchy_level_id_hierarchy_levels')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_products'))
    )
    op.create_table('attributes',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('type', sa.String(length=255), nullable=True),
    sa.Column('is_localized', sa.Boolean(), nullable=True),
    sa.Column('is_read_only', sa.Boolean(), nullable=True),
    sa.Column('product_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], name=op.f('fk_attributes_product_id_products')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_attributes'))
    )
    op.create_table('master_product_names',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('value', sa.String(length=255), nullable=True),
    sa.Column('language_id', sa.Uuid(), nullable=True),
    sa.Column('product_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['language_id'], ['languages.id'], name=op.f('fk_master_product_names_language_id_languages')),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], name=op.f('fk_master_product_names_product_id_products')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_master_product_names'))
    )
    op.create_table('product_language_association_table',
    sa.Column('products_id', sa.Uuid(), nullable=False),
    sa.Column('language_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['language_id'], ['languages.id'], name=op.f('fk_product_language_association_table_language_id_languages')),
    sa.ForeignKeyConstraint(['products_id'], ['products.id'], name=op.f('fk_product_language_association_table_products_id_products')),
    sa.PrimaryKeyConstraint('products_id', 'language_id', name=op.f('pk_product_language_association_table'))
    )
    op.create_table('product_names',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('value', sa.String(length=255), nullable=True),
    sa.Column('language_id', sa.Uuid(), nullable=True),
    sa.Column('product_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['language_id'], ['languages.id'], name=op.f('fk_product_names_language_id_languages')),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], name=op.f('fk_product_names_product_id_products')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_product_names'))
    )
    op.create_table('product_texts',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('is_derived', sa.Boolean(), nullable=True),
    sa.Column('product_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], name=op.f('fk_product_texts_product_id_products')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_product_texts'))
    )
    op.create_table('target_groups',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('segmentation_id', sa.Integer(), nullable=True),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('from_date', sa.DateTime(), nullable=True),
    sa.Column('until_date', sa.DateTime(), nullable=True),
    sa.Column('product_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], name=op.f('fk_target_groups_product_id_products')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_target_groups'))
    )
    op.create_table('attribute_translations',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('language_id', sa.Uuid(), nullable=True),
    sa.Column('attribute_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['attribute_id'], ['attributes.id'], name=op.f('fk_attribute_translations_attribute_id_attributes')),
    sa.ForeignKeyConstraint(['language_id'], ['languages.id'], name=op.f('fk_attribute_translations_language_id_languages')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_attribute_translations'))
    )
    op.create_table('product_text_translations',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('label', sa.String(length=255), nullable=True),
    sa.Column('value', sa.String(length=1024), nullable=True),
    sa.Column('language_id', sa.Uuid(), nullable=True),
    sa.Column('product_text_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['language_id'], ['languages.id'], name=op.f('fk_product_text_translations_language_id_languages')),
    sa.ForeignKeyConstraint(['product_text_id'], ['product_texts.id'], name=op.f('fk_product_text_translations_product_text_id_product_texts')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_product_text_translations'))
    )
    op.create_table('target_group_association_table',
    sa.Column('products_id', sa.Uuid(), nullable=False),
    sa.Column('target_group_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['products_id'], ['products.id'], name=op.f('fk_target_group_association_table_products_id_products')),
    sa.ForeignKeyConstraint(['target_group_id'], ['target_groups.id'], name=op.f('fk_target_group_association_table_target_group_id_target_groups')),
    sa.PrimaryKeyConstraint('products_id', 'target_group_id', name=op.f('pk_target_group_association_table'))
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('target_group_association_table')
    op.drop_table('product_text_translations')
    op.drop_table('attribute_translations')
    op.drop_table('target_groups')
    op.drop_table('product_texts')
    op.drop_table('product_names')
    op.drop_table('product_language_association_table')
    op.drop_table('master_product_names')
    op.drop_table('attributes')
    op.drop_table('products')
    op.drop_table('hierarchy_level_translations')
    op.drop_table('classification_translations')
    op.drop_table('languages')
    op.drop_table('hierarchy_levels')
    op.drop_table('classifications')
    op.drop_table('brands')
    # ### end Alembic commands ###
