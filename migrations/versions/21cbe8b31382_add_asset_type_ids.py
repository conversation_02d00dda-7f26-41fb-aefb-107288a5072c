"""Add asset type ids

Revision ID: 21cbe8b31382
Revises: 0358b5446bac
Create Date: 2024-04-11 19:03:21.329921

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '21cbe8b31382'
down_revision: Union[str, None] = '0358b5446bac'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('assets', sa.Column('asset_type_id', sa.Integer(), nullable=True))
    op.add_column('assets', sa.Column('asset_sub_type_id', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('assets', 'asset_sub_type_id')
    op.drop_column('assets', 'asset_type_id')
    # ### end Alembic commands ###
